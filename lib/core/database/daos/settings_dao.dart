import 'package:drift/drift.dart';
import 'dart:convert';

import '../database.dart';
import '../tables/settings.dart';

part 'settings_dao.g.dart';

@DriftAccessor(tables: [Settings])
class SettingsDao extends DatabaseAccessor<AppDatabase> with _$SettingsDaoMixin {
  SettingsDao(super.db);

  // Get setting by key
  Future<SettingData?> getSetting(String key) async {
    return await (select(settings)..where((s) => s.key.equals(key)))
        .getSingleOrNull();
  }

  // Get all settings
  Future<List<SettingData>> getAllSettings() async {
    return await select(settings).get();
  }

  // Set string setting
  Future<void> setString(String key, String value) async {
    await into(settings).insertOnConflictUpdate(
      SettingsCompanion.insert(
        key: key,
        value: value,
        type: const Value('string'),
      ),
    );
  }

  // Get string setting
  Future<String?> getString(String key) async {
    final setting = await getSetting(key);
    return setting?.value;
  }

  // Set int setting
  Future<void> setInt(String key, int value) async {
    await into(settings).insertOnConflictUpdate(
      SettingsCompanion.insert(
        key: key,
        value: value.toString(),
        type: const Value('int'),
      ),
    );
  }

  // Get int setting
  Future<int?> getInt(String key) async {
    final setting = await getSetting(key);
    if (setting?.value != null && setting?.type == 'int') {
      return int.tryParse(setting!.value);
    }
    return null;
  }

  // Set double setting
  Future<void> setDouble(String key, double value) async {
    await into(settings).insertOnConflictUpdate(
      SettingsCompanion.insert(
        key: key,
        value: value.toString(),
        type: const Value('double'),
      ),
    );
  }

  // Get double setting
  Future<double?> getDouble(String key) async {
    final setting = await getSetting(key);
    if (setting?.value != null && setting?.type == 'double') {
      return double.tryParse(setting!.value);
    }
    return null;
  }

  // Set bool setting
  Future<void> setBool(String key, bool value) async {
    await into(settings).insertOnConflictUpdate(
      SettingsCompanion.insert(
        key: key,
        value: value.toString(),
        type: const Value('bool'),
      ),
    );
  }

  // Get bool setting
  Future<bool?> getBool(String key) async {
    final setting = await getSetting(key);
    if (setting?.value != null && setting?.type == 'bool') {
      return setting!.value.toLowerCase() == 'true';
    }
    return null;
  }

  // Set JSON setting
  Future<void> setJson(String key, Map<String, dynamic> value) async {
    await into(settings).insertOnConflictUpdate(
      SettingsCompanion.insert(
        key: key,
        value: jsonEncode(value),
        type: const Value('json'),
      ),
    );
  }

  // Get JSON setting
  Future<Map<String, dynamic>?> getJson(String key) async {
    final setting = await getSetting(key);
    if (setting?.value != null && setting?.type == 'json') {
      try {
        return jsonDecode(setting!.value) as Map<String, dynamic>;
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  // Delete setting
  Future<void> deleteSetting(String key) async {
    await (delete(settings)..where((s) => s.key.equals(key))).go();
  }

  // Clear all settings
  Future<void> clearAllSettings() async {
    await delete(settings).go();
  }

  // Get settings by prefix (useful for grouping)
  Future<List<SettingData>> getSettingsByPrefix(String prefix) async {
    return await (select(settings)..where((s) => s.key.like('$prefix%'))).get();
  }
}