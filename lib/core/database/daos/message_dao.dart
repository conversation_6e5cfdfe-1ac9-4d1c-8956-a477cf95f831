import 'package:drift/drift.dart';

import '../database.dart';
import '../tables/messages.dart';

part 'message_dao.g.dart';

@DriftAccessor(tables: [Messages])
class MessageDao extends DatabaseAccessor<AppDatabase> with _$MessageDaoMixin {
  MessageDao(super.db);

  // Get messages for a conversation with pagination
  Future<List<MessageData>> getMessages(
    String conversationId, {
    int? limit,
    int? offset,
  }) async {
    final query = select(messages)
      ..where((m) => m.conversationId.equals(conversationId))
      ..orderBy([(m) => OrderingTerm.asc(m.createdAt)]);

    if (limit != null) {
      query.limit(limit, offset: offset);
    }

    return await query.get();
  }

  // Get message by ID
  Future<MessageData?> getMessage(String id) async {
    return await (select(messages)..where((m) => m.id.equals(id)))
        .getSingleOrNull();
  }

  // Add new message
  Future<String> addMessage({
    required String id,
    required String conversationId,
    required String content,
    required MessageRole role,
    int tokenCount = 0,
    String? metadata,
    String? parentMessageId,
  }) async {
    await into(messages).insert(
      MessagesCompanion.insert(
        id: id,
        conversationId: conversationId,
        content: content,
        role: role,
        tokenCount: Value(tokenCount),
        metadata: Value(metadata),
        parentMessageId: Value(parentMessageId),
      ),
    );
    return id;
  }

  // Update message
  Future<bool> updateMessage(MessageData message) async {
    return await update(messages).replace(message);
  }

  // Update message content (for streaming)
  Future<void> updateMessageContent(String id, String content, {
    int? tokenCount,
    bool? isStreaming,
  }) async {
    await (update(messages)..where((m) => m.id.equals(id)))
        .write(MessagesCompanion(
          content: Value(content),
          tokenCount: tokenCount != null ? Value(tokenCount) : const Value.absent(),
          isStreaming: isStreaming != null ? Value(isStreaming) : const Value.absent(),
        ));
  }

  // Delete message
  Future<void> deleteMessage(String id) async {
    await (delete(messages)..where((m) => m.id.equals(id))).go();
  }

  // Get message count for conversation
  Future<int> getMessageCount(String conversationId) async {
    final query = selectOnly(messages)
      ..addColumns([messages.id.count()])
      ..where(messages.conversationId.equals(conversationId));
    
    final result = await query.getSingle();
    return result.read(messages.id.count()) ?? 0;
  }

  // Get total token count for conversation
  Future<int> getTotalTokenCount(String conversationId) async {
    final query = selectOnly(messages)
      ..addColumns([messages.tokenCount.sum()])
      ..where(messages.conversationId.equals(conversationId));
    
    final result = await query.getSingle();
    return result.read(messages.tokenCount.sum()) ?? 0;
  }

  // Get latest message in conversation
  Future<MessageData?> getLatestMessage(String conversationId) async {
    final query = select(messages)
      ..where((m) => m.conversationId.equals(conversationId))
      ..orderBy([(m) => OrderingTerm.desc(m.createdAt)])
      ..limit(1);

    return await query.getSingleOrNull();
  }

  // Delete all messages in conversation
  Future<void> deleteMessagesInConversation(String conversationId) async {
    await (delete(messages)..where((m) => m.conversationId.equals(conversationId))).go();
  }
}