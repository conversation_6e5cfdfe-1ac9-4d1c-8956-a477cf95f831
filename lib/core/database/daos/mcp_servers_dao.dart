import 'dart:convert';

import 'package:drift/drift.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../database.dart';
import '../../../shared/providers/database_provider.dart';
import '../tables/mcp_servers.dart';

part 'mcp_servers_dao.g.dart';

@DriftAccessor(tables: [McpServers])
class McpServersDao extends DatabaseAccessor<AppDatabase> with _$McpServersDaoMixin {
  McpServersDao(AppDatabase db) : super(db);

  // Get all MCP servers
  Future<List<McpServerData>> getAllServers() {
    return select(mcpServers).get();
  }

  // Get enabled MCP servers only
  Stream<List<McpServerData>> watchEnabledServers() {
    return (select(mcpServers)..where((tbl) => tbl.isEnabled.equals(true))).watch();
  }

  // Get server by ID
  Future<McpServerData?> getServerById(String id) {
    return (select(mcpServers)..where((tbl) => tbl.id.equals(id))).getSingleOrNull();
  }

  // Create new MCP server
  Future<McpServerData> createServer({
    required String id,
    required String name,
    required String url,
    required McpTransportType transportType,
    String? description,
    Map<String, String>? headers,
    Map<String, dynamic>? authConfig,
    Map<String, dynamic>? config,
  }) async {
    final serverData = McpServersCompanion.insert(
      id: id,
      name: name,
      url: url,
      transportType: Value(transportType),
      description: Value.absentIfNull(description),
      headers: Value.absentIfNull(headers != null ? jsonEncode(headers) : null),
      authConfig: Value.absentIfNull(authConfig != null ? jsonEncode(authConfig) : null),
      config: Value.absentIfNull(config != null ? jsonEncode(config) : null),
    );

    await into(mcpServers).insert(serverData);
    return (await getServerById(id))!;
  }

  // Update server status
  Future<void> updateServerStatus(String id, ServerStatus status, {String? errorMessage}) {
    return (update(mcpServers)..where((tbl) => tbl.id.equals(id))).write(
      McpServersCompanion(
        status: Value(status),
        errorMessage: Value.absentIfNull(errorMessage),
        lastConnected: status == ServerStatus.connected ? Value(DateTime.now()) : const Value.absent(),
      ),
    );
  }

  // Update server capabilities
  Future<void> updateServerCapabilities(String id, List<String> capabilities) {
    return (update(mcpServers)..where((tbl) => tbl.id.equals(id))).write(
      McpServersCompanion(
        capabilities: Value(jsonEncode(capabilities)),
      ),
    );
  }

  // Enable/disable server
  Future<void> toggleServerEnabled(String id, bool enabled) {
    return (update(mcpServers)..where((tbl) => tbl.id.equals(id))).write(
      McpServersCompanion(isEnabled: Value(enabled)),
    );
  }

  // Delete server
  Future<void> deleteServer(String id) {
    return (delete(mcpServers)..where((tbl) => tbl.id.equals(id))).go();
  }

  // Get servers by status
  Stream<List<McpServerData>> watchServersByStatus(ServerStatus status) {
    return (select(mcpServers)..where((tbl) => tbl.status.equals(status.name))).watch();
  }
}

@riverpod
McpServersDao mcpServersDao(ref) {
  final database = ref.watch(databaseProvider);
  return McpServersDao(database);
}