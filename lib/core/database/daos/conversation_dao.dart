import 'package:drift/drift.dart';

import '../database.dart';
import '../tables/conversations.dart';
import '../tables/messages.dart';

part 'conversation_dao.g.dart';

@DriftAccessor(tables: [Conversations, Messages])
class ConversationDao extends DatabaseAccessor<AppDatabase> 
    with _$ConversationDaoMixin {
  ConversationDao(super.db);

  // Get all conversations with message count and last message preview
  Future<List<ConversationWithStats>> getConversationsWithStats() async {
    final query = select(conversations).join([
      leftOuterJoin(
        messages,
        messages.conversationId.equalsExp(conversations.id),
      ),
    ]);

    query.orderBy([
      OrderingTerm(expression: conversations.updatedAt, mode: OrderingMode.desc)
    ]);

    final results = await query.get();
    final conversationMap = <String, ConversationWithStats>{};

    for (final row in results) {
      final conversation = row.readTable(conversations);
      final message = row.readTableOrNull(messages);

      if (!conversationMap.containsKey(conversation.id)) {
        conversationMap[conversation.id] = ConversationWithStats(
          conversation: conversation,
          messageCount: 0,
          lastMessage: null,
        );
      }

      final stats = conversationMap[conversation.id]!;
      if (message != null) {
        stats.messageCount++;
        if (stats.lastMessage == null || 
            message.createdAt.isAfter(stats.lastMessage!.createdAt)) {
          stats.lastMessage = message;
        }
      }
    }

    return conversationMap.values.toList();
  }

  // Get conversation by ID
  Future<ConversationData?> getConversation(String id) async {
    return await (select(conversations)..where((c) => c.id.equals(id)))
        .getSingleOrNull();
  }

  // Create new conversation
  Future<String> createConversation({
    required String id,
    required String model,
    String? title,
  }) async {
    await into(conversations).insert(
      ConversationsCompanion.insert(
        id: id,
        model: model,
        title: Value(title),
      ),
    );
    return id;
  }

  // Update conversation
  Future<bool> updateConversation(ConversationData conversation) async {
    return await update(conversations).replace(conversation);
  }

  // Delete conversation and all its messages
  Future<void> deleteConversation(String id) async {
    await transaction(() async {
      // Delete all messages first
      await (delete(messages)..where((m) => m.conversationId.equals(id))).go();
      // Then delete the conversation
      await (delete(conversations)..where((c) => c.id.equals(id))).go();
    });
  }

  // Update conversation's updated_at timestamp
  Future<void> touchConversation(String id) async {
    await (update(conversations)..where((c) => c.id.equals(id)))
        .write(ConversationsCompanion(
          updatedAt: Value(DateTime.now()),
        ));
  }

  // Pin/unpin conversation
  Future<void> togglePin(String id) async {
    final conversation = await getConversation(id);
    if (conversation != null) {
      await (update(conversations)..where((c) => c.id.equals(id)))
          .write(ConversationsCompanion(
            isPinned: Value(!conversation.isPinned),
          ));
    }
  }
}

class ConversationWithStats {
  final ConversationData conversation;
  int messageCount;
  MessageData? lastMessage;

  ConversationWithStats({
    required this.conversation,
    required this.messageCount,
    this.lastMessage,
  });
}