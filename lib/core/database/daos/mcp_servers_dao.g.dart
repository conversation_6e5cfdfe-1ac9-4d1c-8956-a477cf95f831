// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'mcp_servers_dao.dart';

// ignore_for_file: type=lint
mixin _$McpServersDaoMixin on DatabaseAccessor<AppDatabase> {
  $McpServersTable get mcpServers => attachedDatabase.mcpServers;
}

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

@ProviderFor(mcpServersDao)
const mcpServersDaoProvider = McpServersDaoProvider._();

final class McpServersDaoProvider
    extends $FunctionalProvider<McpServersDao, McpServersDao, McpServersDao>
    with $Provider<McpServersDao> {
  const McpServersDaoProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'mcpServersDaoProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$mcpServersDaoHash();

  @$internal
  @override
  $ProviderElement<McpServersDao> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  McpServersDao create(Ref ref) {
    return mcpServersDao(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(McpServersDao value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<McpServersDao>(value),
    );
  }
}

String _$mcpServersDaoHash() => r'c87ca314871507692188d3c6e71c5ea45d71eb1c';

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
