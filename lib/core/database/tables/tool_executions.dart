import 'package:drift/drift.dart';

enum ExecutionStatus { pending, running, completed, failed, cancelled }

@DataClassName('ToolExecutionData')
class ToolExecutions extends Table {
  TextColumn get id => text()();
  TextColumn get serverId => text()();
  TextColumn get toolName => text()();
  TextColumn get parameters => text()(); // JSON
  TextColumn get result => text().nullable()(); // JSON
  TextColumn get status => textEnum<ExecutionStatus>().withDefault(const Constant('pending'))();
  DateTimeColumn get startedAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get completedAt => dateTime().nullable()();
  IntColumn get executionTimeMs => integer().nullable()();
  TextColumn get errorMessage => text().nullable()();
  TextColumn get conversationId => text().nullable()(); // Link to conversation if part of chat
  TextColumn get messageId => text().nullable()(); // Link to message if part of chat

  @override
  Set<Column> get primaryKey => {id};
}