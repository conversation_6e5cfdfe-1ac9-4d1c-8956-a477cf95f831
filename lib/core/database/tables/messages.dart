import 'package:drift/drift.dart';

enum MessageRole { user, assistant, system, tool }

@DataClassName('MessageData')
class Messages extends Table {
  TextColumn get id => text()();
  TextColumn get conversationId => text()();
  TextColumn get content => text()();
  TextColumn get role => textEnum<MessageRole>()();
  IntColumn get tokenCount => integer().withDefault(const Constant(0))();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  TextColumn get metadata => text().nullable()(); // JSON for tool calls, etc.
  BoolColumn get isStreaming => boolean().withDefault(const Constant(false))();
  TextColumn get parentMessageId => text().nullable()();

  @override
  Set<Column> get primaryKey => {id};
}