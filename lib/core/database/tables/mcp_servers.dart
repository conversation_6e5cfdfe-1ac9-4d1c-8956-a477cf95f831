import 'package:drift/drift.dart';

enum ServerStatus { disconnected, connecting, connected, error }

@DataClassName('McpServerData')
class McpServers extends Table {
  TextColumn get id => text()();
  TextColumn get name => text()();
  TextColumn get url => text()();
  TextColumn get type => text()(); // websocket, http
  TextColumn get status => textEnum<ServerStatus>().withDefault(const Constant('disconnected'))();
  TextColumn get capabilities => text().nullable()(); // JSON array of capabilities
  DateTimeColumn get lastConnected => dateTime().nullable()();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  TextColumn get config => text().nullable()(); // JSON config
  TextColumn get errorMessage => text().nullable()();

  @override
  Set<Column> get primaryKey => {id};
}