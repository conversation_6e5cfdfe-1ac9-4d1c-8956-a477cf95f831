import 'package:drift/drift.dart';

enum ServerStatus { disconnected, connecting, connected, error }
enum McpTransportType { websocket, http, sse }

@DataClassName('McpServerData')
class McpServers extends Table {
  TextColumn get id => text()();
  TextColumn get name => text()();
  TextColumn get url => text()();
  TextColumn get transportType => textEnum<McpTransportType>().withDefault(const Constant('websocket'))();
  TextColumn get status => textEnum<ServerStatus>().withDefault(const Constant('disconnected'))();
  BoolColumn get isEnabled => boolean().withDefault(const Constant(true))();
  TextColumn get description => text().nullable()();
  TextColumn get capabilities => text().nullable()(); // JSON array of capabilities
  TextColumn get headers => text().nullable()(); // JSON string for HTTP headers
  TextColumn get authConfig => text().nullable()(); // JSON string for auth configuration
  DateTimeColumn get lastConnected => dateTime().nullable()();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  TextColumn get config => text().nullable()(); // JSON config
  TextColumn get errorMessage => text().nullable()();

  @override
  Set<Column> get primaryKey => {id};
}