import 'package:drift/drift.dart';

@DataClassName('ConversationData')
class Conversations extends Table {
  TextColumn get id => text()();
  TextColumn get title => text().nullable()();
  TextColumn get model => text()();
  TextColumn get lastUsedModel => text().nullable()();
  IntColumn get tokenCount => integer().withDefault(const Constant(0))();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
  BoolColumn get isPinned => boolean().withDefault(const Constant(false))();
  TextColumn get folderId => text().nullable()();

  @override
  Set<Column> get primaryKey => {id};
}