import 'dart:io';

import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;

import 'tables/conversations.dart';
import 'tables/messages.dart';
import 'tables/settings.dart';
import 'tables/mcp_servers.dart';
import 'tables/tool_executions.dart';

part 'database.g.dart';

@DriftDatabase(tables: [
  Conversations,
  Messages,
  Settings,
  McpServers,
  ToolExecutions,
])
class AppDatabase extends _$AppDatabase {
  AppDatabase() : super(_openConnection());

  @override
  int get schemaVersion => 2;

  @override
  MigrationStrategy get migration => MigrationStrategy(
    onCreate: (Migrator m) async {
      await m.createAll();
      
      // Create indices for better performance
      await customStatement('''
        CREATE INDEX IF NOT EXISTS idx_messages_conversation_id 
        ON messages(conversation_id);
      ''');
      
      await customStatement('''
        CREATE INDEX IF NOT EXISTS idx_messages_created_at 
        ON messages(created_at);
      ''');
      
      await customStatement('''
        CREATE INDEX IF NOT EXISTS idx_conversations_updated_at 
        ON conversations(updated_at DESC);
      ''');
      
      await customStatement('''
        CREATE INDEX IF NOT EXISTS idx_tool_executions_server_id 
        ON tool_executions(server_id);
      ''');

      // Enable full-text search on messages
      await customStatement('''
        CREATE VIRTUAL TABLE IF NOT EXISTS messages_fts USING fts5(
          id, content, content=messages, content_rowid=rowid
        );
      ''');

      await customStatement('''
        CREATE TRIGGER IF NOT EXISTS messages_fts_insert AFTER INSERT ON messages BEGIN
          INSERT INTO messages_fts(rowid, id, content) VALUES (new.rowid, new.id, new.content);
        END;
      ''');

      await customStatement('''
        CREATE TRIGGER IF NOT EXISTS messages_fts_delete AFTER DELETE ON messages BEGIN
          INSERT INTO messages_fts(messages_fts, rowid, id, content) VALUES ('delete', old.rowid, old.id, old.content);
        END;
      ''');

      await customStatement('''
        CREATE TRIGGER IF NOT EXISTS messages_fts_update AFTER UPDATE ON messages BEGIN
          INSERT INTO messages_fts(messages_fts, rowid, id, content) VALUES ('delete', old.rowid, old.id, old.content);
          INSERT INTO messages_fts(rowid, id, content) VALUES (new.rowid, new.id, new.content);
        END;
      ''');
    },
    onUpgrade: (Migrator m, int from, int to) async {
      // Migration from version 1 to 2: Add lastUsedModel column
      if (from < 2) {
        await customStatement('''
          ALTER TABLE conversations ADD COLUMN last_used_model TEXT;
        ''');
      }
    },
  );

  // Search messages using FTS
  Future<List<MessageData>> searchMessages(String query, {int? limit}) async {
    final result = await customSelect('''
      SELECT m.* FROM messages m 
      JOIN messages_fts fts ON m.rowid = fts.rowid 
      WHERE messages_fts MATCH ? 
      ORDER BY rank 
      ${limit != null ? 'LIMIT ?' : ''}
    ''', variables: [
      Variable.withString(query),
      if (limit != null) Variable.withInt(limit),
    ]).get();

    return result.map((row) => MessageData.fromJson(row.data)).toList();
  }
}

LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, 'otlo_chat.db'));
    return NativeDatabase(file);
  });
}