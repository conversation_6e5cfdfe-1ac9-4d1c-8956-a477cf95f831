import 'dart:async';

import 'protocol/mcp_protocol.dart';
import 'protocol/mcp_types.dart';
import 'transport/websocket_transport.dart';
import 'transport/http_transport.dart';
import '../database/tables/mcp_servers.dart';

class McpClient {
  late final McpProtocol _protocol;
  McpWebSocketTransport? _wsTransport;
  McpHttpTransport? _httpTransport;
  
  final McpTransportType _transportType;
  final String _url;
  final Map<String, String>? _headers;

  StreamSubscription<String>? _messageSubscription;
  StreamSubscription<McpProtocolEvent>? _eventSubscription;

  final StreamController<McpConnectionEvent> _connectionController = StreamController<McpConnectionEvent>.broadcast();
  final StreamController<McpToolsListResult> _toolsController = StreamController<McpToolsListResult>.broadcast();

  Stream<McpConnectionEvent> get connectionEvents => _connectionController.stream;
  Stream<McpToolsListResult> get toolsUpdates => _toolsController.stream;

  bool get isConnected => _protocol.isInitialized;
  McpCapabilities? get serverCapabilities => _protocol.serverCapabilities;
  McpServerInfo? get serverInfo => _protocol.serverInfo;

  McpClient({
    required McpTransportType transportType,
    required String url,
    Map<String, String>? headers,
  }) : _transportType = transportType,
       _url = url,
       _headers = headers,
       _protocol = McpProtocol();

  Future<void> connect() async {
    try {
      // Initialize transport
      if (_transportType == McpTransportType.websocket) {
        _wsTransport = McpWebSocketTransport(url: _url);
        await _wsTransport!.connect();
        
        _messageSubscription = _wsTransport!.messages.listen(
          (message) => _protocol.handleMessage(message),
          onError: (error) => _connectionController.add(McpConnectionErrorEvent(error.toString())),
        );
      } else {
        _httpTransport = McpHttpTransport(
          baseUrl: _url,
          headers: _headers,
        );
      }

      // Listen to protocol events
      _eventSubscription = _protocol.events.listen(_handleProtocolEvent);

      // Initialize MCP protocol
      await _initializeProtocol();

      _connectionController.add(McpConnectedEvent());
    } catch (e) {
      _connectionController.add(McpConnectionErrorEvent(e.toString()));
      rethrow;
    }
  }

  Future<void> _initializeProtocol() async {
    final capabilities = McpCapabilities(
      tools: McpToolsCapability(),
      resources: McpResourcesCapability(),
      prompts: McpPromptsCapability(),
    );

    final clientInfo = McpClientInfo(
      name: 'Otlo Chat',
      version: '1.0.0',
    );

    await _protocol.initialize(
      capabilities: capabilities,
      clientInfo: clientInfo,
    );

    await _protocol.initialized();
  }

  void _handleProtocolEvent(McpProtocolEvent event) {
    if (event is McpInitializedEvent) {
      final initEvent = event as McpInitializedEvent;
      _connectionController.add(McpInitializedEvent(initEvent.result));
    } else if (event is McpToolsListChangedEvent) {
      _refreshTools();
    } else if (event is McpResourcesListChangedEvent) {
      // Handle resources list changed
    } else if (event is McpPromptsListChangedEvent) {
      // Handle prompts list changed
    } else if (event is McpErrorEvent) {
      final errorEvent = event as McpErrorEvent;
      _connectionController.add(McpConnectionErrorEvent(errorEvent.message));
    }
  }

  Future<void> _refreshTools() async {
    try {
      final toolsList = await listTools();
      _toolsController.add(toolsList);
    } catch (e) {
      print('Failed to refresh tools: $e');
    }
  }

  // Tool operations
  Future<McpToolsListResult> listTools({String? cursor}) async {
    return await _protocol.listTools(cursor: cursor);
  }

  Future<McpToolCallResult> callTool({
    required String name,
    Map<String, dynamic>? arguments,
  }) async {
    return await _protocol.callTool(name: name, arguments: arguments);
  }

  // Resource operations
  Future<McpResourcesListResult> listResources({String? cursor}) async {
    return await _protocol.listResources(cursor: cursor);
  }

  // Prompt operations
  Future<McpPromptsListResult> listPrompts({String? cursor}) async {
    return await _protocol.listPrompts(cursor: cursor);
  }

  // HTTP-specific method for synchronous calls
  Future<String> _sendHttpMessage(String message) async {
    if (_httpTransport == null) {
      throw StateError('HTTP transport not initialized');
    }
    return await _httpTransport!.sendMessage(message);
  }

  Future<void> disconnect() async {
    _messageSubscription?.cancel();
    _eventSubscription?.cancel();
    
    if (_wsTransport != null) {
      await _wsTransport!.disconnect();
      _wsTransport = null;
    }
    
    if (_httpTransport != null) {
      _httpTransport!.dispose();
      _httpTransport = null;
    }

    _protocol.dispose();
    _connectionController.add(McpDisconnectedEvent());
  }

  void dispose() {
    disconnect();
    _connectionController.close();
    _toolsController.close();
  }
}

// Connection events
abstract class McpConnectionEvent {}

class McpConnectedEvent extends McpConnectionEvent {}

class McpDisconnectedEvent extends McpConnectionEvent {}

class McpInitializedEvent extends McpConnectionEvent {
  final McpInitializeResult result;
  
  McpInitializedEvent(this.result);
}

class McpConnectionErrorEvent extends McpConnectionEvent {
  final String error;
  
  McpConnectionErrorEvent(this.error);
}