import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'mcp_types.dart';

class JsonRpcClient {
  final Map<String, Completer<dynamic>> _pendingRequests = {};
  final StreamController<JsonRpcRequest> _requestController = StreamController<JsonRpcRequest>.broadcast();
  final StreamController<JsonRpcResponse> _responseController = StreamController<JsonRpcResponse>.broadcast();
  final Random _random = Random();

  Stream<JsonRpcRequest> get requests => _requestController.stream;
  Stream<JsonRpcResponse> get responses => _responseController.stream;

  String _generateId() {
    return _random.nextInt(1000000).toString();
  }

  // Send a request and wait for response
  Future<T> sendRequest<T>(String method, [Object? params]) async {
    final id = _generateId();
    final completer = Completer<T>();
    
    _pendingRequests[id] = completer;

    final request = JsonRpcRequest(
      jsonrpc: '2.0',
      method: method,
      params: params,
      id: id,
    );

    _requestController.add(request);

    // Set timeout for request
    Timer(const Duration(seconds: 30), () {
      if (_pendingRequests.containsKey(id)) {
        _pendingRequests.remove(id);
        completer.completeError(TimeoutException(
          'Request timeout for method: $method',
          const Duration(seconds: 30),
        ));
      }
    });

    return completer.future;
  }

  // Send a notification (no response expected)
  void sendNotification(String method, [Object? params]) {
    final request = JsonRpcRequest(
      jsonrpc: '2.0',
      method: method,
      params: params,
    );

    _requestController.add(request);
  }

  // Handle incoming response
  void handleResponse(JsonRpcResponse response) {
    _responseController.add(response);

    if (response.id != null) {
      final completer = _pendingRequests.remove(response.id);
      if (completer != null) {
        if (response.error != null) {
          completer.completeError(JsonRpcException(response.error!));
        } else {
          completer.complete(response.result);
        }
      }
    }
  }

  // Handle incoming request (for server functionality)
  void handleRequest(JsonRpcRequest request) {
    _requestController.add(request);
  }

  // Send response to a request
  void sendResponse(String? id, {Object? result, JsonRpcError? error}) {
    final response = JsonRpcResponse(
      jsonrpc: '2.0',
      result: result,
      error: error,
      id: id,
    );

    _responseController.add(response);
  }

  // Send error response
  void sendError(String? id, int code, String message, [Object? data]) {
    sendResponse(
      id,
      error: JsonRpcError(
        code: code,
        message: message,
        data: data,
      ),
    );
  }

  // Parse JSON message
  dynamic parseMessage(String json) {
    try {
      final data = jsonDecode(json) as Map<String, dynamic>;
      
      // Check if it's a response (has result or error)
      if (data.containsKey('result') || data.containsKey('error')) {
        return JsonRpcResponse.fromJson(data);
      } else {
        // It's a request or notification
        return JsonRpcRequest.fromJson(data);
      }
    } catch (e) {
      throw JsonRpcParseException('Failed to parse JSON-RPC message: $e');
    }
  }

  // Serialize message to JSON
  String serializeMessage(dynamic message) {
    try {
      if (message is JsonRpcRequest) {
        return jsonEncode(message.toJson());
      } else if (message is JsonRpcResponse) {
        return jsonEncode(message.toJson());
      } else {
        throw ArgumentError('Invalid message type: ${message.runtimeType}');
      }
    } catch (e) {
      throw JsonRpcSerializationException('Failed to serialize message: $e');
    }
  }

  void dispose() {
    _requestController.close();
    _responseController.close();
    
    // Complete all pending requests with error
    for (final completer in _pendingRequests.values) {
      if (!completer.isCompleted) {
        completer.completeError(const JsonRpcException(
          JsonRpcError(
            code: -32000,
            message: 'Client disposed',
          ),
        ));
      }
    }
    _pendingRequests.clear();
  }
}

// JSON-RPC error codes
class JsonRpcErrorCodes {
  static const int parseError = -32700;
  static const int invalidRequest = -32600;
  static const int methodNotFound = -32601;
  static const int invalidParams = -32602;
  static const int internalError = -32603;
  
  // Application-specific error codes
  static const int serverNotInitialized = -32002;
  static const int unknownErrorCode = -32001;
}

// Exceptions
class JsonRpcException implements Exception {
  final JsonRpcError error;

  const JsonRpcException(this.error);

  @override
  String toString() => 'JsonRpcException: ${error.message} (${error.code})';
}

class JsonRpcParseException implements Exception {
  final String message;

  const JsonRpcParseException(this.message);

  @override
  String toString() => 'JsonRpcParseException: $message';
}

class JsonRpcSerializationException implements Exception {
  final String message;

  const JsonRpcSerializationException(this.message);

  @override
  String toString() => 'JsonRpcSerializationException: $message';
}