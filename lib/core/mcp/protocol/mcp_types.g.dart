// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'mcp_types.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_JsonRpcRequest _$JsonRpcRequestFromJson(Map<String, dynamic> json) =>
    _JsonRpcRequest(
      jsonrpc: json['jsonrpc'] as String,
      method: json['method'] as String,
      params: json['params'],
      id: json['id'] as String?,
    );

Map<String, dynamic> _$JsonRpcRequestToJson(_JsonRpcRequest instance) =>
    <String, dynamic>{
      'jsonrpc': instance.jsonrpc,
      'method': instance.method,
      'params': ?instance.params,
      'id': ?instance.id,
    };

_JsonRpcResponse _$JsonRpcResponseFromJson(Map<String, dynamic> json) =>
    _JsonRpcResponse(
      jsonrpc: json['jsonrpc'] as String,
      result: json['result'],
      error: json['error'] == null
          ? null
          : JsonRpcError.fromJson(json['error'] as Map<String, dynamic>),
      id: json['id'] as String?,
    );

Map<String, dynamic> _$JsonRpcResponseToJson(_JsonRpcResponse instance) =>
    <String, dynamic>{
      'jsonrpc': instance.jsonrpc,
      'result': ?instance.result,
      'error': ?instance.error,
      'id': ?instance.id,
    };

_JsonRpcError _$JsonRpcErrorFromJson(Map<String, dynamic> json) =>
    _JsonRpcError(
      code: (json['code'] as num).toInt(),
      message: json['message'] as String,
      data: json['data'],
    );

Map<String, dynamic> _$JsonRpcErrorToJson(_JsonRpcError instance) =>
    <String, dynamic>{
      'code': instance.code,
      'message': instance.message,
      'data': ?instance.data,
    };

_McpCapabilities _$McpCapabilitiesFromJson(
  Map<String, dynamic> json,
) => _McpCapabilities(
  tools: json['tools'] == null
      ? null
      : McpToolsCapability.fromJson(json['tools'] as Map<String, dynamic>),
  resources: json['resources'] == null
      ? null
      : McpResourcesCapability.fromJson(
          json['resources'] as Map<String, dynamic>,
        ),
  prompts: json['prompts'] == null
      ? null
      : McpPromptsCapability.fromJson(json['prompts'] as Map<String, dynamic>),
  logging: json['logging'] == null
      ? null
      : McpLoggingCapability.fromJson(json['logging'] as Map<String, dynamic>),
);

Map<String, dynamic> _$McpCapabilitiesToJson(_McpCapabilities instance) =>
    <String, dynamic>{
      'tools': instance.tools,
      'resources': instance.resources,
      'prompts': instance.prompts,
      'logging': instance.logging,
    };

_McpToolsCapability _$McpToolsCapabilityFromJson(Map<String, dynamic> json) =>
    _McpToolsCapability(listChanged: json['listChanged'] as bool? ?? false);

Map<String, dynamic> _$McpToolsCapabilityToJson(_McpToolsCapability instance) =>
    <String, dynamic>{'listChanged': instance.listChanged};

_McpResourcesCapability _$McpResourcesCapabilityFromJson(
  Map<String, dynamic> json,
) => _McpResourcesCapability(
  subscribe: json['subscribe'] as bool? ?? false,
  listChanged: json['listChanged'] as bool? ?? false,
);

Map<String, dynamic> _$McpResourcesCapabilityToJson(
  _McpResourcesCapability instance,
) => <String, dynamic>{
  'subscribe': instance.subscribe,
  'listChanged': instance.listChanged,
};

_McpPromptsCapability _$McpPromptsCapabilityFromJson(
  Map<String, dynamic> json,
) => _McpPromptsCapability(listChanged: json['listChanged'] as bool? ?? false);

Map<String, dynamic> _$McpPromptsCapabilityToJson(
  _McpPromptsCapability instance,
) => <String, dynamic>{'listChanged': instance.listChanged};

_McpLoggingCapability _$McpLoggingCapabilityFromJson(
  Map<String, dynamic> json,
) => _McpLoggingCapability();

Map<String, dynamic> _$McpLoggingCapabilityToJson(
  _McpLoggingCapability instance,
) => <String, dynamic>{};

_McpClientInfo _$McpClientInfoFromJson(Map<String, dynamic> json) =>
    _McpClientInfo(
      name: json['name'] as String,
      version: json['version'] as String,
    );

Map<String, dynamic> _$McpClientInfoToJson(_McpClientInfo instance) =>
    <String, dynamic>{'name': instance.name, 'version': instance.version};

_McpServerInfo _$McpServerInfoFromJson(Map<String, dynamic> json) =>
    _McpServerInfo(
      name: json['name'] as String,
      version: json['version'] as String,
    );

Map<String, dynamic> _$McpServerInfoToJson(_McpServerInfo instance) =>
    <String, dynamic>{'name': instance.name, 'version': instance.version};

_McpInitializeParams _$McpInitializeParamsFromJson(Map<String, dynamic> json) =>
    _McpInitializeParams(
      protocolVersion: json['protocolVersion'] as String,
      capabilities: McpCapabilities.fromJson(
        json['capabilities'] as Map<String, dynamic>,
      ),
      clientInfo: McpClientInfo.fromJson(
        json['clientInfo'] as Map<String, dynamic>,
      ),
    );

Map<String, dynamic> _$McpInitializeParamsToJson(
  _McpInitializeParams instance,
) => <String, dynamic>{
  'protocolVersion': instance.protocolVersion,
  'capabilities': instance.capabilities,
  'clientInfo': instance.clientInfo,
};

_McpInitializeResult _$McpInitializeResultFromJson(Map<String, dynamic> json) =>
    _McpInitializeResult(
      protocolVersion: json['protocolVersion'] as String,
      capabilities: McpCapabilities.fromJson(
        json['capabilities'] as Map<String, dynamic>,
      ),
      serverInfo: McpServerInfo.fromJson(
        json['serverInfo'] as Map<String, dynamic>,
      ),
    );

Map<String, dynamic> _$McpInitializeResultToJson(
  _McpInitializeResult instance,
) => <String, dynamic>{
  'protocolVersion': instance.protocolVersion,
  'capabilities': instance.capabilities,
  'serverInfo': instance.serverInfo,
};

_McpTool _$McpToolFromJson(Map<String, dynamic> json) => _McpTool(
  name: json['name'] as String,
  description: json['description'] as String?,
  inputSchema: json['inputSchema'] as Map<String, dynamic>,
);

Map<String, dynamic> _$McpToolToJson(_McpTool instance) => <String, dynamic>{
  'name': instance.name,
  'description': instance.description,
  'inputSchema': instance.inputSchema,
};

_McpToolsListParams _$McpToolsListParamsFromJson(Map<String, dynamic> json) =>
    _McpToolsListParams(cursor: json['cursor'] as String?);

Map<String, dynamic> _$McpToolsListParamsToJson(_McpToolsListParams instance) =>
    <String, dynamic>{'cursor': instance.cursor};

_McpToolsListResult _$McpToolsListResultFromJson(Map<String, dynamic> json) =>
    _McpToolsListResult(
      tools: (json['tools'] as List<dynamic>)
          .map((e) => McpTool.fromJson(e as Map<String, dynamic>))
          .toList(),
      nextCursor: json['nextCursor'] as String?,
    );

Map<String, dynamic> _$McpToolsListResultToJson(_McpToolsListResult instance) =>
    <String, dynamic>{
      'tools': instance.tools,
      'nextCursor': instance.nextCursor,
    };

_McpToolCallParams _$McpToolCallParamsFromJson(Map<String, dynamic> json) =>
    _McpToolCallParams(
      name: json['name'] as String,
      arguments: json['arguments'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$McpToolCallParamsToJson(_McpToolCallParams instance) =>
    <String, dynamic>{'name': instance.name, 'arguments': instance.arguments};

_McpToolCallResult _$McpToolCallResultFromJson(Map<String, dynamic> json) =>
    _McpToolCallResult(
      content: (json['content'] as List<dynamic>?)
          ?.map((e) => McpTextContent.fromJson(e as Map<String, dynamic>))
          .toList(),
      isError: json['isError'] as bool? ?? false,
    );

Map<String, dynamic> _$McpToolCallResultToJson(_McpToolCallResult instance) =>
    <String, dynamic>{
      'content': ?instance.content,
      'isError': instance.isError,
    };

_McpTextContent _$McpTextContentFromJson(Map<String, dynamic> json) =>
    _McpTextContent(type: json['type'] as String, text: json['text'] as String);

Map<String, dynamic> _$McpTextContentToJson(_McpTextContent instance) =>
    <String, dynamic>{'type': instance.type, 'text': instance.text};

_McpResource _$McpResourceFromJson(Map<String, dynamic> json) => _McpResource(
  uri: json['uri'] as String,
  name: json['name'] as String,
  description: json['description'] as String?,
  mimeType: json['mimeType'] as String?,
);

Map<String, dynamic> _$McpResourceToJson(_McpResource instance) =>
    <String, dynamic>{
      'uri': instance.uri,
      'name': instance.name,
      'description': instance.description,
      'mimeType': instance.mimeType,
    };

_McpResourcesListParams _$McpResourcesListParamsFromJson(
  Map<String, dynamic> json,
) => _McpResourcesListParams(cursor: json['cursor'] as String?);

Map<String, dynamic> _$McpResourcesListParamsToJson(
  _McpResourcesListParams instance,
) => <String, dynamic>{'cursor': instance.cursor};

_McpResourcesListResult _$McpResourcesListResultFromJson(
  Map<String, dynamic> json,
) => _McpResourcesListResult(
  resources: (json['resources'] as List<dynamic>)
      .map((e) => McpResource.fromJson(e as Map<String, dynamic>))
      .toList(),
  nextCursor: json['nextCursor'] as String?,
);

Map<String, dynamic> _$McpResourcesListResultToJson(
  _McpResourcesListResult instance,
) => <String, dynamic>{
  'resources': instance.resources,
  'nextCursor': instance.nextCursor,
};

_McpPrompt _$McpPromptFromJson(Map<String, dynamic> json) => _McpPrompt(
  name: json['name'] as String,
  description: json['description'] as String?,
  arguments: (json['arguments'] as List<dynamic>?)
      ?.map((e) => McpPromptArgument.fromJson(e as Map<String, dynamic>))
      .toList(),
);

Map<String, dynamic> _$McpPromptToJson(_McpPrompt instance) =>
    <String, dynamic>{
      'name': instance.name,
      'description': instance.description,
      'arguments': instance.arguments,
    };

_McpPromptArgument _$McpPromptArgumentFromJson(Map<String, dynamic> json) =>
    _McpPromptArgument(
      name: json['name'] as String,
      description: json['description'] as String?,
      required: json['required'] as bool? ?? false,
    );

Map<String, dynamic> _$McpPromptArgumentToJson(_McpPromptArgument instance) =>
    <String, dynamic>{
      'name': instance.name,
      'description': instance.description,
      'required': instance.required,
    };

_McpPromptsListResult _$McpPromptsListResultFromJson(
  Map<String, dynamic> json,
) => _McpPromptsListResult(
  prompts: (json['prompts'] as List<dynamic>)
      .map((e) => McpPrompt.fromJson(e as Map<String, dynamic>))
      .toList(),
  nextCursor: json['nextCursor'] as String?,
);

Map<String, dynamic> _$McpPromptsListResultToJson(
  _McpPromptsListResult instance,
) => <String, dynamic>{
  'prompts': instance.prompts,
  'nextCursor': instance.nextCursor,
};
