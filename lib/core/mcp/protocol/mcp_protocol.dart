import 'dart:async';

import 'json_rpc.dart';
import 'mcp_types.dart';

class McpProtocol {
  final JsonRpcClient _jsonRpc = JsonRpcClient();
  final StreamController<McpProtocolEvent> _eventController = StreamController<McpProtocolEvent>.broadcast();
  
  bool _isInitialized = false;
  McpCapabilities? _serverCapabilities;
  McpServerInfo? _serverInfo;

  Stream<McpProtocolEvent> get events => _eventController.stream;
  bool get isInitialized => _isInitialized;
  McpCapabilities? get serverCapabilities => _serverCapabilities;
  McpServerInfo? get serverInfo => _serverInfo;

  // Protocol lifecycle
  Future<McpInitializeResult> initialize({
    required McpCapabilities capabilities,
    required McpClientInfo clientInfo,
  }) async {
    if (_isInitialized) {
      throw McpProtocolException('Already initialized');
    }

    try {
      final params = McpInitializeParams(
        protocolVersion: '2024-11-05',
        capabilities: capabilities,
        clientInfo: clientInfo,
      );

      final result = await _jsonRpc.sendRequest<Map<String, dynamic>>(
        'initialize',
        params.toJson(),
      );

      final initResult = McpInitializeResult.fromJson(result);
      
      _serverCapabilities = initResult.capabilities;
      _serverInfo = initResult.serverInfo;
      _isInitialized = true;

      _eventController.add(McpInitializedEvent(initResult));

      return initResult;
    } catch (e) {
      throw McpProtocolException('Failed to initialize: $e');
    }
  }

  Future<void> initialized() async {
    if (!_isInitialized) {
      throw McpProtocolException('Not initialized');
    }

    _jsonRpc.sendNotification('notifications/initialized');
  }

  // Tools
  Future<McpToolsListResult> listTools({String? cursor}) async {
    _ensureInitialized();
    _ensureCapability('tools');

    try {
      final params = cursor != null ? McpToolsListParams(cursor: cursor) : null;
      final result = await _jsonRpc.sendRequest<Map<String, dynamic>>(
        'tools/list',
        params?.toJson(),
      );

      return McpToolsListResult.fromJson(result);
    } catch (e) {
      throw McpProtocolException('Failed to list tools: $e');
    }
  }

  Future<McpToolCallResult> callTool({
    required String name,
    Map<String, dynamic>? arguments,
  }) async {
    _ensureInitialized();
    _ensureCapability('tools');

    try {
      final params = McpToolCallParams(name: name, arguments: arguments);
      final result = await _jsonRpc.sendRequest<Map<String, dynamic>>(
        'tools/call',
        params.toJson(),
      );

      return McpToolCallResult.fromJson(result);
    } catch (e) {
      throw McpProtocolException('Failed to call tool: $e');
    }
  }

  // Resources
  Future<McpResourcesListResult> listResources({String? cursor}) async {
    _ensureInitialized();
    _ensureCapability('resources');

    try {
      final params = cursor != null ? McpResourcesListParams(cursor: cursor) : null;
      final result = await _jsonRpc.sendRequest<Map<String, dynamic>>(
        'resources/list',
        params?.toJson(),
      );

      return McpResourcesListResult.fromJson(result);
    } catch (e) {
      throw McpProtocolException('Failed to list resources: $e');
    }
  }

  // Prompts
  Future<McpPromptsListResult> listPrompts({String? cursor}) async {
    _ensureInitialized();
    _ensureCapability('prompts');

    try {
      final params = cursor != null ? {'cursor': cursor} : null;
      final result = await _jsonRpc.sendRequest<Map<String, dynamic>>(
        'prompts/list',
        params,
      );

      return McpPromptsListResult.fromJson(result);
    } catch (e) {
      throw McpProtocolException('Failed to list prompts: $e');
    }
  }

  // Message handling
  void handleMessage(String message) {
    try {
      final parsed = _jsonRpc.parseMessage(message);
      
      if (parsed is JsonRpcResponse) {
        _jsonRpc.handleResponse(parsed);
      } else if (parsed is JsonRpcRequest) {
        _handleRequest(parsed);
      }
    } catch (e) {
      _eventController.add(McpErrorEvent('Failed to handle message: $e'));
    }
  }

  String? getOutgoingMessage() {
    // This would be implemented by the transport layer
    // For now, we'll handle this in the specific transport implementations
    return null;
  }

  void _handleRequest(JsonRpcRequest request) {
    // Handle server-initiated requests/notifications
    switch (request.method) {
      case 'notifications/tools/list_changed':
        _eventController.add(McpToolsListChangedEvent());
        break;
      
      case 'notifications/resources/list_changed':
        _eventController.add(McpResourcesListChangedEvent());
        break;
      
      case 'notifications/resources/updated':
        _eventController.add(McpResourcesUpdatedEvent());
        break;
      
      case 'notifications/prompts/list_changed':
        _eventController.add(McpPromptsListChangedEvent());
        break;
      
      default:
        _eventController.add(McpUnknownRequestEvent(request));
    }
  }

  void _ensureInitialized() {
    if (!_isInitialized) {
      throw McpProtocolException('Protocol not initialized');
    }
  }

  void _ensureCapability(String capability) {
    if (_serverCapabilities == null) {
      throw McpProtocolException('Server capabilities unknown');
    }

    switch (capability) {
      case 'tools':
        if (_serverCapabilities!.tools == null) {
          throw McpProtocolException('Server does not support tools');
        }
        break;
      case 'resources':
        if (_serverCapabilities!.resources == null) {
          throw McpProtocolException('Server does not support resources');
        }
        break;
      case 'prompts':
        if (_serverCapabilities!.prompts == null) {
          throw McpProtocolException('Server does not support prompts');
        }
        break;
    }
  }

  void dispose() {
    _jsonRpc.dispose();
    _eventController.close();
    _isInitialized = false;
  }
}

// Events
abstract class McpProtocolEvent {}

class McpInitializedEvent extends McpProtocolEvent {
  final McpInitializeResult result;
  
  McpInitializedEvent(this.result);
}

class McpErrorEvent extends McpProtocolEvent {
  final String message;
  
  McpErrorEvent(this.message);
}

class McpToolsListChangedEvent extends McpProtocolEvent {}

class McpResourcesListChangedEvent extends McpProtocolEvent {}

class McpResourcesUpdatedEvent extends McpProtocolEvent {}

class McpPromptsListChangedEvent extends McpProtocolEvent {}

class McpUnknownRequestEvent extends McpProtocolEvent {
  final JsonRpcRequest request;
  
  McpUnknownRequestEvent(this.request);
}

// Exception
class McpProtocolException implements Exception {
  final String message;

  const McpProtocolException(this.message);

  @override
  String toString() => 'McpProtocolException: $message';
}