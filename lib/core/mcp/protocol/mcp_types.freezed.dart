// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'mcp_types.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$JsonRpcRequest {

 String get jsonrpc; String get method;@JsonKey(includeIfNull: false) Object? get params;@JsonKey(includeIfNull: false) String? get id;
/// Create a copy of JsonRpcRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$JsonRpcRequestCopyWith<JsonRpcRequest> get copyWith => _$JsonRpcRequestCopyWithImpl<JsonRpcRequest>(this as JsonRpcRequest, _$identity);

  /// Serializes this JsonRpcRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is JsonRpcRequest&&(identical(other.jsonrpc, jsonrpc) || other.jsonrpc == jsonrpc)&&(identical(other.method, method) || other.method == method)&&const DeepCollectionEquality().equals(other.params, params)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,jsonrpc,method,const DeepCollectionEquality().hash(params),id);

@override
String toString() {
  return 'JsonRpcRequest(jsonrpc: $jsonrpc, method: $method, params: $params, id: $id)';
}


}

/// @nodoc
abstract mixin class $JsonRpcRequestCopyWith<$Res>  {
  factory $JsonRpcRequestCopyWith(JsonRpcRequest value, $Res Function(JsonRpcRequest) _then) = _$JsonRpcRequestCopyWithImpl;
@useResult
$Res call({
 String jsonrpc, String method,@JsonKey(includeIfNull: false) Object? params,@JsonKey(includeIfNull: false) String? id
});




}
/// @nodoc
class _$JsonRpcRequestCopyWithImpl<$Res>
    implements $JsonRpcRequestCopyWith<$Res> {
  _$JsonRpcRequestCopyWithImpl(this._self, this._then);

  final JsonRpcRequest _self;
  final $Res Function(JsonRpcRequest) _then;

/// Create a copy of JsonRpcRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? jsonrpc = null,Object? method = null,Object? params = freezed,Object? id = freezed,}) {
  return _then(_self.copyWith(
jsonrpc: null == jsonrpc ? _self.jsonrpc : jsonrpc // ignore: cast_nullable_to_non_nullable
as String,method: null == method ? _self.method : method // ignore: cast_nullable_to_non_nullable
as String,params: freezed == params ? _self.params : params ,id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [JsonRpcRequest].
extension JsonRpcRequestPatterns on JsonRpcRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _JsonRpcRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _JsonRpcRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _JsonRpcRequest value)  $default,){
final _that = this;
switch (_that) {
case _JsonRpcRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _JsonRpcRequest value)?  $default,){
final _that = this;
switch (_that) {
case _JsonRpcRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String jsonrpc,  String method, @JsonKey(includeIfNull: false)  Object? params, @JsonKey(includeIfNull: false)  String? id)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _JsonRpcRequest() when $default != null:
return $default(_that.jsonrpc,_that.method,_that.params,_that.id);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String jsonrpc,  String method, @JsonKey(includeIfNull: false)  Object? params, @JsonKey(includeIfNull: false)  String? id)  $default,) {final _that = this;
switch (_that) {
case _JsonRpcRequest():
return $default(_that.jsonrpc,_that.method,_that.params,_that.id);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String jsonrpc,  String method, @JsonKey(includeIfNull: false)  Object? params, @JsonKey(includeIfNull: false)  String? id)?  $default,) {final _that = this;
switch (_that) {
case _JsonRpcRequest() when $default != null:
return $default(_that.jsonrpc,_that.method,_that.params,_that.id);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _JsonRpcRequest implements JsonRpcRequest {
  const _JsonRpcRequest({required this.jsonrpc, required this.method, @JsonKey(includeIfNull: false) this.params, @JsonKey(includeIfNull: false) this.id});
  factory _JsonRpcRequest.fromJson(Map<String, dynamic> json) => _$JsonRpcRequestFromJson(json);

@override final  String jsonrpc;
@override final  String method;
@override@JsonKey(includeIfNull: false) final  Object? params;
@override@JsonKey(includeIfNull: false) final  String? id;

/// Create a copy of JsonRpcRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$JsonRpcRequestCopyWith<_JsonRpcRequest> get copyWith => __$JsonRpcRequestCopyWithImpl<_JsonRpcRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$JsonRpcRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _JsonRpcRequest&&(identical(other.jsonrpc, jsonrpc) || other.jsonrpc == jsonrpc)&&(identical(other.method, method) || other.method == method)&&const DeepCollectionEquality().equals(other.params, params)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,jsonrpc,method,const DeepCollectionEquality().hash(params),id);

@override
String toString() {
  return 'JsonRpcRequest(jsonrpc: $jsonrpc, method: $method, params: $params, id: $id)';
}


}

/// @nodoc
abstract mixin class _$JsonRpcRequestCopyWith<$Res> implements $JsonRpcRequestCopyWith<$Res> {
  factory _$JsonRpcRequestCopyWith(_JsonRpcRequest value, $Res Function(_JsonRpcRequest) _then) = __$JsonRpcRequestCopyWithImpl;
@override @useResult
$Res call({
 String jsonrpc, String method,@JsonKey(includeIfNull: false) Object? params,@JsonKey(includeIfNull: false) String? id
});




}
/// @nodoc
class __$JsonRpcRequestCopyWithImpl<$Res>
    implements _$JsonRpcRequestCopyWith<$Res> {
  __$JsonRpcRequestCopyWithImpl(this._self, this._then);

  final _JsonRpcRequest _self;
  final $Res Function(_JsonRpcRequest) _then;

/// Create a copy of JsonRpcRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? jsonrpc = null,Object? method = null,Object? params = freezed,Object? id = freezed,}) {
  return _then(_JsonRpcRequest(
jsonrpc: null == jsonrpc ? _self.jsonrpc : jsonrpc // ignore: cast_nullable_to_non_nullable
as String,method: null == method ? _self.method : method // ignore: cast_nullable_to_non_nullable
as String,params: freezed == params ? _self.params : params ,id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$JsonRpcResponse {

 String get jsonrpc;@JsonKey(includeIfNull: false) Object? get result;@JsonKey(includeIfNull: false) JsonRpcError? get error;@JsonKey(includeIfNull: false) String? get id;
/// Create a copy of JsonRpcResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$JsonRpcResponseCopyWith<JsonRpcResponse> get copyWith => _$JsonRpcResponseCopyWithImpl<JsonRpcResponse>(this as JsonRpcResponse, _$identity);

  /// Serializes this JsonRpcResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is JsonRpcResponse&&(identical(other.jsonrpc, jsonrpc) || other.jsonrpc == jsonrpc)&&const DeepCollectionEquality().equals(other.result, result)&&(identical(other.error, error) || other.error == error)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,jsonrpc,const DeepCollectionEquality().hash(result),error,id);

@override
String toString() {
  return 'JsonRpcResponse(jsonrpc: $jsonrpc, result: $result, error: $error, id: $id)';
}


}

/// @nodoc
abstract mixin class $JsonRpcResponseCopyWith<$Res>  {
  factory $JsonRpcResponseCopyWith(JsonRpcResponse value, $Res Function(JsonRpcResponse) _then) = _$JsonRpcResponseCopyWithImpl;
@useResult
$Res call({
 String jsonrpc,@JsonKey(includeIfNull: false) Object? result,@JsonKey(includeIfNull: false) JsonRpcError? error,@JsonKey(includeIfNull: false) String? id
});


$JsonRpcErrorCopyWith<$Res>? get error;

}
/// @nodoc
class _$JsonRpcResponseCopyWithImpl<$Res>
    implements $JsonRpcResponseCopyWith<$Res> {
  _$JsonRpcResponseCopyWithImpl(this._self, this._then);

  final JsonRpcResponse _self;
  final $Res Function(JsonRpcResponse) _then;

/// Create a copy of JsonRpcResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? jsonrpc = null,Object? result = freezed,Object? error = freezed,Object? id = freezed,}) {
  return _then(_self.copyWith(
jsonrpc: null == jsonrpc ? _self.jsonrpc : jsonrpc // ignore: cast_nullable_to_non_nullable
as String,result: freezed == result ? _self.result : result ,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as JsonRpcError?,id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}
/// Create a copy of JsonRpcResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$JsonRpcErrorCopyWith<$Res>? get error {
    if (_self.error == null) {
    return null;
  }

  return $JsonRpcErrorCopyWith<$Res>(_self.error!, (value) {
    return _then(_self.copyWith(error: value));
  });
}
}


/// Adds pattern-matching-related methods to [JsonRpcResponse].
extension JsonRpcResponsePatterns on JsonRpcResponse {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _JsonRpcResponse value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _JsonRpcResponse() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _JsonRpcResponse value)  $default,){
final _that = this;
switch (_that) {
case _JsonRpcResponse():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _JsonRpcResponse value)?  $default,){
final _that = this;
switch (_that) {
case _JsonRpcResponse() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String jsonrpc, @JsonKey(includeIfNull: false)  Object? result, @JsonKey(includeIfNull: false)  JsonRpcError? error, @JsonKey(includeIfNull: false)  String? id)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _JsonRpcResponse() when $default != null:
return $default(_that.jsonrpc,_that.result,_that.error,_that.id);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String jsonrpc, @JsonKey(includeIfNull: false)  Object? result, @JsonKey(includeIfNull: false)  JsonRpcError? error, @JsonKey(includeIfNull: false)  String? id)  $default,) {final _that = this;
switch (_that) {
case _JsonRpcResponse():
return $default(_that.jsonrpc,_that.result,_that.error,_that.id);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String jsonrpc, @JsonKey(includeIfNull: false)  Object? result, @JsonKey(includeIfNull: false)  JsonRpcError? error, @JsonKey(includeIfNull: false)  String? id)?  $default,) {final _that = this;
switch (_that) {
case _JsonRpcResponse() when $default != null:
return $default(_that.jsonrpc,_that.result,_that.error,_that.id);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _JsonRpcResponse implements JsonRpcResponse {
  const _JsonRpcResponse({required this.jsonrpc, @JsonKey(includeIfNull: false) this.result, @JsonKey(includeIfNull: false) this.error, @JsonKey(includeIfNull: false) this.id});
  factory _JsonRpcResponse.fromJson(Map<String, dynamic> json) => _$JsonRpcResponseFromJson(json);

@override final  String jsonrpc;
@override@JsonKey(includeIfNull: false) final  Object? result;
@override@JsonKey(includeIfNull: false) final  JsonRpcError? error;
@override@JsonKey(includeIfNull: false) final  String? id;

/// Create a copy of JsonRpcResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$JsonRpcResponseCopyWith<_JsonRpcResponse> get copyWith => __$JsonRpcResponseCopyWithImpl<_JsonRpcResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$JsonRpcResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _JsonRpcResponse&&(identical(other.jsonrpc, jsonrpc) || other.jsonrpc == jsonrpc)&&const DeepCollectionEquality().equals(other.result, result)&&(identical(other.error, error) || other.error == error)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,jsonrpc,const DeepCollectionEquality().hash(result),error,id);

@override
String toString() {
  return 'JsonRpcResponse(jsonrpc: $jsonrpc, result: $result, error: $error, id: $id)';
}


}

/// @nodoc
abstract mixin class _$JsonRpcResponseCopyWith<$Res> implements $JsonRpcResponseCopyWith<$Res> {
  factory _$JsonRpcResponseCopyWith(_JsonRpcResponse value, $Res Function(_JsonRpcResponse) _then) = __$JsonRpcResponseCopyWithImpl;
@override @useResult
$Res call({
 String jsonrpc,@JsonKey(includeIfNull: false) Object? result,@JsonKey(includeIfNull: false) JsonRpcError? error,@JsonKey(includeIfNull: false) String? id
});


@override $JsonRpcErrorCopyWith<$Res>? get error;

}
/// @nodoc
class __$JsonRpcResponseCopyWithImpl<$Res>
    implements _$JsonRpcResponseCopyWith<$Res> {
  __$JsonRpcResponseCopyWithImpl(this._self, this._then);

  final _JsonRpcResponse _self;
  final $Res Function(_JsonRpcResponse) _then;

/// Create a copy of JsonRpcResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? jsonrpc = null,Object? result = freezed,Object? error = freezed,Object? id = freezed,}) {
  return _then(_JsonRpcResponse(
jsonrpc: null == jsonrpc ? _self.jsonrpc : jsonrpc // ignore: cast_nullable_to_non_nullable
as String,result: freezed == result ? _self.result : result ,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as JsonRpcError?,id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of JsonRpcResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$JsonRpcErrorCopyWith<$Res>? get error {
    if (_self.error == null) {
    return null;
  }

  return $JsonRpcErrorCopyWith<$Res>(_self.error!, (value) {
    return _then(_self.copyWith(error: value));
  });
}
}


/// @nodoc
mixin _$JsonRpcError {

 int get code; String get message;@JsonKey(includeIfNull: false) Object? get data;
/// Create a copy of JsonRpcError
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$JsonRpcErrorCopyWith<JsonRpcError> get copyWith => _$JsonRpcErrorCopyWithImpl<JsonRpcError>(this as JsonRpcError, _$identity);

  /// Serializes this JsonRpcError to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is JsonRpcError&&(identical(other.code, code) || other.code == code)&&(identical(other.message, message) || other.message == message)&&const DeepCollectionEquality().equals(other.data, data));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,code,message,const DeepCollectionEquality().hash(data));

@override
String toString() {
  return 'JsonRpcError(code: $code, message: $message, data: $data)';
}


}

/// @nodoc
abstract mixin class $JsonRpcErrorCopyWith<$Res>  {
  factory $JsonRpcErrorCopyWith(JsonRpcError value, $Res Function(JsonRpcError) _then) = _$JsonRpcErrorCopyWithImpl;
@useResult
$Res call({
 int code, String message,@JsonKey(includeIfNull: false) Object? data
});




}
/// @nodoc
class _$JsonRpcErrorCopyWithImpl<$Res>
    implements $JsonRpcErrorCopyWith<$Res> {
  _$JsonRpcErrorCopyWithImpl(this._self, this._then);

  final JsonRpcError _self;
  final $Res Function(JsonRpcError) _then;

/// Create a copy of JsonRpcError
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? code = null,Object? message = null,Object? data = freezed,}) {
  return _then(_self.copyWith(
code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as int,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,data: freezed == data ? _self.data : data ,
  ));
}

}


/// Adds pattern-matching-related methods to [JsonRpcError].
extension JsonRpcErrorPatterns on JsonRpcError {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _JsonRpcError value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _JsonRpcError() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _JsonRpcError value)  $default,){
final _that = this;
switch (_that) {
case _JsonRpcError():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _JsonRpcError value)?  $default,){
final _that = this;
switch (_that) {
case _JsonRpcError() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int code,  String message, @JsonKey(includeIfNull: false)  Object? data)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _JsonRpcError() when $default != null:
return $default(_that.code,_that.message,_that.data);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int code,  String message, @JsonKey(includeIfNull: false)  Object? data)  $default,) {final _that = this;
switch (_that) {
case _JsonRpcError():
return $default(_that.code,_that.message,_that.data);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int code,  String message, @JsonKey(includeIfNull: false)  Object? data)?  $default,) {final _that = this;
switch (_that) {
case _JsonRpcError() when $default != null:
return $default(_that.code,_that.message,_that.data);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _JsonRpcError implements JsonRpcError {
  const _JsonRpcError({required this.code, required this.message, @JsonKey(includeIfNull: false) this.data});
  factory _JsonRpcError.fromJson(Map<String, dynamic> json) => _$JsonRpcErrorFromJson(json);

@override final  int code;
@override final  String message;
@override@JsonKey(includeIfNull: false) final  Object? data;

/// Create a copy of JsonRpcError
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$JsonRpcErrorCopyWith<_JsonRpcError> get copyWith => __$JsonRpcErrorCopyWithImpl<_JsonRpcError>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$JsonRpcErrorToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _JsonRpcError&&(identical(other.code, code) || other.code == code)&&(identical(other.message, message) || other.message == message)&&const DeepCollectionEquality().equals(other.data, data));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,code,message,const DeepCollectionEquality().hash(data));

@override
String toString() {
  return 'JsonRpcError(code: $code, message: $message, data: $data)';
}


}

/// @nodoc
abstract mixin class _$JsonRpcErrorCopyWith<$Res> implements $JsonRpcErrorCopyWith<$Res> {
  factory _$JsonRpcErrorCopyWith(_JsonRpcError value, $Res Function(_JsonRpcError) _then) = __$JsonRpcErrorCopyWithImpl;
@override @useResult
$Res call({
 int code, String message,@JsonKey(includeIfNull: false) Object? data
});




}
/// @nodoc
class __$JsonRpcErrorCopyWithImpl<$Res>
    implements _$JsonRpcErrorCopyWith<$Res> {
  __$JsonRpcErrorCopyWithImpl(this._self, this._then);

  final _JsonRpcError _self;
  final $Res Function(_JsonRpcError) _then;

/// Create a copy of JsonRpcError
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? code = null,Object? message = null,Object? data = freezed,}) {
  return _then(_JsonRpcError(
code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as int,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,data: freezed == data ? _self.data : data ,
  ));
}


}


/// @nodoc
mixin _$McpCapabilities {

 McpToolsCapability? get tools; McpResourcesCapability? get resources; McpPromptsCapability? get prompts; McpLoggingCapability? get logging;
/// Create a copy of McpCapabilities
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$McpCapabilitiesCopyWith<McpCapabilities> get copyWith => _$McpCapabilitiesCopyWithImpl<McpCapabilities>(this as McpCapabilities, _$identity);

  /// Serializes this McpCapabilities to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is McpCapabilities&&(identical(other.tools, tools) || other.tools == tools)&&(identical(other.resources, resources) || other.resources == resources)&&(identical(other.prompts, prompts) || other.prompts == prompts)&&(identical(other.logging, logging) || other.logging == logging));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,tools,resources,prompts,logging);

@override
String toString() {
  return 'McpCapabilities(tools: $tools, resources: $resources, prompts: $prompts, logging: $logging)';
}


}

/// @nodoc
abstract mixin class $McpCapabilitiesCopyWith<$Res>  {
  factory $McpCapabilitiesCopyWith(McpCapabilities value, $Res Function(McpCapabilities) _then) = _$McpCapabilitiesCopyWithImpl;
@useResult
$Res call({
 McpToolsCapability? tools, McpResourcesCapability? resources, McpPromptsCapability? prompts, McpLoggingCapability? logging
});


$McpToolsCapabilityCopyWith<$Res>? get tools;$McpResourcesCapabilityCopyWith<$Res>? get resources;$McpPromptsCapabilityCopyWith<$Res>? get prompts;$McpLoggingCapabilityCopyWith<$Res>? get logging;

}
/// @nodoc
class _$McpCapabilitiesCopyWithImpl<$Res>
    implements $McpCapabilitiesCopyWith<$Res> {
  _$McpCapabilitiesCopyWithImpl(this._self, this._then);

  final McpCapabilities _self;
  final $Res Function(McpCapabilities) _then;

/// Create a copy of McpCapabilities
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? tools = freezed,Object? resources = freezed,Object? prompts = freezed,Object? logging = freezed,}) {
  return _then(_self.copyWith(
tools: freezed == tools ? _self.tools : tools // ignore: cast_nullable_to_non_nullable
as McpToolsCapability?,resources: freezed == resources ? _self.resources : resources // ignore: cast_nullable_to_non_nullable
as McpResourcesCapability?,prompts: freezed == prompts ? _self.prompts : prompts // ignore: cast_nullable_to_non_nullable
as McpPromptsCapability?,logging: freezed == logging ? _self.logging : logging // ignore: cast_nullable_to_non_nullable
as McpLoggingCapability?,
  ));
}
/// Create a copy of McpCapabilities
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$McpToolsCapabilityCopyWith<$Res>? get tools {
    if (_self.tools == null) {
    return null;
  }

  return $McpToolsCapabilityCopyWith<$Res>(_self.tools!, (value) {
    return _then(_self.copyWith(tools: value));
  });
}/// Create a copy of McpCapabilities
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$McpResourcesCapabilityCopyWith<$Res>? get resources {
    if (_self.resources == null) {
    return null;
  }

  return $McpResourcesCapabilityCopyWith<$Res>(_self.resources!, (value) {
    return _then(_self.copyWith(resources: value));
  });
}/// Create a copy of McpCapabilities
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$McpPromptsCapabilityCopyWith<$Res>? get prompts {
    if (_self.prompts == null) {
    return null;
  }

  return $McpPromptsCapabilityCopyWith<$Res>(_self.prompts!, (value) {
    return _then(_self.copyWith(prompts: value));
  });
}/// Create a copy of McpCapabilities
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$McpLoggingCapabilityCopyWith<$Res>? get logging {
    if (_self.logging == null) {
    return null;
  }

  return $McpLoggingCapabilityCopyWith<$Res>(_self.logging!, (value) {
    return _then(_self.copyWith(logging: value));
  });
}
}


/// Adds pattern-matching-related methods to [McpCapabilities].
extension McpCapabilitiesPatterns on McpCapabilities {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _McpCapabilities value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _McpCapabilities() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _McpCapabilities value)  $default,){
final _that = this;
switch (_that) {
case _McpCapabilities():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _McpCapabilities value)?  $default,){
final _that = this;
switch (_that) {
case _McpCapabilities() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( McpToolsCapability? tools,  McpResourcesCapability? resources,  McpPromptsCapability? prompts,  McpLoggingCapability? logging)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _McpCapabilities() when $default != null:
return $default(_that.tools,_that.resources,_that.prompts,_that.logging);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( McpToolsCapability? tools,  McpResourcesCapability? resources,  McpPromptsCapability? prompts,  McpLoggingCapability? logging)  $default,) {final _that = this;
switch (_that) {
case _McpCapabilities():
return $default(_that.tools,_that.resources,_that.prompts,_that.logging);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( McpToolsCapability? tools,  McpResourcesCapability? resources,  McpPromptsCapability? prompts,  McpLoggingCapability? logging)?  $default,) {final _that = this;
switch (_that) {
case _McpCapabilities() when $default != null:
return $default(_that.tools,_that.resources,_that.prompts,_that.logging);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _McpCapabilities implements McpCapabilities {
  const _McpCapabilities({this.tools, this.resources, this.prompts, this.logging});
  factory _McpCapabilities.fromJson(Map<String, dynamic> json) => _$McpCapabilitiesFromJson(json);

@override final  McpToolsCapability? tools;
@override final  McpResourcesCapability? resources;
@override final  McpPromptsCapability? prompts;
@override final  McpLoggingCapability? logging;

/// Create a copy of McpCapabilities
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$McpCapabilitiesCopyWith<_McpCapabilities> get copyWith => __$McpCapabilitiesCopyWithImpl<_McpCapabilities>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$McpCapabilitiesToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _McpCapabilities&&(identical(other.tools, tools) || other.tools == tools)&&(identical(other.resources, resources) || other.resources == resources)&&(identical(other.prompts, prompts) || other.prompts == prompts)&&(identical(other.logging, logging) || other.logging == logging));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,tools,resources,prompts,logging);

@override
String toString() {
  return 'McpCapabilities(tools: $tools, resources: $resources, prompts: $prompts, logging: $logging)';
}


}

/// @nodoc
abstract mixin class _$McpCapabilitiesCopyWith<$Res> implements $McpCapabilitiesCopyWith<$Res> {
  factory _$McpCapabilitiesCopyWith(_McpCapabilities value, $Res Function(_McpCapabilities) _then) = __$McpCapabilitiesCopyWithImpl;
@override @useResult
$Res call({
 McpToolsCapability? tools, McpResourcesCapability? resources, McpPromptsCapability? prompts, McpLoggingCapability? logging
});


@override $McpToolsCapabilityCopyWith<$Res>? get tools;@override $McpResourcesCapabilityCopyWith<$Res>? get resources;@override $McpPromptsCapabilityCopyWith<$Res>? get prompts;@override $McpLoggingCapabilityCopyWith<$Res>? get logging;

}
/// @nodoc
class __$McpCapabilitiesCopyWithImpl<$Res>
    implements _$McpCapabilitiesCopyWith<$Res> {
  __$McpCapabilitiesCopyWithImpl(this._self, this._then);

  final _McpCapabilities _self;
  final $Res Function(_McpCapabilities) _then;

/// Create a copy of McpCapabilities
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? tools = freezed,Object? resources = freezed,Object? prompts = freezed,Object? logging = freezed,}) {
  return _then(_McpCapabilities(
tools: freezed == tools ? _self.tools : tools // ignore: cast_nullable_to_non_nullable
as McpToolsCapability?,resources: freezed == resources ? _self.resources : resources // ignore: cast_nullable_to_non_nullable
as McpResourcesCapability?,prompts: freezed == prompts ? _self.prompts : prompts // ignore: cast_nullable_to_non_nullable
as McpPromptsCapability?,logging: freezed == logging ? _self.logging : logging // ignore: cast_nullable_to_non_nullable
as McpLoggingCapability?,
  ));
}

/// Create a copy of McpCapabilities
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$McpToolsCapabilityCopyWith<$Res>? get tools {
    if (_self.tools == null) {
    return null;
  }

  return $McpToolsCapabilityCopyWith<$Res>(_self.tools!, (value) {
    return _then(_self.copyWith(tools: value));
  });
}/// Create a copy of McpCapabilities
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$McpResourcesCapabilityCopyWith<$Res>? get resources {
    if (_self.resources == null) {
    return null;
  }

  return $McpResourcesCapabilityCopyWith<$Res>(_self.resources!, (value) {
    return _then(_self.copyWith(resources: value));
  });
}/// Create a copy of McpCapabilities
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$McpPromptsCapabilityCopyWith<$Res>? get prompts {
    if (_self.prompts == null) {
    return null;
  }

  return $McpPromptsCapabilityCopyWith<$Res>(_self.prompts!, (value) {
    return _then(_self.copyWith(prompts: value));
  });
}/// Create a copy of McpCapabilities
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$McpLoggingCapabilityCopyWith<$Res>? get logging {
    if (_self.logging == null) {
    return null;
  }

  return $McpLoggingCapabilityCopyWith<$Res>(_self.logging!, (value) {
    return _then(_self.copyWith(logging: value));
  });
}
}


/// @nodoc
mixin _$McpToolsCapability {

 bool get listChanged;
/// Create a copy of McpToolsCapability
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$McpToolsCapabilityCopyWith<McpToolsCapability> get copyWith => _$McpToolsCapabilityCopyWithImpl<McpToolsCapability>(this as McpToolsCapability, _$identity);

  /// Serializes this McpToolsCapability to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is McpToolsCapability&&(identical(other.listChanged, listChanged) || other.listChanged == listChanged));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,listChanged);

@override
String toString() {
  return 'McpToolsCapability(listChanged: $listChanged)';
}


}

/// @nodoc
abstract mixin class $McpToolsCapabilityCopyWith<$Res>  {
  factory $McpToolsCapabilityCopyWith(McpToolsCapability value, $Res Function(McpToolsCapability) _then) = _$McpToolsCapabilityCopyWithImpl;
@useResult
$Res call({
 bool listChanged
});




}
/// @nodoc
class _$McpToolsCapabilityCopyWithImpl<$Res>
    implements $McpToolsCapabilityCopyWith<$Res> {
  _$McpToolsCapabilityCopyWithImpl(this._self, this._then);

  final McpToolsCapability _self;
  final $Res Function(McpToolsCapability) _then;

/// Create a copy of McpToolsCapability
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? listChanged = null,}) {
  return _then(_self.copyWith(
listChanged: null == listChanged ? _self.listChanged : listChanged // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [McpToolsCapability].
extension McpToolsCapabilityPatterns on McpToolsCapability {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _McpToolsCapability value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _McpToolsCapability() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _McpToolsCapability value)  $default,){
final _that = this;
switch (_that) {
case _McpToolsCapability():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _McpToolsCapability value)?  $default,){
final _that = this;
switch (_that) {
case _McpToolsCapability() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( bool listChanged)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _McpToolsCapability() when $default != null:
return $default(_that.listChanged);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( bool listChanged)  $default,) {final _that = this;
switch (_that) {
case _McpToolsCapability():
return $default(_that.listChanged);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( bool listChanged)?  $default,) {final _that = this;
switch (_that) {
case _McpToolsCapability() when $default != null:
return $default(_that.listChanged);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _McpToolsCapability implements McpToolsCapability {
  const _McpToolsCapability({this.listChanged = false});
  factory _McpToolsCapability.fromJson(Map<String, dynamic> json) => _$McpToolsCapabilityFromJson(json);

@override@JsonKey() final  bool listChanged;

/// Create a copy of McpToolsCapability
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$McpToolsCapabilityCopyWith<_McpToolsCapability> get copyWith => __$McpToolsCapabilityCopyWithImpl<_McpToolsCapability>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$McpToolsCapabilityToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _McpToolsCapability&&(identical(other.listChanged, listChanged) || other.listChanged == listChanged));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,listChanged);

@override
String toString() {
  return 'McpToolsCapability(listChanged: $listChanged)';
}


}

/// @nodoc
abstract mixin class _$McpToolsCapabilityCopyWith<$Res> implements $McpToolsCapabilityCopyWith<$Res> {
  factory _$McpToolsCapabilityCopyWith(_McpToolsCapability value, $Res Function(_McpToolsCapability) _then) = __$McpToolsCapabilityCopyWithImpl;
@override @useResult
$Res call({
 bool listChanged
});




}
/// @nodoc
class __$McpToolsCapabilityCopyWithImpl<$Res>
    implements _$McpToolsCapabilityCopyWith<$Res> {
  __$McpToolsCapabilityCopyWithImpl(this._self, this._then);

  final _McpToolsCapability _self;
  final $Res Function(_McpToolsCapability) _then;

/// Create a copy of McpToolsCapability
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? listChanged = null,}) {
  return _then(_McpToolsCapability(
listChanged: null == listChanged ? _self.listChanged : listChanged // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$McpResourcesCapability {

 bool get subscribe; bool get listChanged;
/// Create a copy of McpResourcesCapability
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$McpResourcesCapabilityCopyWith<McpResourcesCapability> get copyWith => _$McpResourcesCapabilityCopyWithImpl<McpResourcesCapability>(this as McpResourcesCapability, _$identity);

  /// Serializes this McpResourcesCapability to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is McpResourcesCapability&&(identical(other.subscribe, subscribe) || other.subscribe == subscribe)&&(identical(other.listChanged, listChanged) || other.listChanged == listChanged));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,subscribe,listChanged);

@override
String toString() {
  return 'McpResourcesCapability(subscribe: $subscribe, listChanged: $listChanged)';
}


}

/// @nodoc
abstract mixin class $McpResourcesCapabilityCopyWith<$Res>  {
  factory $McpResourcesCapabilityCopyWith(McpResourcesCapability value, $Res Function(McpResourcesCapability) _then) = _$McpResourcesCapabilityCopyWithImpl;
@useResult
$Res call({
 bool subscribe, bool listChanged
});




}
/// @nodoc
class _$McpResourcesCapabilityCopyWithImpl<$Res>
    implements $McpResourcesCapabilityCopyWith<$Res> {
  _$McpResourcesCapabilityCopyWithImpl(this._self, this._then);

  final McpResourcesCapability _self;
  final $Res Function(McpResourcesCapability) _then;

/// Create a copy of McpResourcesCapability
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? subscribe = null,Object? listChanged = null,}) {
  return _then(_self.copyWith(
subscribe: null == subscribe ? _self.subscribe : subscribe // ignore: cast_nullable_to_non_nullable
as bool,listChanged: null == listChanged ? _self.listChanged : listChanged // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [McpResourcesCapability].
extension McpResourcesCapabilityPatterns on McpResourcesCapability {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _McpResourcesCapability value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _McpResourcesCapability() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _McpResourcesCapability value)  $default,){
final _that = this;
switch (_that) {
case _McpResourcesCapability():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _McpResourcesCapability value)?  $default,){
final _that = this;
switch (_that) {
case _McpResourcesCapability() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( bool subscribe,  bool listChanged)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _McpResourcesCapability() when $default != null:
return $default(_that.subscribe,_that.listChanged);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( bool subscribe,  bool listChanged)  $default,) {final _that = this;
switch (_that) {
case _McpResourcesCapability():
return $default(_that.subscribe,_that.listChanged);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( bool subscribe,  bool listChanged)?  $default,) {final _that = this;
switch (_that) {
case _McpResourcesCapability() when $default != null:
return $default(_that.subscribe,_that.listChanged);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _McpResourcesCapability implements McpResourcesCapability {
  const _McpResourcesCapability({this.subscribe = false, this.listChanged = false});
  factory _McpResourcesCapability.fromJson(Map<String, dynamic> json) => _$McpResourcesCapabilityFromJson(json);

@override@JsonKey() final  bool subscribe;
@override@JsonKey() final  bool listChanged;

/// Create a copy of McpResourcesCapability
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$McpResourcesCapabilityCopyWith<_McpResourcesCapability> get copyWith => __$McpResourcesCapabilityCopyWithImpl<_McpResourcesCapability>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$McpResourcesCapabilityToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _McpResourcesCapability&&(identical(other.subscribe, subscribe) || other.subscribe == subscribe)&&(identical(other.listChanged, listChanged) || other.listChanged == listChanged));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,subscribe,listChanged);

@override
String toString() {
  return 'McpResourcesCapability(subscribe: $subscribe, listChanged: $listChanged)';
}


}

/// @nodoc
abstract mixin class _$McpResourcesCapabilityCopyWith<$Res> implements $McpResourcesCapabilityCopyWith<$Res> {
  factory _$McpResourcesCapabilityCopyWith(_McpResourcesCapability value, $Res Function(_McpResourcesCapability) _then) = __$McpResourcesCapabilityCopyWithImpl;
@override @useResult
$Res call({
 bool subscribe, bool listChanged
});




}
/// @nodoc
class __$McpResourcesCapabilityCopyWithImpl<$Res>
    implements _$McpResourcesCapabilityCopyWith<$Res> {
  __$McpResourcesCapabilityCopyWithImpl(this._self, this._then);

  final _McpResourcesCapability _self;
  final $Res Function(_McpResourcesCapability) _then;

/// Create a copy of McpResourcesCapability
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? subscribe = null,Object? listChanged = null,}) {
  return _then(_McpResourcesCapability(
subscribe: null == subscribe ? _self.subscribe : subscribe // ignore: cast_nullable_to_non_nullable
as bool,listChanged: null == listChanged ? _self.listChanged : listChanged // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$McpPromptsCapability {

 bool get listChanged;
/// Create a copy of McpPromptsCapability
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$McpPromptsCapabilityCopyWith<McpPromptsCapability> get copyWith => _$McpPromptsCapabilityCopyWithImpl<McpPromptsCapability>(this as McpPromptsCapability, _$identity);

  /// Serializes this McpPromptsCapability to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is McpPromptsCapability&&(identical(other.listChanged, listChanged) || other.listChanged == listChanged));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,listChanged);

@override
String toString() {
  return 'McpPromptsCapability(listChanged: $listChanged)';
}


}

/// @nodoc
abstract mixin class $McpPromptsCapabilityCopyWith<$Res>  {
  factory $McpPromptsCapabilityCopyWith(McpPromptsCapability value, $Res Function(McpPromptsCapability) _then) = _$McpPromptsCapabilityCopyWithImpl;
@useResult
$Res call({
 bool listChanged
});




}
/// @nodoc
class _$McpPromptsCapabilityCopyWithImpl<$Res>
    implements $McpPromptsCapabilityCopyWith<$Res> {
  _$McpPromptsCapabilityCopyWithImpl(this._self, this._then);

  final McpPromptsCapability _self;
  final $Res Function(McpPromptsCapability) _then;

/// Create a copy of McpPromptsCapability
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? listChanged = null,}) {
  return _then(_self.copyWith(
listChanged: null == listChanged ? _self.listChanged : listChanged // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [McpPromptsCapability].
extension McpPromptsCapabilityPatterns on McpPromptsCapability {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _McpPromptsCapability value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _McpPromptsCapability() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _McpPromptsCapability value)  $default,){
final _that = this;
switch (_that) {
case _McpPromptsCapability():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _McpPromptsCapability value)?  $default,){
final _that = this;
switch (_that) {
case _McpPromptsCapability() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( bool listChanged)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _McpPromptsCapability() when $default != null:
return $default(_that.listChanged);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( bool listChanged)  $default,) {final _that = this;
switch (_that) {
case _McpPromptsCapability():
return $default(_that.listChanged);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( bool listChanged)?  $default,) {final _that = this;
switch (_that) {
case _McpPromptsCapability() when $default != null:
return $default(_that.listChanged);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _McpPromptsCapability implements McpPromptsCapability {
  const _McpPromptsCapability({this.listChanged = false});
  factory _McpPromptsCapability.fromJson(Map<String, dynamic> json) => _$McpPromptsCapabilityFromJson(json);

@override@JsonKey() final  bool listChanged;

/// Create a copy of McpPromptsCapability
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$McpPromptsCapabilityCopyWith<_McpPromptsCapability> get copyWith => __$McpPromptsCapabilityCopyWithImpl<_McpPromptsCapability>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$McpPromptsCapabilityToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _McpPromptsCapability&&(identical(other.listChanged, listChanged) || other.listChanged == listChanged));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,listChanged);

@override
String toString() {
  return 'McpPromptsCapability(listChanged: $listChanged)';
}


}

/// @nodoc
abstract mixin class _$McpPromptsCapabilityCopyWith<$Res> implements $McpPromptsCapabilityCopyWith<$Res> {
  factory _$McpPromptsCapabilityCopyWith(_McpPromptsCapability value, $Res Function(_McpPromptsCapability) _then) = __$McpPromptsCapabilityCopyWithImpl;
@override @useResult
$Res call({
 bool listChanged
});




}
/// @nodoc
class __$McpPromptsCapabilityCopyWithImpl<$Res>
    implements _$McpPromptsCapabilityCopyWith<$Res> {
  __$McpPromptsCapabilityCopyWithImpl(this._self, this._then);

  final _McpPromptsCapability _self;
  final $Res Function(_McpPromptsCapability) _then;

/// Create a copy of McpPromptsCapability
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? listChanged = null,}) {
  return _then(_McpPromptsCapability(
listChanged: null == listChanged ? _self.listChanged : listChanged // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$McpLoggingCapability {



  /// Serializes this McpLoggingCapability to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is McpLoggingCapability);
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'McpLoggingCapability()';
}


}

/// @nodoc
class $McpLoggingCapabilityCopyWith<$Res>  {
$McpLoggingCapabilityCopyWith(McpLoggingCapability _, $Res Function(McpLoggingCapability) __);
}


/// Adds pattern-matching-related methods to [McpLoggingCapability].
extension McpLoggingCapabilityPatterns on McpLoggingCapability {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _McpLoggingCapability value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _McpLoggingCapability() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _McpLoggingCapability value)  $default,){
final _that = this;
switch (_that) {
case _McpLoggingCapability():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _McpLoggingCapability value)?  $default,){
final _that = this;
switch (_that) {
case _McpLoggingCapability() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function()?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _McpLoggingCapability() when $default != null:
return $default();case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function()  $default,) {final _that = this;
switch (_that) {
case _McpLoggingCapability():
return $default();case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function()?  $default,) {final _that = this;
switch (_that) {
case _McpLoggingCapability() when $default != null:
return $default();case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _McpLoggingCapability implements McpLoggingCapability {
  const _McpLoggingCapability();
  factory _McpLoggingCapability.fromJson(Map<String, dynamic> json) => _$McpLoggingCapabilityFromJson(json);




@override
Map<String, dynamic> toJson() {
  return _$McpLoggingCapabilityToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _McpLoggingCapability);
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'McpLoggingCapability()';
}


}





/// @nodoc
mixin _$McpClientInfo {

 String get name; String get version;
/// Create a copy of McpClientInfo
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$McpClientInfoCopyWith<McpClientInfo> get copyWith => _$McpClientInfoCopyWithImpl<McpClientInfo>(this as McpClientInfo, _$identity);

  /// Serializes this McpClientInfo to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is McpClientInfo&&(identical(other.name, name) || other.name == name)&&(identical(other.version, version) || other.version == version));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,version);

@override
String toString() {
  return 'McpClientInfo(name: $name, version: $version)';
}


}

/// @nodoc
abstract mixin class $McpClientInfoCopyWith<$Res>  {
  factory $McpClientInfoCopyWith(McpClientInfo value, $Res Function(McpClientInfo) _then) = _$McpClientInfoCopyWithImpl;
@useResult
$Res call({
 String name, String version
});




}
/// @nodoc
class _$McpClientInfoCopyWithImpl<$Res>
    implements $McpClientInfoCopyWith<$Res> {
  _$McpClientInfoCopyWithImpl(this._self, this._then);

  final McpClientInfo _self;
  final $Res Function(McpClientInfo) _then;

/// Create a copy of McpClientInfo
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = null,Object? version = null,}) {
  return _then(_self.copyWith(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,version: null == version ? _self.version : version // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [McpClientInfo].
extension McpClientInfoPatterns on McpClientInfo {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _McpClientInfo value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _McpClientInfo() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _McpClientInfo value)  $default,){
final _that = this;
switch (_that) {
case _McpClientInfo():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _McpClientInfo value)?  $default,){
final _that = this;
switch (_that) {
case _McpClientInfo() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String name,  String version)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _McpClientInfo() when $default != null:
return $default(_that.name,_that.version);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String name,  String version)  $default,) {final _that = this;
switch (_that) {
case _McpClientInfo():
return $default(_that.name,_that.version);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String name,  String version)?  $default,) {final _that = this;
switch (_that) {
case _McpClientInfo() when $default != null:
return $default(_that.name,_that.version);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _McpClientInfo implements McpClientInfo {
  const _McpClientInfo({required this.name, required this.version});
  factory _McpClientInfo.fromJson(Map<String, dynamic> json) => _$McpClientInfoFromJson(json);

@override final  String name;
@override final  String version;

/// Create a copy of McpClientInfo
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$McpClientInfoCopyWith<_McpClientInfo> get copyWith => __$McpClientInfoCopyWithImpl<_McpClientInfo>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$McpClientInfoToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _McpClientInfo&&(identical(other.name, name) || other.name == name)&&(identical(other.version, version) || other.version == version));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,version);

@override
String toString() {
  return 'McpClientInfo(name: $name, version: $version)';
}


}

/// @nodoc
abstract mixin class _$McpClientInfoCopyWith<$Res> implements $McpClientInfoCopyWith<$Res> {
  factory _$McpClientInfoCopyWith(_McpClientInfo value, $Res Function(_McpClientInfo) _then) = __$McpClientInfoCopyWithImpl;
@override @useResult
$Res call({
 String name, String version
});




}
/// @nodoc
class __$McpClientInfoCopyWithImpl<$Res>
    implements _$McpClientInfoCopyWith<$Res> {
  __$McpClientInfoCopyWithImpl(this._self, this._then);

  final _McpClientInfo _self;
  final $Res Function(_McpClientInfo) _then;

/// Create a copy of McpClientInfo
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = null,Object? version = null,}) {
  return _then(_McpClientInfo(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,version: null == version ? _self.version : version // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$McpServerInfo {

 String get name; String get version;
/// Create a copy of McpServerInfo
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$McpServerInfoCopyWith<McpServerInfo> get copyWith => _$McpServerInfoCopyWithImpl<McpServerInfo>(this as McpServerInfo, _$identity);

  /// Serializes this McpServerInfo to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is McpServerInfo&&(identical(other.name, name) || other.name == name)&&(identical(other.version, version) || other.version == version));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,version);

@override
String toString() {
  return 'McpServerInfo(name: $name, version: $version)';
}


}

/// @nodoc
abstract mixin class $McpServerInfoCopyWith<$Res>  {
  factory $McpServerInfoCopyWith(McpServerInfo value, $Res Function(McpServerInfo) _then) = _$McpServerInfoCopyWithImpl;
@useResult
$Res call({
 String name, String version
});




}
/// @nodoc
class _$McpServerInfoCopyWithImpl<$Res>
    implements $McpServerInfoCopyWith<$Res> {
  _$McpServerInfoCopyWithImpl(this._self, this._then);

  final McpServerInfo _self;
  final $Res Function(McpServerInfo) _then;

/// Create a copy of McpServerInfo
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = null,Object? version = null,}) {
  return _then(_self.copyWith(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,version: null == version ? _self.version : version // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [McpServerInfo].
extension McpServerInfoPatterns on McpServerInfo {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _McpServerInfo value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _McpServerInfo() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _McpServerInfo value)  $default,){
final _that = this;
switch (_that) {
case _McpServerInfo():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _McpServerInfo value)?  $default,){
final _that = this;
switch (_that) {
case _McpServerInfo() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String name,  String version)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _McpServerInfo() when $default != null:
return $default(_that.name,_that.version);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String name,  String version)  $default,) {final _that = this;
switch (_that) {
case _McpServerInfo():
return $default(_that.name,_that.version);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String name,  String version)?  $default,) {final _that = this;
switch (_that) {
case _McpServerInfo() when $default != null:
return $default(_that.name,_that.version);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _McpServerInfo implements McpServerInfo {
  const _McpServerInfo({required this.name, required this.version});
  factory _McpServerInfo.fromJson(Map<String, dynamic> json) => _$McpServerInfoFromJson(json);

@override final  String name;
@override final  String version;

/// Create a copy of McpServerInfo
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$McpServerInfoCopyWith<_McpServerInfo> get copyWith => __$McpServerInfoCopyWithImpl<_McpServerInfo>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$McpServerInfoToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _McpServerInfo&&(identical(other.name, name) || other.name == name)&&(identical(other.version, version) || other.version == version));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,version);

@override
String toString() {
  return 'McpServerInfo(name: $name, version: $version)';
}


}

/// @nodoc
abstract mixin class _$McpServerInfoCopyWith<$Res> implements $McpServerInfoCopyWith<$Res> {
  factory _$McpServerInfoCopyWith(_McpServerInfo value, $Res Function(_McpServerInfo) _then) = __$McpServerInfoCopyWithImpl;
@override @useResult
$Res call({
 String name, String version
});




}
/// @nodoc
class __$McpServerInfoCopyWithImpl<$Res>
    implements _$McpServerInfoCopyWith<$Res> {
  __$McpServerInfoCopyWithImpl(this._self, this._then);

  final _McpServerInfo _self;
  final $Res Function(_McpServerInfo) _then;

/// Create a copy of McpServerInfo
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = null,Object? version = null,}) {
  return _then(_McpServerInfo(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,version: null == version ? _self.version : version // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$McpInitializeParams {

 String get protocolVersion; McpCapabilities get capabilities; McpClientInfo get clientInfo;
/// Create a copy of McpInitializeParams
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$McpInitializeParamsCopyWith<McpInitializeParams> get copyWith => _$McpInitializeParamsCopyWithImpl<McpInitializeParams>(this as McpInitializeParams, _$identity);

  /// Serializes this McpInitializeParams to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is McpInitializeParams&&(identical(other.protocolVersion, protocolVersion) || other.protocolVersion == protocolVersion)&&(identical(other.capabilities, capabilities) || other.capabilities == capabilities)&&(identical(other.clientInfo, clientInfo) || other.clientInfo == clientInfo));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,protocolVersion,capabilities,clientInfo);

@override
String toString() {
  return 'McpInitializeParams(protocolVersion: $protocolVersion, capabilities: $capabilities, clientInfo: $clientInfo)';
}


}

/// @nodoc
abstract mixin class $McpInitializeParamsCopyWith<$Res>  {
  factory $McpInitializeParamsCopyWith(McpInitializeParams value, $Res Function(McpInitializeParams) _then) = _$McpInitializeParamsCopyWithImpl;
@useResult
$Res call({
 String protocolVersion, McpCapabilities capabilities, McpClientInfo clientInfo
});


$McpCapabilitiesCopyWith<$Res> get capabilities;$McpClientInfoCopyWith<$Res> get clientInfo;

}
/// @nodoc
class _$McpInitializeParamsCopyWithImpl<$Res>
    implements $McpInitializeParamsCopyWith<$Res> {
  _$McpInitializeParamsCopyWithImpl(this._self, this._then);

  final McpInitializeParams _self;
  final $Res Function(McpInitializeParams) _then;

/// Create a copy of McpInitializeParams
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? protocolVersion = null,Object? capabilities = null,Object? clientInfo = null,}) {
  return _then(_self.copyWith(
protocolVersion: null == protocolVersion ? _self.protocolVersion : protocolVersion // ignore: cast_nullable_to_non_nullable
as String,capabilities: null == capabilities ? _self.capabilities : capabilities // ignore: cast_nullable_to_non_nullable
as McpCapabilities,clientInfo: null == clientInfo ? _self.clientInfo : clientInfo // ignore: cast_nullable_to_non_nullable
as McpClientInfo,
  ));
}
/// Create a copy of McpInitializeParams
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$McpCapabilitiesCopyWith<$Res> get capabilities {
  
  return $McpCapabilitiesCopyWith<$Res>(_self.capabilities, (value) {
    return _then(_self.copyWith(capabilities: value));
  });
}/// Create a copy of McpInitializeParams
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$McpClientInfoCopyWith<$Res> get clientInfo {
  
  return $McpClientInfoCopyWith<$Res>(_self.clientInfo, (value) {
    return _then(_self.copyWith(clientInfo: value));
  });
}
}


/// Adds pattern-matching-related methods to [McpInitializeParams].
extension McpInitializeParamsPatterns on McpInitializeParams {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _McpInitializeParams value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _McpInitializeParams() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _McpInitializeParams value)  $default,){
final _that = this;
switch (_that) {
case _McpInitializeParams():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _McpInitializeParams value)?  $default,){
final _that = this;
switch (_that) {
case _McpInitializeParams() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String protocolVersion,  McpCapabilities capabilities,  McpClientInfo clientInfo)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _McpInitializeParams() when $default != null:
return $default(_that.protocolVersion,_that.capabilities,_that.clientInfo);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String protocolVersion,  McpCapabilities capabilities,  McpClientInfo clientInfo)  $default,) {final _that = this;
switch (_that) {
case _McpInitializeParams():
return $default(_that.protocolVersion,_that.capabilities,_that.clientInfo);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String protocolVersion,  McpCapabilities capabilities,  McpClientInfo clientInfo)?  $default,) {final _that = this;
switch (_that) {
case _McpInitializeParams() when $default != null:
return $default(_that.protocolVersion,_that.capabilities,_that.clientInfo);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _McpInitializeParams implements McpInitializeParams {
  const _McpInitializeParams({required this.protocolVersion, required this.capabilities, required this.clientInfo});
  factory _McpInitializeParams.fromJson(Map<String, dynamic> json) => _$McpInitializeParamsFromJson(json);

@override final  String protocolVersion;
@override final  McpCapabilities capabilities;
@override final  McpClientInfo clientInfo;

/// Create a copy of McpInitializeParams
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$McpInitializeParamsCopyWith<_McpInitializeParams> get copyWith => __$McpInitializeParamsCopyWithImpl<_McpInitializeParams>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$McpInitializeParamsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _McpInitializeParams&&(identical(other.protocolVersion, protocolVersion) || other.protocolVersion == protocolVersion)&&(identical(other.capabilities, capabilities) || other.capabilities == capabilities)&&(identical(other.clientInfo, clientInfo) || other.clientInfo == clientInfo));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,protocolVersion,capabilities,clientInfo);

@override
String toString() {
  return 'McpInitializeParams(protocolVersion: $protocolVersion, capabilities: $capabilities, clientInfo: $clientInfo)';
}


}

/// @nodoc
abstract mixin class _$McpInitializeParamsCopyWith<$Res> implements $McpInitializeParamsCopyWith<$Res> {
  factory _$McpInitializeParamsCopyWith(_McpInitializeParams value, $Res Function(_McpInitializeParams) _then) = __$McpInitializeParamsCopyWithImpl;
@override @useResult
$Res call({
 String protocolVersion, McpCapabilities capabilities, McpClientInfo clientInfo
});


@override $McpCapabilitiesCopyWith<$Res> get capabilities;@override $McpClientInfoCopyWith<$Res> get clientInfo;

}
/// @nodoc
class __$McpInitializeParamsCopyWithImpl<$Res>
    implements _$McpInitializeParamsCopyWith<$Res> {
  __$McpInitializeParamsCopyWithImpl(this._self, this._then);

  final _McpInitializeParams _self;
  final $Res Function(_McpInitializeParams) _then;

/// Create a copy of McpInitializeParams
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? protocolVersion = null,Object? capabilities = null,Object? clientInfo = null,}) {
  return _then(_McpInitializeParams(
protocolVersion: null == protocolVersion ? _self.protocolVersion : protocolVersion // ignore: cast_nullable_to_non_nullable
as String,capabilities: null == capabilities ? _self.capabilities : capabilities // ignore: cast_nullable_to_non_nullable
as McpCapabilities,clientInfo: null == clientInfo ? _self.clientInfo : clientInfo // ignore: cast_nullable_to_non_nullable
as McpClientInfo,
  ));
}

/// Create a copy of McpInitializeParams
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$McpCapabilitiesCopyWith<$Res> get capabilities {
  
  return $McpCapabilitiesCopyWith<$Res>(_self.capabilities, (value) {
    return _then(_self.copyWith(capabilities: value));
  });
}/// Create a copy of McpInitializeParams
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$McpClientInfoCopyWith<$Res> get clientInfo {
  
  return $McpClientInfoCopyWith<$Res>(_self.clientInfo, (value) {
    return _then(_self.copyWith(clientInfo: value));
  });
}
}


/// @nodoc
mixin _$McpInitializeResult {

 String get protocolVersion; McpCapabilities get capabilities; McpServerInfo get serverInfo;
/// Create a copy of McpInitializeResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$McpInitializeResultCopyWith<McpInitializeResult> get copyWith => _$McpInitializeResultCopyWithImpl<McpInitializeResult>(this as McpInitializeResult, _$identity);

  /// Serializes this McpInitializeResult to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is McpInitializeResult&&(identical(other.protocolVersion, protocolVersion) || other.protocolVersion == protocolVersion)&&(identical(other.capabilities, capabilities) || other.capabilities == capabilities)&&(identical(other.serverInfo, serverInfo) || other.serverInfo == serverInfo));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,protocolVersion,capabilities,serverInfo);

@override
String toString() {
  return 'McpInitializeResult(protocolVersion: $protocolVersion, capabilities: $capabilities, serverInfo: $serverInfo)';
}


}

/// @nodoc
abstract mixin class $McpInitializeResultCopyWith<$Res>  {
  factory $McpInitializeResultCopyWith(McpInitializeResult value, $Res Function(McpInitializeResult) _then) = _$McpInitializeResultCopyWithImpl;
@useResult
$Res call({
 String protocolVersion, McpCapabilities capabilities, McpServerInfo serverInfo
});


$McpCapabilitiesCopyWith<$Res> get capabilities;$McpServerInfoCopyWith<$Res> get serverInfo;

}
/// @nodoc
class _$McpInitializeResultCopyWithImpl<$Res>
    implements $McpInitializeResultCopyWith<$Res> {
  _$McpInitializeResultCopyWithImpl(this._self, this._then);

  final McpInitializeResult _self;
  final $Res Function(McpInitializeResult) _then;

/// Create a copy of McpInitializeResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? protocolVersion = null,Object? capabilities = null,Object? serverInfo = null,}) {
  return _then(_self.copyWith(
protocolVersion: null == protocolVersion ? _self.protocolVersion : protocolVersion // ignore: cast_nullable_to_non_nullable
as String,capabilities: null == capabilities ? _self.capabilities : capabilities // ignore: cast_nullable_to_non_nullable
as McpCapabilities,serverInfo: null == serverInfo ? _self.serverInfo : serverInfo // ignore: cast_nullable_to_non_nullable
as McpServerInfo,
  ));
}
/// Create a copy of McpInitializeResult
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$McpCapabilitiesCopyWith<$Res> get capabilities {
  
  return $McpCapabilitiesCopyWith<$Res>(_self.capabilities, (value) {
    return _then(_self.copyWith(capabilities: value));
  });
}/// Create a copy of McpInitializeResult
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$McpServerInfoCopyWith<$Res> get serverInfo {
  
  return $McpServerInfoCopyWith<$Res>(_self.serverInfo, (value) {
    return _then(_self.copyWith(serverInfo: value));
  });
}
}


/// Adds pattern-matching-related methods to [McpInitializeResult].
extension McpInitializeResultPatterns on McpInitializeResult {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _McpInitializeResult value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _McpInitializeResult() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _McpInitializeResult value)  $default,){
final _that = this;
switch (_that) {
case _McpInitializeResult():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _McpInitializeResult value)?  $default,){
final _that = this;
switch (_that) {
case _McpInitializeResult() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String protocolVersion,  McpCapabilities capabilities,  McpServerInfo serverInfo)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _McpInitializeResult() when $default != null:
return $default(_that.protocolVersion,_that.capabilities,_that.serverInfo);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String protocolVersion,  McpCapabilities capabilities,  McpServerInfo serverInfo)  $default,) {final _that = this;
switch (_that) {
case _McpInitializeResult():
return $default(_that.protocolVersion,_that.capabilities,_that.serverInfo);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String protocolVersion,  McpCapabilities capabilities,  McpServerInfo serverInfo)?  $default,) {final _that = this;
switch (_that) {
case _McpInitializeResult() when $default != null:
return $default(_that.protocolVersion,_that.capabilities,_that.serverInfo);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _McpInitializeResult implements McpInitializeResult {
  const _McpInitializeResult({required this.protocolVersion, required this.capabilities, required this.serverInfo});
  factory _McpInitializeResult.fromJson(Map<String, dynamic> json) => _$McpInitializeResultFromJson(json);

@override final  String protocolVersion;
@override final  McpCapabilities capabilities;
@override final  McpServerInfo serverInfo;

/// Create a copy of McpInitializeResult
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$McpInitializeResultCopyWith<_McpInitializeResult> get copyWith => __$McpInitializeResultCopyWithImpl<_McpInitializeResult>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$McpInitializeResultToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _McpInitializeResult&&(identical(other.protocolVersion, protocolVersion) || other.protocolVersion == protocolVersion)&&(identical(other.capabilities, capabilities) || other.capabilities == capabilities)&&(identical(other.serverInfo, serverInfo) || other.serverInfo == serverInfo));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,protocolVersion,capabilities,serverInfo);

@override
String toString() {
  return 'McpInitializeResult(protocolVersion: $protocolVersion, capabilities: $capabilities, serverInfo: $serverInfo)';
}


}

/// @nodoc
abstract mixin class _$McpInitializeResultCopyWith<$Res> implements $McpInitializeResultCopyWith<$Res> {
  factory _$McpInitializeResultCopyWith(_McpInitializeResult value, $Res Function(_McpInitializeResult) _then) = __$McpInitializeResultCopyWithImpl;
@override @useResult
$Res call({
 String protocolVersion, McpCapabilities capabilities, McpServerInfo serverInfo
});


@override $McpCapabilitiesCopyWith<$Res> get capabilities;@override $McpServerInfoCopyWith<$Res> get serverInfo;

}
/// @nodoc
class __$McpInitializeResultCopyWithImpl<$Res>
    implements _$McpInitializeResultCopyWith<$Res> {
  __$McpInitializeResultCopyWithImpl(this._self, this._then);

  final _McpInitializeResult _self;
  final $Res Function(_McpInitializeResult) _then;

/// Create a copy of McpInitializeResult
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? protocolVersion = null,Object? capabilities = null,Object? serverInfo = null,}) {
  return _then(_McpInitializeResult(
protocolVersion: null == protocolVersion ? _self.protocolVersion : protocolVersion // ignore: cast_nullable_to_non_nullable
as String,capabilities: null == capabilities ? _self.capabilities : capabilities // ignore: cast_nullable_to_non_nullable
as McpCapabilities,serverInfo: null == serverInfo ? _self.serverInfo : serverInfo // ignore: cast_nullable_to_non_nullable
as McpServerInfo,
  ));
}

/// Create a copy of McpInitializeResult
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$McpCapabilitiesCopyWith<$Res> get capabilities {
  
  return $McpCapabilitiesCopyWith<$Res>(_self.capabilities, (value) {
    return _then(_self.copyWith(capabilities: value));
  });
}/// Create a copy of McpInitializeResult
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$McpServerInfoCopyWith<$Res> get serverInfo {
  
  return $McpServerInfoCopyWith<$Res>(_self.serverInfo, (value) {
    return _then(_self.copyWith(serverInfo: value));
  });
}
}


/// @nodoc
mixin _$McpTool {

 String get name; String? get description; Map<String, dynamic> get inputSchema;
/// Create a copy of McpTool
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$McpToolCopyWith<McpTool> get copyWith => _$McpToolCopyWithImpl<McpTool>(this as McpTool, _$identity);

  /// Serializes this McpTool to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is McpTool&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&const DeepCollectionEquality().equals(other.inputSchema, inputSchema));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,description,const DeepCollectionEquality().hash(inputSchema));

@override
String toString() {
  return 'McpTool(name: $name, description: $description, inputSchema: $inputSchema)';
}


}

/// @nodoc
abstract mixin class $McpToolCopyWith<$Res>  {
  factory $McpToolCopyWith(McpTool value, $Res Function(McpTool) _then) = _$McpToolCopyWithImpl;
@useResult
$Res call({
 String name, String? description, Map<String, dynamic> inputSchema
});




}
/// @nodoc
class _$McpToolCopyWithImpl<$Res>
    implements $McpToolCopyWith<$Res> {
  _$McpToolCopyWithImpl(this._self, this._then);

  final McpTool _self;
  final $Res Function(McpTool) _then;

/// Create a copy of McpTool
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = null,Object? description = freezed,Object? inputSchema = null,}) {
  return _then(_self.copyWith(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,inputSchema: null == inputSchema ? _self.inputSchema : inputSchema // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}

}


/// Adds pattern-matching-related methods to [McpTool].
extension McpToolPatterns on McpTool {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _McpTool value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _McpTool() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _McpTool value)  $default,){
final _that = this;
switch (_that) {
case _McpTool():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _McpTool value)?  $default,){
final _that = this;
switch (_that) {
case _McpTool() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String name,  String? description,  Map<String, dynamic> inputSchema)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _McpTool() when $default != null:
return $default(_that.name,_that.description,_that.inputSchema);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String name,  String? description,  Map<String, dynamic> inputSchema)  $default,) {final _that = this;
switch (_that) {
case _McpTool():
return $default(_that.name,_that.description,_that.inputSchema);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String name,  String? description,  Map<String, dynamic> inputSchema)?  $default,) {final _that = this;
switch (_that) {
case _McpTool() when $default != null:
return $default(_that.name,_that.description,_that.inputSchema);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _McpTool implements McpTool {
  const _McpTool({required this.name, this.description, required final  Map<String, dynamic> inputSchema}): _inputSchema = inputSchema;
  factory _McpTool.fromJson(Map<String, dynamic> json) => _$McpToolFromJson(json);

@override final  String name;
@override final  String? description;
 final  Map<String, dynamic> _inputSchema;
@override Map<String, dynamic> get inputSchema {
  if (_inputSchema is EqualUnmodifiableMapView) return _inputSchema;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_inputSchema);
}


/// Create a copy of McpTool
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$McpToolCopyWith<_McpTool> get copyWith => __$McpToolCopyWithImpl<_McpTool>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$McpToolToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _McpTool&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&const DeepCollectionEquality().equals(other._inputSchema, _inputSchema));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,description,const DeepCollectionEquality().hash(_inputSchema));

@override
String toString() {
  return 'McpTool(name: $name, description: $description, inputSchema: $inputSchema)';
}


}

/// @nodoc
abstract mixin class _$McpToolCopyWith<$Res> implements $McpToolCopyWith<$Res> {
  factory _$McpToolCopyWith(_McpTool value, $Res Function(_McpTool) _then) = __$McpToolCopyWithImpl;
@override @useResult
$Res call({
 String name, String? description, Map<String, dynamic> inputSchema
});




}
/// @nodoc
class __$McpToolCopyWithImpl<$Res>
    implements _$McpToolCopyWith<$Res> {
  __$McpToolCopyWithImpl(this._self, this._then);

  final _McpTool _self;
  final $Res Function(_McpTool) _then;

/// Create a copy of McpTool
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = null,Object? description = freezed,Object? inputSchema = null,}) {
  return _then(_McpTool(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,inputSchema: null == inputSchema ? _self._inputSchema : inputSchema // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}


}


/// @nodoc
mixin _$McpToolsListParams {

 String? get cursor;
/// Create a copy of McpToolsListParams
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$McpToolsListParamsCopyWith<McpToolsListParams> get copyWith => _$McpToolsListParamsCopyWithImpl<McpToolsListParams>(this as McpToolsListParams, _$identity);

  /// Serializes this McpToolsListParams to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is McpToolsListParams&&(identical(other.cursor, cursor) || other.cursor == cursor));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,cursor);

@override
String toString() {
  return 'McpToolsListParams(cursor: $cursor)';
}


}

/// @nodoc
abstract mixin class $McpToolsListParamsCopyWith<$Res>  {
  factory $McpToolsListParamsCopyWith(McpToolsListParams value, $Res Function(McpToolsListParams) _then) = _$McpToolsListParamsCopyWithImpl;
@useResult
$Res call({
 String? cursor
});




}
/// @nodoc
class _$McpToolsListParamsCopyWithImpl<$Res>
    implements $McpToolsListParamsCopyWith<$Res> {
  _$McpToolsListParamsCopyWithImpl(this._self, this._then);

  final McpToolsListParams _self;
  final $Res Function(McpToolsListParams) _then;

/// Create a copy of McpToolsListParams
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? cursor = freezed,}) {
  return _then(_self.copyWith(
cursor: freezed == cursor ? _self.cursor : cursor // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [McpToolsListParams].
extension McpToolsListParamsPatterns on McpToolsListParams {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _McpToolsListParams value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _McpToolsListParams() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _McpToolsListParams value)  $default,){
final _that = this;
switch (_that) {
case _McpToolsListParams():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _McpToolsListParams value)?  $default,){
final _that = this;
switch (_that) {
case _McpToolsListParams() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String? cursor)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _McpToolsListParams() when $default != null:
return $default(_that.cursor);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String? cursor)  $default,) {final _that = this;
switch (_that) {
case _McpToolsListParams():
return $default(_that.cursor);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String? cursor)?  $default,) {final _that = this;
switch (_that) {
case _McpToolsListParams() when $default != null:
return $default(_that.cursor);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _McpToolsListParams implements McpToolsListParams {
  const _McpToolsListParams({this.cursor});
  factory _McpToolsListParams.fromJson(Map<String, dynamic> json) => _$McpToolsListParamsFromJson(json);

@override final  String? cursor;

/// Create a copy of McpToolsListParams
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$McpToolsListParamsCopyWith<_McpToolsListParams> get copyWith => __$McpToolsListParamsCopyWithImpl<_McpToolsListParams>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$McpToolsListParamsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _McpToolsListParams&&(identical(other.cursor, cursor) || other.cursor == cursor));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,cursor);

@override
String toString() {
  return 'McpToolsListParams(cursor: $cursor)';
}


}

/// @nodoc
abstract mixin class _$McpToolsListParamsCopyWith<$Res> implements $McpToolsListParamsCopyWith<$Res> {
  factory _$McpToolsListParamsCopyWith(_McpToolsListParams value, $Res Function(_McpToolsListParams) _then) = __$McpToolsListParamsCopyWithImpl;
@override @useResult
$Res call({
 String? cursor
});




}
/// @nodoc
class __$McpToolsListParamsCopyWithImpl<$Res>
    implements _$McpToolsListParamsCopyWith<$Res> {
  __$McpToolsListParamsCopyWithImpl(this._self, this._then);

  final _McpToolsListParams _self;
  final $Res Function(_McpToolsListParams) _then;

/// Create a copy of McpToolsListParams
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? cursor = freezed,}) {
  return _then(_McpToolsListParams(
cursor: freezed == cursor ? _self.cursor : cursor // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$McpToolsListResult {

 List<McpTool> get tools; String? get nextCursor;
/// Create a copy of McpToolsListResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$McpToolsListResultCopyWith<McpToolsListResult> get copyWith => _$McpToolsListResultCopyWithImpl<McpToolsListResult>(this as McpToolsListResult, _$identity);

  /// Serializes this McpToolsListResult to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is McpToolsListResult&&const DeepCollectionEquality().equals(other.tools, tools)&&(identical(other.nextCursor, nextCursor) || other.nextCursor == nextCursor));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(tools),nextCursor);

@override
String toString() {
  return 'McpToolsListResult(tools: $tools, nextCursor: $nextCursor)';
}


}

/// @nodoc
abstract mixin class $McpToolsListResultCopyWith<$Res>  {
  factory $McpToolsListResultCopyWith(McpToolsListResult value, $Res Function(McpToolsListResult) _then) = _$McpToolsListResultCopyWithImpl;
@useResult
$Res call({
 List<McpTool> tools, String? nextCursor
});




}
/// @nodoc
class _$McpToolsListResultCopyWithImpl<$Res>
    implements $McpToolsListResultCopyWith<$Res> {
  _$McpToolsListResultCopyWithImpl(this._self, this._then);

  final McpToolsListResult _self;
  final $Res Function(McpToolsListResult) _then;

/// Create a copy of McpToolsListResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? tools = null,Object? nextCursor = freezed,}) {
  return _then(_self.copyWith(
tools: null == tools ? _self.tools : tools // ignore: cast_nullable_to_non_nullable
as List<McpTool>,nextCursor: freezed == nextCursor ? _self.nextCursor : nextCursor // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [McpToolsListResult].
extension McpToolsListResultPatterns on McpToolsListResult {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _McpToolsListResult value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _McpToolsListResult() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _McpToolsListResult value)  $default,){
final _that = this;
switch (_that) {
case _McpToolsListResult():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _McpToolsListResult value)?  $default,){
final _that = this;
switch (_that) {
case _McpToolsListResult() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<McpTool> tools,  String? nextCursor)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _McpToolsListResult() when $default != null:
return $default(_that.tools,_that.nextCursor);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<McpTool> tools,  String? nextCursor)  $default,) {final _that = this;
switch (_that) {
case _McpToolsListResult():
return $default(_that.tools,_that.nextCursor);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<McpTool> tools,  String? nextCursor)?  $default,) {final _that = this;
switch (_that) {
case _McpToolsListResult() when $default != null:
return $default(_that.tools,_that.nextCursor);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _McpToolsListResult implements McpToolsListResult {
  const _McpToolsListResult({required final  List<McpTool> tools, this.nextCursor}): _tools = tools;
  factory _McpToolsListResult.fromJson(Map<String, dynamic> json) => _$McpToolsListResultFromJson(json);

 final  List<McpTool> _tools;
@override List<McpTool> get tools {
  if (_tools is EqualUnmodifiableListView) return _tools;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_tools);
}

@override final  String? nextCursor;

/// Create a copy of McpToolsListResult
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$McpToolsListResultCopyWith<_McpToolsListResult> get copyWith => __$McpToolsListResultCopyWithImpl<_McpToolsListResult>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$McpToolsListResultToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _McpToolsListResult&&const DeepCollectionEquality().equals(other._tools, _tools)&&(identical(other.nextCursor, nextCursor) || other.nextCursor == nextCursor));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_tools),nextCursor);

@override
String toString() {
  return 'McpToolsListResult(tools: $tools, nextCursor: $nextCursor)';
}


}

/// @nodoc
abstract mixin class _$McpToolsListResultCopyWith<$Res> implements $McpToolsListResultCopyWith<$Res> {
  factory _$McpToolsListResultCopyWith(_McpToolsListResult value, $Res Function(_McpToolsListResult) _then) = __$McpToolsListResultCopyWithImpl;
@override @useResult
$Res call({
 List<McpTool> tools, String? nextCursor
});




}
/// @nodoc
class __$McpToolsListResultCopyWithImpl<$Res>
    implements _$McpToolsListResultCopyWith<$Res> {
  __$McpToolsListResultCopyWithImpl(this._self, this._then);

  final _McpToolsListResult _self;
  final $Res Function(_McpToolsListResult) _then;

/// Create a copy of McpToolsListResult
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? tools = null,Object? nextCursor = freezed,}) {
  return _then(_McpToolsListResult(
tools: null == tools ? _self._tools : tools // ignore: cast_nullable_to_non_nullable
as List<McpTool>,nextCursor: freezed == nextCursor ? _self.nextCursor : nextCursor // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$McpToolCallParams {

 String get name; Map<String, dynamic>? get arguments;
/// Create a copy of McpToolCallParams
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$McpToolCallParamsCopyWith<McpToolCallParams> get copyWith => _$McpToolCallParamsCopyWithImpl<McpToolCallParams>(this as McpToolCallParams, _$identity);

  /// Serializes this McpToolCallParams to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is McpToolCallParams&&(identical(other.name, name) || other.name == name)&&const DeepCollectionEquality().equals(other.arguments, arguments));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,const DeepCollectionEquality().hash(arguments));

@override
String toString() {
  return 'McpToolCallParams(name: $name, arguments: $arguments)';
}


}

/// @nodoc
abstract mixin class $McpToolCallParamsCopyWith<$Res>  {
  factory $McpToolCallParamsCopyWith(McpToolCallParams value, $Res Function(McpToolCallParams) _then) = _$McpToolCallParamsCopyWithImpl;
@useResult
$Res call({
 String name, Map<String, dynamic>? arguments
});




}
/// @nodoc
class _$McpToolCallParamsCopyWithImpl<$Res>
    implements $McpToolCallParamsCopyWith<$Res> {
  _$McpToolCallParamsCopyWithImpl(this._self, this._then);

  final McpToolCallParams _self;
  final $Res Function(McpToolCallParams) _then;

/// Create a copy of McpToolCallParams
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = null,Object? arguments = freezed,}) {
  return _then(_self.copyWith(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,arguments: freezed == arguments ? _self.arguments : arguments // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [McpToolCallParams].
extension McpToolCallParamsPatterns on McpToolCallParams {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _McpToolCallParams value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _McpToolCallParams() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _McpToolCallParams value)  $default,){
final _that = this;
switch (_that) {
case _McpToolCallParams():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _McpToolCallParams value)?  $default,){
final _that = this;
switch (_that) {
case _McpToolCallParams() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String name,  Map<String, dynamic>? arguments)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _McpToolCallParams() when $default != null:
return $default(_that.name,_that.arguments);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String name,  Map<String, dynamic>? arguments)  $default,) {final _that = this;
switch (_that) {
case _McpToolCallParams():
return $default(_that.name,_that.arguments);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String name,  Map<String, dynamic>? arguments)?  $default,) {final _that = this;
switch (_that) {
case _McpToolCallParams() when $default != null:
return $default(_that.name,_that.arguments);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _McpToolCallParams implements McpToolCallParams {
  const _McpToolCallParams({required this.name, final  Map<String, dynamic>? arguments}): _arguments = arguments;
  factory _McpToolCallParams.fromJson(Map<String, dynamic> json) => _$McpToolCallParamsFromJson(json);

@override final  String name;
 final  Map<String, dynamic>? _arguments;
@override Map<String, dynamic>? get arguments {
  final value = _arguments;
  if (value == null) return null;
  if (_arguments is EqualUnmodifiableMapView) return _arguments;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of McpToolCallParams
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$McpToolCallParamsCopyWith<_McpToolCallParams> get copyWith => __$McpToolCallParamsCopyWithImpl<_McpToolCallParams>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$McpToolCallParamsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _McpToolCallParams&&(identical(other.name, name) || other.name == name)&&const DeepCollectionEquality().equals(other._arguments, _arguments));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,const DeepCollectionEquality().hash(_arguments));

@override
String toString() {
  return 'McpToolCallParams(name: $name, arguments: $arguments)';
}


}

/// @nodoc
abstract mixin class _$McpToolCallParamsCopyWith<$Res> implements $McpToolCallParamsCopyWith<$Res> {
  factory _$McpToolCallParamsCopyWith(_McpToolCallParams value, $Res Function(_McpToolCallParams) _then) = __$McpToolCallParamsCopyWithImpl;
@override @useResult
$Res call({
 String name, Map<String, dynamic>? arguments
});




}
/// @nodoc
class __$McpToolCallParamsCopyWithImpl<$Res>
    implements _$McpToolCallParamsCopyWith<$Res> {
  __$McpToolCallParamsCopyWithImpl(this._self, this._then);

  final _McpToolCallParams _self;
  final $Res Function(_McpToolCallParams) _then;

/// Create a copy of McpToolCallParams
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = null,Object? arguments = freezed,}) {
  return _then(_McpToolCallParams(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,arguments: freezed == arguments ? _self._arguments : arguments // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$McpToolCallResult {

@JsonKey(includeIfNull: false) List<McpTextContent>? get content; bool get isError;
/// Create a copy of McpToolCallResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$McpToolCallResultCopyWith<McpToolCallResult> get copyWith => _$McpToolCallResultCopyWithImpl<McpToolCallResult>(this as McpToolCallResult, _$identity);

  /// Serializes this McpToolCallResult to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is McpToolCallResult&&const DeepCollectionEquality().equals(other.content, content)&&(identical(other.isError, isError) || other.isError == isError));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(content),isError);

@override
String toString() {
  return 'McpToolCallResult(content: $content, isError: $isError)';
}


}

/// @nodoc
abstract mixin class $McpToolCallResultCopyWith<$Res>  {
  factory $McpToolCallResultCopyWith(McpToolCallResult value, $Res Function(McpToolCallResult) _then) = _$McpToolCallResultCopyWithImpl;
@useResult
$Res call({
@JsonKey(includeIfNull: false) List<McpTextContent>? content, bool isError
});




}
/// @nodoc
class _$McpToolCallResultCopyWithImpl<$Res>
    implements $McpToolCallResultCopyWith<$Res> {
  _$McpToolCallResultCopyWithImpl(this._self, this._then);

  final McpToolCallResult _self;
  final $Res Function(McpToolCallResult) _then;

/// Create a copy of McpToolCallResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? content = freezed,Object? isError = null,}) {
  return _then(_self.copyWith(
content: freezed == content ? _self.content : content // ignore: cast_nullable_to_non_nullable
as List<McpTextContent>?,isError: null == isError ? _self.isError : isError // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [McpToolCallResult].
extension McpToolCallResultPatterns on McpToolCallResult {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _McpToolCallResult value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _McpToolCallResult() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _McpToolCallResult value)  $default,){
final _that = this;
switch (_that) {
case _McpToolCallResult():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _McpToolCallResult value)?  $default,){
final _that = this;
switch (_that) {
case _McpToolCallResult() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(includeIfNull: false)  List<McpTextContent>? content,  bool isError)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _McpToolCallResult() when $default != null:
return $default(_that.content,_that.isError);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(includeIfNull: false)  List<McpTextContent>? content,  bool isError)  $default,) {final _that = this;
switch (_that) {
case _McpToolCallResult():
return $default(_that.content,_that.isError);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(includeIfNull: false)  List<McpTextContent>? content,  bool isError)?  $default,) {final _that = this;
switch (_that) {
case _McpToolCallResult() when $default != null:
return $default(_that.content,_that.isError);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _McpToolCallResult implements McpToolCallResult {
  const _McpToolCallResult({@JsonKey(includeIfNull: false) final  List<McpTextContent>? content, this.isError = false}): _content = content;
  factory _McpToolCallResult.fromJson(Map<String, dynamic> json) => _$McpToolCallResultFromJson(json);

 final  List<McpTextContent>? _content;
@override@JsonKey(includeIfNull: false) List<McpTextContent>? get content {
  final value = _content;
  if (value == null) return null;
  if (_content is EqualUnmodifiableListView) return _content;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override@JsonKey() final  bool isError;

/// Create a copy of McpToolCallResult
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$McpToolCallResultCopyWith<_McpToolCallResult> get copyWith => __$McpToolCallResultCopyWithImpl<_McpToolCallResult>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$McpToolCallResultToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _McpToolCallResult&&const DeepCollectionEquality().equals(other._content, _content)&&(identical(other.isError, isError) || other.isError == isError));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_content),isError);

@override
String toString() {
  return 'McpToolCallResult(content: $content, isError: $isError)';
}


}

/// @nodoc
abstract mixin class _$McpToolCallResultCopyWith<$Res> implements $McpToolCallResultCopyWith<$Res> {
  factory _$McpToolCallResultCopyWith(_McpToolCallResult value, $Res Function(_McpToolCallResult) _then) = __$McpToolCallResultCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(includeIfNull: false) List<McpTextContent>? content, bool isError
});




}
/// @nodoc
class __$McpToolCallResultCopyWithImpl<$Res>
    implements _$McpToolCallResultCopyWith<$Res> {
  __$McpToolCallResultCopyWithImpl(this._self, this._then);

  final _McpToolCallResult _self;
  final $Res Function(_McpToolCallResult) _then;

/// Create a copy of McpToolCallResult
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? content = freezed,Object? isError = null,}) {
  return _then(_McpToolCallResult(
content: freezed == content ? _self._content : content // ignore: cast_nullable_to_non_nullable
as List<McpTextContent>?,isError: null == isError ? _self.isError : isError // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$McpTextContent {

 String get type; String get text;
/// Create a copy of McpTextContent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$McpTextContentCopyWith<McpTextContent> get copyWith => _$McpTextContentCopyWithImpl<McpTextContent>(this as McpTextContent, _$identity);

  /// Serializes this McpTextContent to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is McpTextContent&&(identical(other.type, type) || other.type == type)&&(identical(other.text, text) || other.text == text));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,type,text);

@override
String toString() {
  return 'McpTextContent(type: $type, text: $text)';
}


}

/// @nodoc
abstract mixin class $McpTextContentCopyWith<$Res>  {
  factory $McpTextContentCopyWith(McpTextContent value, $Res Function(McpTextContent) _then) = _$McpTextContentCopyWithImpl;
@useResult
$Res call({
 String type, String text
});




}
/// @nodoc
class _$McpTextContentCopyWithImpl<$Res>
    implements $McpTextContentCopyWith<$Res> {
  _$McpTextContentCopyWithImpl(this._self, this._then);

  final McpTextContent _self;
  final $Res Function(McpTextContent) _then;

/// Create a copy of McpTextContent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? type = null,Object? text = null,}) {
  return _then(_self.copyWith(
type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String,text: null == text ? _self.text : text // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [McpTextContent].
extension McpTextContentPatterns on McpTextContent {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _McpTextContent value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _McpTextContent() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _McpTextContent value)  $default,){
final _that = this;
switch (_that) {
case _McpTextContent():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _McpTextContent value)?  $default,){
final _that = this;
switch (_that) {
case _McpTextContent() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String type,  String text)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _McpTextContent() when $default != null:
return $default(_that.type,_that.text);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String type,  String text)  $default,) {final _that = this;
switch (_that) {
case _McpTextContent():
return $default(_that.type,_that.text);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String type,  String text)?  $default,) {final _that = this;
switch (_that) {
case _McpTextContent() when $default != null:
return $default(_that.type,_that.text);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _McpTextContent implements McpTextContent {
  const _McpTextContent({required this.type, required this.text});
  factory _McpTextContent.fromJson(Map<String, dynamic> json) => _$McpTextContentFromJson(json);

@override final  String type;
@override final  String text;

/// Create a copy of McpTextContent
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$McpTextContentCopyWith<_McpTextContent> get copyWith => __$McpTextContentCopyWithImpl<_McpTextContent>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$McpTextContentToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _McpTextContent&&(identical(other.type, type) || other.type == type)&&(identical(other.text, text) || other.text == text));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,type,text);

@override
String toString() {
  return 'McpTextContent(type: $type, text: $text)';
}


}

/// @nodoc
abstract mixin class _$McpTextContentCopyWith<$Res> implements $McpTextContentCopyWith<$Res> {
  factory _$McpTextContentCopyWith(_McpTextContent value, $Res Function(_McpTextContent) _then) = __$McpTextContentCopyWithImpl;
@override @useResult
$Res call({
 String type, String text
});




}
/// @nodoc
class __$McpTextContentCopyWithImpl<$Res>
    implements _$McpTextContentCopyWith<$Res> {
  __$McpTextContentCopyWithImpl(this._self, this._then);

  final _McpTextContent _self;
  final $Res Function(_McpTextContent) _then;

/// Create a copy of McpTextContent
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? type = null,Object? text = null,}) {
  return _then(_McpTextContent(
type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String,text: null == text ? _self.text : text // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$McpResource {

 String get uri; String get name; String? get description; String? get mimeType;
/// Create a copy of McpResource
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$McpResourceCopyWith<McpResource> get copyWith => _$McpResourceCopyWithImpl<McpResource>(this as McpResource, _$identity);

  /// Serializes this McpResource to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is McpResource&&(identical(other.uri, uri) || other.uri == uri)&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&(identical(other.mimeType, mimeType) || other.mimeType == mimeType));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,uri,name,description,mimeType);

@override
String toString() {
  return 'McpResource(uri: $uri, name: $name, description: $description, mimeType: $mimeType)';
}


}

/// @nodoc
abstract mixin class $McpResourceCopyWith<$Res>  {
  factory $McpResourceCopyWith(McpResource value, $Res Function(McpResource) _then) = _$McpResourceCopyWithImpl;
@useResult
$Res call({
 String uri, String name, String? description, String? mimeType
});




}
/// @nodoc
class _$McpResourceCopyWithImpl<$Res>
    implements $McpResourceCopyWith<$Res> {
  _$McpResourceCopyWithImpl(this._self, this._then);

  final McpResource _self;
  final $Res Function(McpResource) _then;

/// Create a copy of McpResource
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? uri = null,Object? name = null,Object? description = freezed,Object? mimeType = freezed,}) {
  return _then(_self.copyWith(
uri: null == uri ? _self.uri : uri // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,mimeType: freezed == mimeType ? _self.mimeType : mimeType // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [McpResource].
extension McpResourcePatterns on McpResource {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _McpResource value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _McpResource() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _McpResource value)  $default,){
final _that = this;
switch (_that) {
case _McpResource():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _McpResource value)?  $default,){
final _that = this;
switch (_that) {
case _McpResource() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String uri,  String name,  String? description,  String? mimeType)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _McpResource() when $default != null:
return $default(_that.uri,_that.name,_that.description,_that.mimeType);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String uri,  String name,  String? description,  String? mimeType)  $default,) {final _that = this;
switch (_that) {
case _McpResource():
return $default(_that.uri,_that.name,_that.description,_that.mimeType);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String uri,  String name,  String? description,  String? mimeType)?  $default,) {final _that = this;
switch (_that) {
case _McpResource() when $default != null:
return $default(_that.uri,_that.name,_that.description,_that.mimeType);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _McpResource implements McpResource {
  const _McpResource({required this.uri, required this.name, this.description, this.mimeType});
  factory _McpResource.fromJson(Map<String, dynamic> json) => _$McpResourceFromJson(json);

@override final  String uri;
@override final  String name;
@override final  String? description;
@override final  String? mimeType;

/// Create a copy of McpResource
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$McpResourceCopyWith<_McpResource> get copyWith => __$McpResourceCopyWithImpl<_McpResource>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$McpResourceToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _McpResource&&(identical(other.uri, uri) || other.uri == uri)&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&(identical(other.mimeType, mimeType) || other.mimeType == mimeType));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,uri,name,description,mimeType);

@override
String toString() {
  return 'McpResource(uri: $uri, name: $name, description: $description, mimeType: $mimeType)';
}


}

/// @nodoc
abstract mixin class _$McpResourceCopyWith<$Res> implements $McpResourceCopyWith<$Res> {
  factory _$McpResourceCopyWith(_McpResource value, $Res Function(_McpResource) _then) = __$McpResourceCopyWithImpl;
@override @useResult
$Res call({
 String uri, String name, String? description, String? mimeType
});




}
/// @nodoc
class __$McpResourceCopyWithImpl<$Res>
    implements _$McpResourceCopyWith<$Res> {
  __$McpResourceCopyWithImpl(this._self, this._then);

  final _McpResource _self;
  final $Res Function(_McpResource) _then;

/// Create a copy of McpResource
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? uri = null,Object? name = null,Object? description = freezed,Object? mimeType = freezed,}) {
  return _then(_McpResource(
uri: null == uri ? _self.uri : uri // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,mimeType: freezed == mimeType ? _self.mimeType : mimeType // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$McpResourcesListParams {

 String? get cursor;
/// Create a copy of McpResourcesListParams
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$McpResourcesListParamsCopyWith<McpResourcesListParams> get copyWith => _$McpResourcesListParamsCopyWithImpl<McpResourcesListParams>(this as McpResourcesListParams, _$identity);

  /// Serializes this McpResourcesListParams to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is McpResourcesListParams&&(identical(other.cursor, cursor) || other.cursor == cursor));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,cursor);

@override
String toString() {
  return 'McpResourcesListParams(cursor: $cursor)';
}


}

/// @nodoc
abstract mixin class $McpResourcesListParamsCopyWith<$Res>  {
  factory $McpResourcesListParamsCopyWith(McpResourcesListParams value, $Res Function(McpResourcesListParams) _then) = _$McpResourcesListParamsCopyWithImpl;
@useResult
$Res call({
 String? cursor
});




}
/// @nodoc
class _$McpResourcesListParamsCopyWithImpl<$Res>
    implements $McpResourcesListParamsCopyWith<$Res> {
  _$McpResourcesListParamsCopyWithImpl(this._self, this._then);

  final McpResourcesListParams _self;
  final $Res Function(McpResourcesListParams) _then;

/// Create a copy of McpResourcesListParams
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? cursor = freezed,}) {
  return _then(_self.copyWith(
cursor: freezed == cursor ? _self.cursor : cursor // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [McpResourcesListParams].
extension McpResourcesListParamsPatterns on McpResourcesListParams {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _McpResourcesListParams value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _McpResourcesListParams() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _McpResourcesListParams value)  $default,){
final _that = this;
switch (_that) {
case _McpResourcesListParams():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _McpResourcesListParams value)?  $default,){
final _that = this;
switch (_that) {
case _McpResourcesListParams() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String? cursor)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _McpResourcesListParams() when $default != null:
return $default(_that.cursor);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String? cursor)  $default,) {final _that = this;
switch (_that) {
case _McpResourcesListParams():
return $default(_that.cursor);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String? cursor)?  $default,) {final _that = this;
switch (_that) {
case _McpResourcesListParams() when $default != null:
return $default(_that.cursor);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _McpResourcesListParams implements McpResourcesListParams {
  const _McpResourcesListParams({this.cursor});
  factory _McpResourcesListParams.fromJson(Map<String, dynamic> json) => _$McpResourcesListParamsFromJson(json);

@override final  String? cursor;

/// Create a copy of McpResourcesListParams
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$McpResourcesListParamsCopyWith<_McpResourcesListParams> get copyWith => __$McpResourcesListParamsCopyWithImpl<_McpResourcesListParams>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$McpResourcesListParamsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _McpResourcesListParams&&(identical(other.cursor, cursor) || other.cursor == cursor));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,cursor);

@override
String toString() {
  return 'McpResourcesListParams(cursor: $cursor)';
}


}

/// @nodoc
abstract mixin class _$McpResourcesListParamsCopyWith<$Res> implements $McpResourcesListParamsCopyWith<$Res> {
  factory _$McpResourcesListParamsCopyWith(_McpResourcesListParams value, $Res Function(_McpResourcesListParams) _then) = __$McpResourcesListParamsCopyWithImpl;
@override @useResult
$Res call({
 String? cursor
});




}
/// @nodoc
class __$McpResourcesListParamsCopyWithImpl<$Res>
    implements _$McpResourcesListParamsCopyWith<$Res> {
  __$McpResourcesListParamsCopyWithImpl(this._self, this._then);

  final _McpResourcesListParams _self;
  final $Res Function(_McpResourcesListParams) _then;

/// Create a copy of McpResourcesListParams
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? cursor = freezed,}) {
  return _then(_McpResourcesListParams(
cursor: freezed == cursor ? _self.cursor : cursor // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$McpResourcesListResult {

 List<McpResource> get resources; String? get nextCursor;
/// Create a copy of McpResourcesListResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$McpResourcesListResultCopyWith<McpResourcesListResult> get copyWith => _$McpResourcesListResultCopyWithImpl<McpResourcesListResult>(this as McpResourcesListResult, _$identity);

  /// Serializes this McpResourcesListResult to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is McpResourcesListResult&&const DeepCollectionEquality().equals(other.resources, resources)&&(identical(other.nextCursor, nextCursor) || other.nextCursor == nextCursor));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(resources),nextCursor);

@override
String toString() {
  return 'McpResourcesListResult(resources: $resources, nextCursor: $nextCursor)';
}


}

/// @nodoc
abstract mixin class $McpResourcesListResultCopyWith<$Res>  {
  factory $McpResourcesListResultCopyWith(McpResourcesListResult value, $Res Function(McpResourcesListResult) _then) = _$McpResourcesListResultCopyWithImpl;
@useResult
$Res call({
 List<McpResource> resources, String? nextCursor
});




}
/// @nodoc
class _$McpResourcesListResultCopyWithImpl<$Res>
    implements $McpResourcesListResultCopyWith<$Res> {
  _$McpResourcesListResultCopyWithImpl(this._self, this._then);

  final McpResourcesListResult _self;
  final $Res Function(McpResourcesListResult) _then;

/// Create a copy of McpResourcesListResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? resources = null,Object? nextCursor = freezed,}) {
  return _then(_self.copyWith(
resources: null == resources ? _self.resources : resources // ignore: cast_nullable_to_non_nullable
as List<McpResource>,nextCursor: freezed == nextCursor ? _self.nextCursor : nextCursor // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [McpResourcesListResult].
extension McpResourcesListResultPatterns on McpResourcesListResult {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _McpResourcesListResult value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _McpResourcesListResult() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _McpResourcesListResult value)  $default,){
final _that = this;
switch (_that) {
case _McpResourcesListResult():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _McpResourcesListResult value)?  $default,){
final _that = this;
switch (_that) {
case _McpResourcesListResult() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<McpResource> resources,  String? nextCursor)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _McpResourcesListResult() when $default != null:
return $default(_that.resources,_that.nextCursor);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<McpResource> resources,  String? nextCursor)  $default,) {final _that = this;
switch (_that) {
case _McpResourcesListResult():
return $default(_that.resources,_that.nextCursor);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<McpResource> resources,  String? nextCursor)?  $default,) {final _that = this;
switch (_that) {
case _McpResourcesListResult() when $default != null:
return $default(_that.resources,_that.nextCursor);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _McpResourcesListResult implements McpResourcesListResult {
  const _McpResourcesListResult({required final  List<McpResource> resources, this.nextCursor}): _resources = resources;
  factory _McpResourcesListResult.fromJson(Map<String, dynamic> json) => _$McpResourcesListResultFromJson(json);

 final  List<McpResource> _resources;
@override List<McpResource> get resources {
  if (_resources is EqualUnmodifiableListView) return _resources;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_resources);
}

@override final  String? nextCursor;

/// Create a copy of McpResourcesListResult
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$McpResourcesListResultCopyWith<_McpResourcesListResult> get copyWith => __$McpResourcesListResultCopyWithImpl<_McpResourcesListResult>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$McpResourcesListResultToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _McpResourcesListResult&&const DeepCollectionEquality().equals(other._resources, _resources)&&(identical(other.nextCursor, nextCursor) || other.nextCursor == nextCursor));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_resources),nextCursor);

@override
String toString() {
  return 'McpResourcesListResult(resources: $resources, nextCursor: $nextCursor)';
}


}

/// @nodoc
abstract mixin class _$McpResourcesListResultCopyWith<$Res> implements $McpResourcesListResultCopyWith<$Res> {
  factory _$McpResourcesListResultCopyWith(_McpResourcesListResult value, $Res Function(_McpResourcesListResult) _then) = __$McpResourcesListResultCopyWithImpl;
@override @useResult
$Res call({
 List<McpResource> resources, String? nextCursor
});




}
/// @nodoc
class __$McpResourcesListResultCopyWithImpl<$Res>
    implements _$McpResourcesListResultCopyWith<$Res> {
  __$McpResourcesListResultCopyWithImpl(this._self, this._then);

  final _McpResourcesListResult _self;
  final $Res Function(_McpResourcesListResult) _then;

/// Create a copy of McpResourcesListResult
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? resources = null,Object? nextCursor = freezed,}) {
  return _then(_McpResourcesListResult(
resources: null == resources ? _self._resources : resources // ignore: cast_nullable_to_non_nullable
as List<McpResource>,nextCursor: freezed == nextCursor ? _self.nextCursor : nextCursor // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$McpPrompt {

 String get name; String? get description; List<McpPromptArgument>? get arguments;
/// Create a copy of McpPrompt
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$McpPromptCopyWith<McpPrompt> get copyWith => _$McpPromptCopyWithImpl<McpPrompt>(this as McpPrompt, _$identity);

  /// Serializes this McpPrompt to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is McpPrompt&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&const DeepCollectionEquality().equals(other.arguments, arguments));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,description,const DeepCollectionEquality().hash(arguments));

@override
String toString() {
  return 'McpPrompt(name: $name, description: $description, arguments: $arguments)';
}


}

/// @nodoc
abstract mixin class $McpPromptCopyWith<$Res>  {
  factory $McpPromptCopyWith(McpPrompt value, $Res Function(McpPrompt) _then) = _$McpPromptCopyWithImpl;
@useResult
$Res call({
 String name, String? description, List<McpPromptArgument>? arguments
});




}
/// @nodoc
class _$McpPromptCopyWithImpl<$Res>
    implements $McpPromptCopyWith<$Res> {
  _$McpPromptCopyWithImpl(this._self, this._then);

  final McpPrompt _self;
  final $Res Function(McpPrompt) _then;

/// Create a copy of McpPrompt
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = null,Object? description = freezed,Object? arguments = freezed,}) {
  return _then(_self.copyWith(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,arguments: freezed == arguments ? _self.arguments : arguments // ignore: cast_nullable_to_non_nullable
as List<McpPromptArgument>?,
  ));
}

}


/// Adds pattern-matching-related methods to [McpPrompt].
extension McpPromptPatterns on McpPrompt {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _McpPrompt value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _McpPrompt() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _McpPrompt value)  $default,){
final _that = this;
switch (_that) {
case _McpPrompt():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _McpPrompt value)?  $default,){
final _that = this;
switch (_that) {
case _McpPrompt() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String name,  String? description,  List<McpPromptArgument>? arguments)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _McpPrompt() when $default != null:
return $default(_that.name,_that.description,_that.arguments);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String name,  String? description,  List<McpPromptArgument>? arguments)  $default,) {final _that = this;
switch (_that) {
case _McpPrompt():
return $default(_that.name,_that.description,_that.arguments);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String name,  String? description,  List<McpPromptArgument>? arguments)?  $default,) {final _that = this;
switch (_that) {
case _McpPrompt() when $default != null:
return $default(_that.name,_that.description,_that.arguments);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _McpPrompt implements McpPrompt {
  const _McpPrompt({required this.name, this.description, final  List<McpPromptArgument>? arguments}): _arguments = arguments;
  factory _McpPrompt.fromJson(Map<String, dynamic> json) => _$McpPromptFromJson(json);

@override final  String name;
@override final  String? description;
 final  List<McpPromptArgument>? _arguments;
@override List<McpPromptArgument>? get arguments {
  final value = _arguments;
  if (value == null) return null;
  if (_arguments is EqualUnmodifiableListView) return _arguments;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}


/// Create a copy of McpPrompt
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$McpPromptCopyWith<_McpPrompt> get copyWith => __$McpPromptCopyWithImpl<_McpPrompt>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$McpPromptToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _McpPrompt&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&const DeepCollectionEquality().equals(other._arguments, _arguments));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,description,const DeepCollectionEquality().hash(_arguments));

@override
String toString() {
  return 'McpPrompt(name: $name, description: $description, arguments: $arguments)';
}


}

/// @nodoc
abstract mixin class _$McpPromptCopyWith<$Res> implements $McpPromptCopyWith<$Res> {
  factory _$McpPromptCopyWith(_McpPrompt value, $Res Function(_McpPrompt) _then) = __$McpPromptCopyWithImpl;
@override @useResult
$Res call({
 String name, String? description, List<McpPromptArgument>? arguments
});




}
/// @nodoc
class __$McpPromptCopyWithImpl<$Res>
    implements _$McpPromptCopyWith<$Res> {
  __$McpPromptCopyWithImpl(this._self, this._then);

  final _McpPrompt _self;
  final $Res Function(_McpPrompt) _then;

/// Create a copy of McpPrompt
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = null,Object? description = freezed,Object? arguments = freezed,}) {
  return _then(_McpPrompt(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,arguments: freezed == arguments ? _self._arguments : arguments // ignore: cast_nullable_to_non_nullable
as List<McpPromptArgument>?,
  ));
}


}


/// @nodoc
mixin _$McpPromptArgument {

 String get name; String? get description; bool get required;
/// Create a copy of McpPromptArgument
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$McpPromptArgumentCopyWith<McpPromptArgument> get copyWith => _$McpPromptArgumentCopyWithImpl<McpPromptArgument>(this as McpPromptArgument, _$identity);

  /// Serializes this McpPromptArgument to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is McpPromptArgument&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&(identical(other.required, required) || other.required == required));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,description,required);

@override
String toString() {
  return 'McpPromptArgument(name: $name, description: $description, required: $required)';
}


}

/// @nodoc
abstract mixin class $McpPromptArgumentCopyWith<$Res>  {
  factory $McpPromptArgumentCopyWith(McpPromptArgument value, $Res Function(McpPromptArgument) _then) = _$McpPromptArgumentCopyWithImpl;
@useResult
$Res call({
 String name, String? description, bool required
});




}
/// @nodoc
class _$McpPromptArgumentCopyWithImpl<$Res>
    implements $McpPromptArgumentCopyWith<$Res> {
  _$McpPromptArgumentCopyWithImpl(this._self, this._then);

  final McpPromptArgument _self;
  final $Res Function(McpPromptArgument) _then;

/// Create a copy of McpPromptArgument
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = null,Object? description = freezed,Object? required = null,}) {
  return _then(_self.copyWith(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,required: null == required ? _self.required : required // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [McpPromptArgument].
extension McpPromptArgumentPatterns on McpPromptArgument {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _McpPromptArgument value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _McpPromptArgument() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _McpPromptArgument value)  $default,){
final _that = this;
switch (_that) {
case _McpPromptArgument():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _McpPromptArgument value)?  $default,){
final _that = this;
switch (_that) {
case _McpPromptArgument() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String name,  String? description,  bool required)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _McpPromptArgument() when $default != null:
return $default(_that.name,_that.description,_that.required);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String name,  String? description,  bool required)  $default,) {final _that = this;
switch (_that) {
case _McpPromptArgument():
return $default(_that.name,_that.description,_that.required);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String name,  String? description,  bool required)?  $default,) {final _that = this;
switch (_that) {
case _McpPromptArgument() when $default != null:
return $default(_that.name,_that.description,_that.required);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _McpPromptArgument implements McpPromptArgument {
  const _McpPromptArgument({required this.name, this.description, this.required = false});
  factory _McpPromptArgument.fromJson(Map<String, dynamic> json) => _$McpPromptArgumentFromJson(json);

@override final  String name;
@override final  String? description;
@override@JsonKey() final  bool required;

/// Create a copy of McpPromptArgument
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$McpPromptArgumentCopyWith<_McpPromptArgument> get copyWith => __$McpPromptArgumentCopyWithImpl<_McpPromptArgument>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$McpPromptArgumentToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _McpPromptArgument&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&(identical(other.required, required) || other.required == required));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,description,required);

@override
String toString() {
  return 'McpPromptArgument(name: $name, description: $description, required: $required)';
}


}

/// @nodoc
abstract mixin class _$McpPromptArgumentCopyWith<$Res> implements $McpPromptArgumentCopyWith<$Res> {
  factory _$McpPromptArgumentCopyWith(_McpPromptArgument value, $Res Function(_McpPromptArgument) _then) = __$McpPromptArgumentCopyWithImpl;
@override @useResult
$Res call({
 String name, String? description, bool required
});




}
/// @nodoc
class __$McpPromptArgumentCopyWithImpl<$Res>
    implements _$McpPromptArgumentCopyWith<$Res> {
  __$McpPromptArgumentCopyWithImpl(this._self, this._then);

  final _McpPromptArgument _self;
  final $Res Function(_McpPromptArgument) _then;

/// Create a copy of McpPromptArgument
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = null,Object? description = freezed,Object? required = null,}) {
  return _then(_McpPromptArgument(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,required: null == required ? _self.required : required // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$McpPromptsListResult {

 List<McpPrompt> get prompts; String? get nextCursor;
/// Create a copy of McpPromptsListResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$McpPromptsListResultCopyWith<McpPromptsListResult> get copyWith => _$McpPromptsListResultCopyWithImpl<McpPromptsListResult>(this as McpPromptsListResult, _$identity);

  /// Serializes this McpPromptsListResult to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is McpPromptsListResult&&const DeepCollectionEquality().equals(other.prompts, prompts)&&(identical(other.nextCursor, nextCursor) || other.nextCursor == nextCursor));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(prompts),nextCursor);

@override
String toString() {
  return 'McpPromptsListResult(prompts: $prompts, nextCursor: $nextCursor)';
}


}

/// @nodoc
abstract mixin class $McpPromptsListResultCopyWith<$Res>  {
  factory $McpPromptsListResultCopyWith(McpPromptsListResult value, $Res Function(McpPromptsListResult) _then) = _$McpPromptsListResultCopyWithImpl;
@useResult
$Res call({
 List<McpPrompt> prompts, String? nextCursor
});




}
/// @nodoc
class _$McpPromptsListResultCopyWithImpl<$Res>
    implements $McpPromptsListResultCopyWith<$Res> {
  _$McpPromptsListResultCopyWithImpl(this._self, this._then);

  final McpPromptsListResult _self;
  final $Res Function(McpPromptsListResult) _then;

/// Create a copy of McpPromptsListResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? prompts = null,Object? nextCursor = freezed,}) {
  return _then(_self.copyWith(
prompts: null == prompts ? _self.prompts : prompts // ignore: cast_nullable_to_non_nullable
as List<McpPrompt>,nextCursor: freezed == nextCursor ? _self.nextCursor : nextCursor // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [McpPromptsListResult].
extension McpPromptsListResultPatterns on McpPromptsListResult {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _McpPromptsListResult value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _McpPromptsListResult() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _McpPromptsListResult value)  $default,){
final _that = this;
switch (_that) {
case _McpPromptsListResult():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _McpPromptsListResult value)?  $default,){
final _that = this;
switch (_that) {
case _McpPromptsListResult() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<McpPrompt> prompts,  String? nextCursor)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _McpPromptsListResult() when $default != null:
return $default(_that.prompts,_that.nextCursor);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<McpPrompt> prompts,  String? nextCursor)  $default,) {final _that = this;
switch (_that) {
case _McpPromptsListResult():
return $default(_that.prompts,_that.nextCursor);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<McpPrompt> prompts,  String? nextCursor)?  $default,) {final _that = this;
switch (_that) {
case _McpPromptsListResult() when $default != null:
return $default(_that.prompts,_that.nextCursor);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _McpPromptsListResult implements McpPromptsListResult {
  const _McpPromptsListResult({required final  List<McpPrompt> prompts, this.nextCursor}): _prompts = prompts;
  factory _McpPromptsListResult.fromJson(Map<String, dynamic> json) => _$McpPromptsListResultFromJson(json);

 final  List<McpPrompt> _prompts;
@override List<McpPrompt> get prompts {
  if (_prompts is EqualUnmodifiableListView) return _prompts;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_prompts);
}

@override final  String? nextCursor;

/// Create a copy of McpPromptsListResult
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$McpPromptsListResultCopyWith<_McpPromptsListResult> get copyWith => __$McpPromptsListResultCopyWithImpl<_McpPromptsListResult>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$McpPromptsListResultToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _McpPromptsListResult&&const DeepCollectionEquality().equals(other._prompts, _prompts)&&(identical(other.nextCursor, nextCursor) || other.nextCursor == nextCursor));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_prompts),nextCursor);

@override
String toString() {
  return 'McpPromptsListResult(prompts: $prompts, nextCursor: $nextCursor)';
}


}

/// @nodoc
abstract mixin class _$McpPromptsListResultCopyWith<$Res> implements $McpPromptsListResultCopyWith<$Res> {
  factory _$McpPromptsListResultCopyWith(_McpPromptsListResult value, $Res Function(_McpPromptsListResult) _then) = __$McpPromptsListResultCopyWithImpl;
@override @useResult
$Res call({
 List<McpPrompt> prompts, String? nextCursor
});




}
/// @nodoc
class __$McpPromptsListResultCopyWithImpl<$Res>
    implements _$McpPromptsListResultCopyWith<$Res> {
  __$McpPromptsListResultCopyWithImpl(this._self, this._then);

  final _McpPromptsListResult _self;
  final $Res Function(_McpPromptsListResult) _then;

/// Create a copy of McpPromptsListResult
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? prompts = null,Object? nextCursor = freezed,}) {
  return _then(_McpPromptsListResult(
prompts: null == prompts ? _self._prompts : prompts // ignore: cast_nullable_to_non_nullable
as List<McpPrompt>,nextCursor: freezed == nextCursor ? _self.nextCursor : nextCursor // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
