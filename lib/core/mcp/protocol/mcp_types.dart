import 'package:freezed_annotation/freezed_annotation.dart';

part 'mcp_types.freezed.dart';
part 'mcp_types.g.dart';

// JSON-RPC 2.0 message types
@freezed
abstract class JsonRpcRequest with _$JsonRpcRequest {
  const factory JsonRpcRequest({
    required String jsonrpc,
    required String method,
    @JsonKey(includeIfNull: false) Object? params,
    @JsonKey(includeIfNull: false) String? id,
  }) = _JsonRpcRequest;

  factory JsonRpcRequest.fromJson(Map<String, dynamic> json) =>
      _$JsonRpcRequestFromJson(json);
}

@freezed
abstract class JsonRpcResponse with _$JsonRpcResponse {
  const factory JsonRpcResponse({
    required String jsonrpc,
    @JsonKey(includeIfNull: false) Object? result,
    @JsonKey(includeIfNull: false) JsonRpcError? error,
    @JsonKey(includeIfNull: false) String? id,
  }) = _JsonRpcResponse;

  factory JsonRpcResponse.fromJson(Map<String, dynamic> json) =>
      _$JsonRpcResponseFromJson(json);
}

@freezed
abstract class JsonRpcError with _$JsonRpcError {
  const factory JsonRpcError({
    required int code,
    required String message,
    @JsonKey(includeIfNull: false) Object? data,
  }) = _JsonRpcError;

  factory JsonRpcError.fromJson(Map<String, dynamic> json) =>
      _$JsonRpcErrorFromJson(json);
}

// MCP Protocol types
@freezed
abstract class McpCapabilities with _$McpCapabilities {
  const factory McpCapabilities({
    McpToolsCapability? tools,
    McpResourcesCapability? resources,
    McpPromptsCapability? prompts,
    McpLoggingCapability? logging,
  }) = _McpCapabilities;

  factory McpCapabilities.fromJson(Map<String, dynamic> json) =>
      _$McpCapabilitiesFromJson(json);
}

@freezed
abstract class McpToolsCapability with _$McpToolsCapability {
  const factory McpToolsCapability({
    @Default(false) bool listChanged,
  }) = _McpToolsCapability;

  factory McpToolsCapability.fromJson(Map<String, dynamic> json) =>
      _$McpToolsCapabilityFromJson(json);
}

@freezed
abstract class McpResourcesCapability with _$McpResourcesCapability {
  const factory McpResourcesCapability({
    @Default(false) bool subscribe,
    @Default(false) bool listChanged,
  }) = _McpResourcesCapability;

  factory McpResourcesCapability.fromJson(Map<String, dynamic> json) =>
      _$McpResourcesCapabilityFromJson(json);
}

@freezed
abstract class McpPromptsCapability with _$McpPromptsCapability {
  const factory McpPromptsCapability({
    @Default(false) bool listChanged,
  }) = _McpPromptsCapability;

  factory McpPromptsCapability.fromJson(Map<String, dynamic> json) =>
      _$McpPromptsCapabilityFromJson(json);
}

@freezed
abstract class McpLoggingCapability with _$McpLoggingCapability {
  const factory McpLoggingCapability() = _McpLoggingCapability;

  factory McpLoggingCapability.fromJson(Map<String, dynamic> json) =>
      _$McpLoggingCapabilityFromJson(json);
}

@freezed
abstract class McpClientInfo with _$McpClientInfo {
  const factory McpClientInfo({
    required String name,
    required String version,
  }) = _McpClientInfo;

  factory McpClientInfo.fromJson(Map<String, dynamic> json) =>
      _$McpClientInfoFromJson(json);
}

@freezed
abstract class McpServerInfo with _$McpServerInfo {
  const factory McpServerInfo({
    required String name,
    required String version,
  }) = _McpServerInfo;

  factory McpServerInfo.fromJson(Map<String, dynamic> json) =>
      _$McpServerInfoFromJson(json);
}

@freezed
abstract class McpInitializeParams with _$McpInitializeParams {
  const factory McpInitializeParams({
    required String protocolVersion,
    required McpCapabilities capabilities,
    required McpClientInfo clientInfo,
  }) = _McpInitializeParams;

  factory McpInitializeParams.fromJson(Map<String, dynamic> json) =>
      _$McpInitializeParamsFromJson(json);
}

@freezed
abstract class McpInitializeResult with _$McpInitializeResult {
  const factory McpInitializeResult({
    required String protocolVersion,
    required McpCapabilities capabilities,
    required McpServerInfo serverInfo,
  }) = _McpInitializeResult;

  factory McpInitializeResult.fromJson(Map<String, dynamic> json) =>
      _$McpInitializeResultFromJson(json);
}

// Tool types
@freezed
abstract class McpTool with _$McpTool {
  const factory McpTool({
    required String name,
    String? description,
    required Map<String, dynamic> inputSchema,
  }) = _McpTool;

  factory McpTool.fromJson(Map<String, dynamic> json) =>
      _$McpToolFromJson(json);
}

@freezed
abstract class McpToolsListParams with _$McpToolsListParams {
  const factory McpToolsListParams({
    String? cursor,
  }) = _McpToolsListParams;

  factory McpToolsListParams.fromJson(Map<String, dynamic> json) =>
      _$McpToolsListParamsFromJson(json);
}

@freezed
abstract class McpToolsListResult with _$McpToolsListResult {
  const factory McpToolsListResult({
    required List<McpTool> tools,
    String? nextCursor,
  }) = _McpToolsListResult;

  factory McpToolsListResult.fromJson(Map<String, dynamic> json) =>
      _$McpToolsListResultFromJson(json);
}

@freezed
abstract class McpToolCallParams with _$McpToolCallParams {
  const factory McpToolCallParams({
    required String name,
    Map<String, dynamic>? arguments,
  }) = _McpToolCallParams;

  factory McpToolCallParams.fromJson(Map<String, dynamic> json) =>
      _$McpToolCallParamsFromJson(json);
}

@freezed
abstract class McpToolCallResult with _$McpToolCallResult {
  const factory McpToolCallResult({
    @JsonKey(includeIfNull: false) List<McpTextContent>? content,
    @Default(false) bool isError,
  }) = _McpToolCallResult;

  factory McpToolCallResult.fromJson(Map<String, dynamic> json) =>
      _$McpToolCallResultFromJson(json);
}

@freezed
abstract class McpTextContent with _$McpTextContent {
  const factory McpTextContent({
    required String type,
    required String text,
  }) = _McpTextContent;

  factory McpTextContent.fromJson(Map<String, dynamic> json) =>
      _$McpTextContentFromJson(json);
}

// Resource types
@freezed
abstract class McpResource with _$McpResource {
  const factory McpResource({
    required String uri,
    required String name,
    String? description,
    String? mimeType,
  }) = _McpResource;

  factory McpResource.fromJson(Map<String, dynamic> json) =>
      _$McpResourceFromJson(json);
}

@freezed
abstract class McpResourcesListParams with _$McpResourcesListParams {
  const factory McpResourcesListParams({
    String? cursor,
  }) = _McpResourcesListParams;

  factory McpResourcesListParams.fromJson(Map<String, dynamic> json) =>
      _$McpResourcesListParamsFromJson(json);
}

@freezed
abstract class McpResourcesListResult with _$McpResourcesListResult {
  const factory McpResourcesListResult({
    required List<McpResource> resources,
    String? nextCursor,
  }) = _McpResourcesListResult;

  factory McpResourcesListResult.fromJson(Map<String, dynamic> json) =>
      _$McpResourcesListResultFromJson(json);
}

// Prompt types
@freezed
abstract class McpPrompt with _$McpPrompt {
  const factory McpPrompt({
    required String name,
    String? description,
    List<McpPromptArgument>? arguments,
  }) = _McpPrompt;

  factory McpPrompt.fromJson(Map<String, dynamic> json) =>
      _$McpPromptFromJson(json);
}

@freezed
abstract class McpPromptArgument with _$McpPromptArgument {
  const factory McpPromptArgument({
    required String name,
    String? description,
    @Default(false) bool required,
  }) = _McpPromptArgument;

  factory McpPromptArgument.fromJson(Map<String, dynamic> json) =>
      _$McpPromptArgumentFromJson(json);
}

@freezed
abstract class McpPromptsListResult with _$McpPromptsListResult {
  const factory McpPromptsListResult({
    required List<McpPrompt> prompts,
    String? nextCursor,
  }) = _McpPromptsListResult;

  factory McpPromptsListResult.fromJson(Map<String, dynamic> json) =>
      _$McpPromptsListResultFromJson(json);
}