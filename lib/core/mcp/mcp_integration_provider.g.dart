// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'mcp_integration_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

/// Integration provider that connects the MCP server manager with the tool registry
/// This is the central coordinator for the MCP system
@ProviderFor(McpIntegration)
const mcpIntegrationProvider = McpIntegrationProvider._();

/// Integration provider that connects the MCP server manager with the tool registry
/// This is the central coordinator for the MCP system
final class McpIntegrationProvider
    extends $AsyncNotifierProvider<McpIntegration, void> {
  /// Integration provider that connects the MCP server manager with the tool registry
  /// This is the central coordinator for the MCP system
  const McpIntegrationProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'mcpIntegrationProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$mcpIntegrationHash();

  @$internal
  @override
  McpIntegration create() => McpIntegration();
}

String _$mcpIntegrationHash() => r'6da4146c06434137b32cc687a083779b313f8e97';

abstract class _$McpIntegration extends $AsyncNotifier<void> {
  FutureOr<void> build();
  @$mustCallSuper
  @override
  void runBuild() {
    build();
    final ref = this.ref as $Ref<AsyncValue<void>, void>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<void>, void>,
              AsyncValue<void>,
              Object?,
              Object?
            >;
    element.handleValue(ref, null);
  }
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
