import 'dart:async';
import 'dart:convert';

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../database/daos/mcp_servers_dao.dart';
import '../database/tables/mcp_servers.dart';
import 'mcp_client.dart';
import 'protocol/mcp_types.dart';
import 'tools/tool_registry.dart';
import '../../features/tools/providers/tool_registry_provider.dart';

part 'mcp_server_manager.g.dart';

/// Manages multiple MCP server connections and their integration with the tool registry
@riverpod
class McpServerManager extends _$McpServerManager {
  final Map<String, McpClient> _clients = {};
  final StreamController<McpServerManagerEvent> _eventController = StreamController<McpServerManagerEvent>.broadcast();
  late final ToolRegistry _toolRegistry;

  @override
  Future<Map<String, McpServerManagerState>> build() async {
    _toolRegistry = ref.read(toolRegistryNotifierProvider);
    return await _loadAndConnectServers();
  }

  Stream<McpServerManagerEvent> get events => _eventController.stream;

  /// Load servers from database and attempt connections
  Future<Map<String, McpServerManagerState>> _loadAndConnectServers() async {
    final dao = ref.read(mcpServersDaoProvider);
    final servers = await dao.getAllServers();
    
    final Map<String, McpServerManagerState> serverStates = {};
    
    for (final server in servers) {
      if (server.isEnabled) {
        serverStates[server.id] = McpServerManagerState(
          server: server,
          isConnecting: true,
        );
        // Start connection in background
        _connectToServer(server);
      } else {
        serverStates[server.id] = McpServerManagerState(
          server: server,
          isConnecting: false,
        );
      }
    }
    
    return serverStates;
  }

  /// Connect to a specific MCP server
  Future<void> _connectToServer(McpServerData serverData) async {
    final dao = ref.read(mcpServersDaoProvider);
    
    try {
      // Update status to connecting
      await dao.updateServerStatus(serverData.id, ServerStatus.connecting);
      _updateState(serverData.id, (state) => state.copyWith(isConnecting: true));
      
      final client = McpClient(
        transportType: _mapTransportType(serverData.transportType),
        url: serverData.url,
        headers: _parseHeaders(serverData.headers),
      );

      // Listen to connection events
      client.connectionEvents.listen((event) async {
        await _handleConnectionEvent(serverData.id, event);
      });

      // Listen to tools updates
      client.toolsUpdates.listen((toolsList) async {
        await _handleToolsUpdate(serverData.id, toolsList);
      });

      await client.connect();
      _clients[serverData.id] = client;
      
    } catch (e) {
      await dao.updateServerStatus(
        serverData.id,
        ServerStatus.error,
        errorMessage: e.toString(),
      );
      _updateState(serverData.id, (state) => state.copyWith(
        isConnecting: false,
        error: e.toString(),
      ));
      _eventController.add(McpServerConnectionFailedEvent(serverData.id, e.toString()));
    }
  }

  /// Handle MCP client connection events
  Future<void> _handleConnectionEvent(String serverId, McpConnectionEvent event) async {
    final dao = ref.read(mcpServersDaoProvider);
    
    if (event is McpConnectedEvent) {
      await dao.updateServerStatus(serverId, ServerStatus.connected);
      _updateState(serverId, (state) => state.copyWith(
        isConnecting: false,
        isConnected: true,
        error: null,
      ));
      _eventController.add(McpServerConnectedEvent(serverId));
      
    } else if (event is McpDisconnectedEvent) {
      await dao.updateServerStatus(serverId, ServerStatus.disconnected);
      _updateState(serverId, (state) => state.copyWith(
        isConnecting: false,
        isConnected: false,
      ));
      _clients.remove(serverId);
      _eventController.add(McpServerDisconnectedEvent(serverId));
      
    } else if (event is McpConnectionErrorEvent) {
      await dao.updateServerStatus(serverId, ServerStatus.error, errorMessage: event.error);
      _updateState(serverId, (state) => state.copyWith(
        isConnecting: false,
        isConnected: false,
        error: event.error,
      ));
      _clients.remove(serverId);
      _eventController.add(McpServerConnectionFailedEvent(serverId, event.error));
      
    } else if (event is McpInitializedEvent) {
      final capabilities = event.result.capabilities;
      final capabilitiesList = <String>[];
      if (capabilities?.tools != null) capabilitiesList.add('tools');
      if (capabilities?.resources != null) capabilitiesList.add('resources');
      if (capabilities?.prompts != null) capabilitiesList.add('prompts');
      
      await dao.updateServerCapabilities(serverId, capabilitiesList);
      _updateState(serverId, (state) => state.copyWith(
        capabilities: capabilitiesList,
      ));
    }
  }

  /// Handle tools list updates from MCP servers
  Future<void> _handleToolsUpdate(String serverId, McpToolsListResult toolsList) async {
    // The tools are automatically handled by the tool registry when we register the server
    _eventController.add(McpServerToolsUpdatedEvent(serverId, toolsList.tools));
  }

  /// Add a new MCP server configuration
  Future<void> addServer({
    required String name,
    required String url,
    required McpTransportType transportType,
    String? description,
    Map<String, String>? headers,
    Map<String, dynamic>? authConfig,
  }) async {
    final dao = ref.read(mcpServersDaoProvider);
    final id = _generateServerId(url);
    
    final serverData = await dao.createServer(
      id: id,
      name: name,
      url: url,
      transportType: transportType,
      description: description,
      headers: headers,
      authConfig: authConfig,
    );
    
    // Add to current state
    _updateState(id, (_) => McpServerManagerState(
      server: serverData,
      isConnecting: true,
    ));
    
    // Start connection
    await _connectToServer(serverData);
    
    _eventController.add(McpServerAddedEvent(id));
  }

  /// Remove an MCP server
  Future<void> removeServer(String serverId) async {
    // Disconnect if connected
    final client = _clients[serverId];
    if (client != null) {
      await client.disconnect();
      _clients.remove(serverId);
    }
    
    // Remove from database
    final dao = ref.read(mcpServersDaoProvider);
    await dao.deleteServer(serverId);
    
    // Remove from state
    final currentState = state.when(data: (data) => data, loading: () => null, error: (_, __) => null) ?? {};
    currentState.remove(serverId);
    state = AsyncValue.data(currentState);
    
    _eventController.add(McpServerRemovedEvent(serverId));
  }

  /// Reconnect to a server
  Future<void> reconnectServer(String serverId) async {
    final currentState = state.when(data: (data) => data, loading: () => null, error: (_, __) => null);
    if (currentState == null || !currentState.containsKey(serverId)) return;
    
    final serverData = currentState[serverId]!.server;
    
    // Disconnect if connected
    final client = _clients[serverId];
    if (client != null) {
      await client.disconnect();
      _clients.remove(serverId);
    }
    
    // Reconnect
    await _connectToServer(serverData);
  }

  /// Get a specific MCP client
  McpClient? getClient(String serverId) => _clients[serverId];

  /// Get all connected clients
  Map<String, McpClient> get connectedClients => Map.unmodifiable(_clients);

  /// Helper methods
  void _updateState(String serverId, McpServerManagerState Function(McpServerManagerState) updater) {
    final currentState = state.when(data: (data) => data, loading: () => null, error: (_, __) => null) ?? {};
    if (currentState.containsKey(serverId)) {
      currentState[serverId] = updater(currentState[serverId]!);
      state = AsyncValue.data(currentState);
    }
  }

  McpTransportType _mapTransportType(McpTransportType type) => type;

  Map<String, String>? _parseHeaders(String? headersJson) {
    if (headersJson == null) return null;
    try {
      final parsed = jsonDecode(headersJson);
      if (parsed is Map<String, dynamic>) {
        return parsed.cast<String, String>();
      }
    } catch (e) {
      // Ignore parsing errors
    }
    return null;
  }

  String _generateServerId(String url) {
    // Generate a simple ID based on URL - in production might want something more robust
    return Uri.parse(url).host.replaceAll('.', '_');
  }

  @override
  void dispose() {
    _eventController.close();
    for (final client in _clients.values) {
      client.dispose();
    }
    _clients.clear();
    super.dispose();
  }
}

/// State for each MCP server
class McpServerManagerState {
  final McpServerData server;
  final bool isConnecting;
  final bool isConnected;
  final String? error;
  final List<String>? capabilities;

  const McpServerManagerState({
    required this.server,
    this.isConnecting = false,
    this.isConnected = false,
    this.error,
    this.capabilities,
  });

  McpServerManagerState copyWith({
    McpServerData? server,
    bool? isConnecting,
    bool? isConnected,
    String? error,
    List<String>? capabilities,
  }) {
    return McpServerManagerState(
      server: server ?? this.server,
      isConnecting: isConnecting ?? this.isConnecting,
      isConnected: isConnected ?? this.isConnected,
      error: error ?? this.error,
      capabilities: capabilities ?? this.capabilities,
    );
  }
}

/// Events emitted by the MCP server manager
abstract class McpServerManagerEvent {}

class McpServerAddedEvent extends McpServerManagerEvent {
  final String serverId;
  McpServerAddedEvent(this.serverId);
}

class McpServerRemovedEvent extends McpServerManagerEvent {
  final String serverId;
  McpServerRemovedEvent(this.serverId);
}

class McpServerConnectedEvent extends McpServerManagerEvent {
  final String serverId;
  McpServerConnectedEvent(this.serverId);
}

class McpServerDisconnectedEvent extends McpServerManagerEvent {
  final String serverId;
  McpServerDisconnectedEvent(this.serverId);
}

class McpServerConnectionFailedEvent extends McpServerManagerEvent {
  final String serverId;
  final String error;
  McpServerConnectionFailedEvent(this.serverId, this.error);
}

class McpServerToolsUpdatedEvent extends McpServerManagerEvent {
  final String serverId;
  final List<McpTool> tools;
  McpServerToolsUpdatedEvent(this.serverId, this.tools);
}

