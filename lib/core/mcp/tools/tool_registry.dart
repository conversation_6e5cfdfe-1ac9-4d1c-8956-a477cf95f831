import 'dart:async';
import 'dart:collection';

import '../protocol/mcp_types.dart';
import '../mcp_client.dart';

class ToolRegistry {
  final Map<String, Map<String, McpTool>> _toolsByServer = {};
  final Map<String, McpClient> _clients = {};
  final StreamController<ToolRegistryEvent> _eventController = StreamController<ToolRegistryEvent>.broadcast();

  Stream<ToolRegistryEvent> get events => _eventController.stream;

  // Get all tools from all servers
  UnmodifiableMapView<String, Map<String, McpTool>> get toolsByServer => 
      UnmodifiableMapView(_toolsByServer);

  // Get all tools as a flat list
  List<ToolWithServer> get allTools {
    final tools = <ToolWithServer>[];
    for (final entry in _toolsByServer.entries) {
      final serverId = entry.key;
      for (final tool in entry.value.values) {
        tools.add(ToolWithServer(
          serverId: serverId,
          tool: tool,
        ));
      }
    }
    return tools;
  }

  // Register an MCP server
  Future<void> registerServer(String serverId, McpClient client) async {
    if (_clients.containsKey(serverId)) {
      throw StateError('Server $serverId is already registered');
    }

    _clients[serverId] = client;
    _toolsByServer[serverId] = {};

    // Listen for tool updates
    client.toolsUpdates.listen((toolsList) {
      _updateTools(serverId, toolsList.tools);
    });

    // Listen for connection events
    client.connectionEvents.listen((event) {
      switch (event) {
        case McpConnectedEvent():
          _refreshServerTools(serverId);
          break;
        case McpDisconnectedEvent():
          _clearServerTools(serverId);
          break;
        case McpConnectionErrorEvent():
          _eventController.add(ToolRegistryErrorEvent(
            serverId: serverId,
            error: event.error,
          ));
          break;
      }
    });

    // Initial tool fetch if already connected
    if (client.isConnected) {
      await _refreshServerTools(serverId);
    }

    _eventController.add(ToolRegistryServerRegisteredEvent(serverId));
  }

  // Unregister an MCP server
  Future<void> unregisterServer(String serverId) async {
    final client = _clients.remove(serverId);
    if (client != null) {
      await client.disconnect();
    }

    _clearServerTools(serverId);
    _eventController.add(ToolRegistryServerUnregisteredEvent(serverId));
  }

  // Get tool by name (searches all servers)
  ToolWithServer? findTool(String toolName) {
    for (final entry in _toolsByServer.entries) {
      final serverId = entry.key;
      final tool = entry.value[toolName];
      if (tool != null) {
        return ToolWithServer(serverId: serverId, tool: tool);
      }
    }
    return null;
  }

  // Get tools by server
  Map<String, McpTool> getToolsForServer(String serverId) {
    return UnmodifiableMapView(_toolsByServer[serverId] ?? {});
  }

  // Execute a tool
  Future<McpToolCallResult> executeTool({
    required String serverId,
    required String toolName,
    Map<String, dynamic>? arguments,
  }) async {
    final client = _clients[serverId];
    if (client == null) {
      throw ToolExecutionException('Server $serverId not found');
    }

    if (!client.isConnected) {
      throw ToolExecutionException('Server $serverId is not connected');
    }

    final tool = _toolsByServer[serverId]?[toolName];
    if (tool == null) {
      throw ToolExecutionException('Tool $toolName not found on server $serverId');
    }

    try {
      final result = await client.callTool(name: toolName, arguments: arguments);
      
      _eventController.add(ToolExecutedEvent(
        serverId: serverId,
        toolName: toolName,
        arguments: arguments,
        result: result,
      ));

      return result;
    } catch (e) {
      _eventController.add(ToolExecutionErrorEvent(
        serverId: serverId,
        toolName: toolName,
        arguments: arguments,
        error: e.toString(),
      ));
      rethrow;
    }
  }

  Future<void> _refreshServerTools(String serverId) async {
    try {
      final client = _clients[serverId];
      if (client == null) return;

      final toolsList = await client.listTools();
      _updateTools(serverId, toolsList.tools);
    } catch (e) {
      _eventController.add(ToolRegistryErrorEvent(
        serverId: serverId,
        error: 'Failed to refresh tools: $e',
      ));
    }
  }

  void _updateTools(String serverId, List<McpTool> tools) {
    final toolsMap = <String, McpTool>{};
    for (final tool in tools) {
      toolsMap[tool.name] = tool;
    }

    _toolsByServer[serverId] = toolsMap;
    _eventController.add(ToolsUpdatedEvent(serverId, tools));
  }

  void _clearServerTools(String serverId) {
    _toolsByServer[serverId] = {};
    _eventController.add(ToolsUpdatedEvent(serverId, []));
  }

  // Get server connection status
  bool isServerConnected(String serverId) {
    final client = _clients[serverId];
    return client?.isConnected ?? false;
  }

  // Get all registered server IDs
  List<String> get serverIds => _clients.keys.toList();

  void dispose() {
    for (final client in _clients.values) {
      client.dispose();
    }
    _clients.clear();
    _toolsByServer.clear();
    _eventController.close();
  }
}

class ToolWithServer {
  final String serverId;
  final McpTool tool;

  const ToolWithServer({
    required this.serverId,
    required this.tool,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ToolWithServer &&
        other.serverId == serverId &&
        other.tool.name == tool.name;
  }

  @override
  int get hashCode => Object.hash(serverId, tool.name);
}

// Events
abstract class ToolRegistryEvent {}

class ToolRegistryServerRegisteredEvent extends ToolRegistryEvent {
  final String serverId;
  
  ToolRegistryServerRegisteredEvent(this.serverId);
}

class ToolRegistryServerUnregisteredEvent extends ToolRegistryEvent {
  final String serverId;
  
  ToolRegistryServerUnregisteredEvent(this.serverId);
}

class ToolsUpdatedEvent extends ToolRegistryEvent {
  final String serverId;
  final List<McpTool> tools;
  
  ToolsUpdatedEvent(this.serverId, this.tools);
}

class ToolExecutedEvent extends ToolRegistryEvent {
  final String serverId;
  final String toolName;
  final Map<String, dynamic>? arguments;
  final McpToolCallResult result;
  
  ToolExecutedEvent({
    required this.serverId,
    required this.toolName,
    this.arguments,
    required this.result,
  });
}

class ToolExecutionErrorEvent extends ToolRegistryEvent {
  final String serverId;
  final String toolName;
  final Map<String, dynamic>? arguments;
  final String error;
  
  ToolExecutionErrorEvent({
    required this.serverId,
    required this.toolName,
    this.arguments,
    required this.error,
  });
}

class ToolRegistryErrorEvent extends ToolRegistryEvent {
  final String serverId;
  final String error;
  
  ToolRegistryErrorEvent({
    required this.serverId,
    required this.error,
  });
}

// Exceptions
class ToolExecutionException implements Exception {
  final String message;

  const ToolExecutionException(this.message);

  @override
  String toString() => 'ToolExecutionException: $message';
}