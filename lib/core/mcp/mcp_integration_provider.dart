import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'mcp_server_manager.dart';
import '../database/tables/mcp_servers.dart';
import '../../features/tools/providers/tool_registry_provider.dart';

part 'mcp_integration_provider.g.dart';

/// Integration provider that connects the MCP server manager with the tool registry
/// This is the central coordinator for the MCP system
@riverpod
class McpIntegration extends _$McpIntegration {
  @override
  Future<void> build() async {
    // Initialize the MCP server manager
    final serverManager = ref.watch(mcpServerManagerProvider);
    final toolRegistryNotifier = ref.read(toolRegistryNotifierProvider.notifier);
    
    // When servers come online, register them with the tool registry
    await serverManager.when(
      data: (serverStates) async {
        final manager = ref.read(mcpServerManagerProvider.notifier);
        
        // Listen for server manager events
        manager.events.listen((event) async {
          switch (event) {
            case McpServerConnectedEvent():
              final client = manager.getClient(event.serverId);
              if (client != null) {
                await toolRegistryNotifier.registerServer(event.serverId, client);
              }
              break;
            case McpServerDisconnectedEvent():
              await toolRegistryNotifier.unregisterServer(event.serverId);
              break;
            case McpServerRemovedEvent():
              await toolRegistryNotifier.unregisterServer(event.serverId);
              break;
            default:
              // Handle other events if needed
              break;
          }
        });

        // Register any already connected servers
        for (final entry in serverStates.entries) {
          final serverId = entry.key;
          final state = entry.value;
          
          if (state.isConnected) {
            final client = manager.getClient(serverId);
            if (client != null) {
              await toolRegistryNotifier.registerServer(serverId, client);
            }
          }
        }
      },
      loading: () {/* Still loading servers */},
      error: (error, stack) => throw error,
    );
  }

  /// Add a default Context7 MCP server
  Future<void> addContext7Server() async {
    final manager = ref.read(mcpServerManagerProvider.notifier);
    await manager.addServer(
      name: 'Context7',
      url: 'https://mcp.context7.ai',
      transportType: McpTransportType.sse,
      description: 'Context7 MCP server for documentation and code examples',
    );
  }

  /// Add Jina MCP server
  Future<void> addJinaServer() async {
    final manager = ref.read(mcpServerManagerProvider.notifier);
    await manager.addServer(
      name: 'Jina Reader',
      url: 'https://r.jina.ai/mcp',
      transportType: McpTransportType.http,
      description: 'Jina Reader MCP server for web content processing',
    );
  }

  /// Add a custom MCP server
  Future<void> addCustomServer({
    required String name,
    required String url,
    required McpTransportType transportType,
    String? description,
    Map<String, String>? headers,
  }) async {
    final manager = ref.read(mcpServerManagerProvider.notifier);
    await manager.addServer(
      name: name,
      url: url,
      transportType: transportType,
      description: description,
      headers: headers,
    );
  }

  /// Get current server states
  Map<String, McpServerManagerState>? get serverStates {
    final asyncValue = ref.read(mcpServerManagerProvider);
    return asyncValue.when(
      data: (data) => data,
      loading: () => null,
      error: (_, __) => null,
    );
  }

  /// Get list of connected server IDs
  List<String> get connectedServerIds {
    final states = serverStates;
    if (states == null) return [];
    
    return states.entries
        .where((entry) => entry.value.isConnected)
        .map((entry) => entry.key)
        .toList();
  }
}