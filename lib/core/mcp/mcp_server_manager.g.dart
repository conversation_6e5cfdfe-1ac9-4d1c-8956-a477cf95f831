// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'mcp_server_manager.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

/// Manages multiple MCP server connections and their integration with the tool registry
@ProviderFor(McpServerManager)
const mcpServerManagerProvider = McpServerManagerProvider._();

/// Manages multiple MCP server connections and their integration with the tool registry
final class McpServerManagerProvider
    extends
        $AsyncNotifierProvider<
          McpServerManager,
          Map<String, McpServerManagerState>
        > {
  /// Manages multiple MCP server connections and their integration with the tool registry
  const McpServerManagerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'mcpServerManagerProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$mcpServerManagerHash();

  @$internal
  @override
  McpServerManager create() => McpServerManager();
}

String _$mcpServerManagerHash() => r'f919fb7b6524d64570115cce1cd86668397c0196';

abstract class _$McpServerManager
    extends $AsyncNotifier<Map<String, McpServerManagerState>> {
  FutureOr<Map<String, McpServerManagerState>> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref =
        this.ref
            as $Ref<
              AsyncValue<Map<String, McpServerManagerState>>,
              Map<String, McpServerManagerState>
            >;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<
                AsyncValue<Map<String, McpServerManagerState>>,
                Map<String, McpServerManagerState>
              >,
              AsyncValue<Map<String, McpServerManagerState>>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
