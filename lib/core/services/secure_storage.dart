import 'dart:convert';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class SecureStorageService {
  static const _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  // API Keys
  static const String _openRouterApiKeyKey = 'openrouter_api_key';
  static const String _customApiKeysKey = 'custom_api_keys';
  
  // MCP Server credentials
  static const String _mcpServerCredsKey = 'mcp_server_credentials';

  // Store OpenRouter API key
  Future<void> storeOpenRouterApiKey(String apiKey) async {
    await _storage.write(key: _openRouterApiKeyKey, value: apiKey);
  }

  // Retrieve OpenRouter API key
  Future<String?> getOpenRouterApiKey() async {
    return await _storage.read(key: _openRouterApiKeyKey);
  }

  // Delete OpenRouter API key
  Future<void> deleteOpenRouterApiKey() async {
    await _storage.delete(key: _openRouterApiKeyKey);
  }

  // Store custom API keys (for different providers)
  Future<void> storeCustomApiKeys(Map<String, String> apiKeys) async {
    final jsonString = jsonEncode(apiKeys);
    await _storage.write(key: _customApiKeysKey, value: jsonString);
  }

  // Retrieve custom API keys
  Future<Map<String, String>> getCustomApiKeys() async {
    final jsonString = await _storage.read(key: _customApiKeysKey);
    if (jsonString == null) return {};
    
    try {
      final decoded = jsonDecode(jsonString) as Map<String, dynamic>;
      return decoded.cast<String, String>();
    } catch (e) {
      return {};
    }
  }

  // Store API key for specific provider
  Future<void> storeApiKey(String provider, String apiKey) async {
    final existingKeys = await getCustomApiKeys();
    existingKeys[provider] = apiKey;
    await storeCustomApiKeys(existingKeys);
  }

  // Get API key for specific provider
  Future<String?> getApiKey(String provider) async {
    final keys = await getCustomApiKeys();
    return keys[provider];
  }

  // Delete API key for specific provider
  Future<void> deleteApiKey(String provider) async {
    final existingKeys = await getCustomApiKeys();
    existingKeys.remove(provider);
    await storeCustomApiKeys(existingKeys);
  }

  // Store MCP server credentials
  Future<void> storeMcpServerCredentials(
    String serverId,
    Map<String, String> credentials,
  ) async {
    final existingCreds = await getMcpServerCredentials();
    existingCreds[serverId] = credentials;
    
    final jsonString = jsonEncode(existingCreds);
    await _storage.write(key: _mcpServerCredsKey, value: jsonString);
  }

  // Retrieve all MCP server credentials
  Future<Map<String, Map<String, String>>> getMcpServerCredentials() async {
    final jsonString = await _storage.read(key: _mcpServerCredsKey);
    if (jsonString == null) return {};
    
    try {
      final decoded = jsonDecode(jsonString) as Map<String, dynamic>;
      final result = <String, Map<String, String>>{};
      
      for (final entry in decoded.entries) {
        result[entry.key] = (entry.value as Map<String, dynamic>).cast<String, String>();
      }
      
      return result;
    } catch (e) {
      return {};
    }
  }

  // Get credentials for specific MCP server
  Future<Map<String, String>?> getMcpServerCredential(String serverId) async {
    final allCreds = await getMcpServerCredentials();
    return allCreds[serverId];
  }

  // Delete credentials for MCP server
  Future<void> deleteMcpServerCredentials(String serverId) async {
    final existingCreds = await getMcpServerCredentials();
    existingCreds.remove(serverId);
    
    final jsonString = jsonEncode(existingCreds);
    await _storage.write(key: _mcpServerCredsKey, value: jsonString);
  }

  // Generic secure storage methods
  Future<void> store(String key, String value) async {
    await _storage.write(key: key, value: value);
  }

  Future<String?> retrieve(String key) async {
    return await _storage.read(key: key);
  }

  Future<void> delete(String key) async {
    await _storage.delete(key: key);
  }

  // Store JSON data securely
  Future<void> storeJson(String key, Map<String, dynamic> data) async {
    final jsonString = jsonEncode(data);
    await _storage.write(key: key, value: jsonString);
  }

  // Retrieve JSON data
  Future<Map<String, dynamic>?> retrieveJson(String key) async {
    final jsonString = await _storage.read(key: key);
    if (jsonString == null) return null;
    
    try {
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }

  // Clear all stored data
  Future<void> clearAll() async {
    await _storage.deleteAll();
  }

  // Check if key exists
  Future<bool> containsKey(String key) async {
    return await _storage.containsKey(key: key);
  }

  // Get all keys
  Future<Map<String, String>> getAll() async {
    return await _storage.readAll();
  }
}