import '../mcp/protocol/mcp_types.dart';
import '../api/openrouter/openrouter_models.dart';

/// Service for converting MCP tool definitions to OpenRouter-compatible format
class ToolAdapter {
  /// Converts an MCP tool to OpenRouter Tool format
  /// Following the pattern from OpenRouter + MCP documentation
  static Tool convertMcpToolToOpenRouterTool(McpTool mcpTool) {
    return Tool(
      type: 'function',
      function: ToolFunctionSchema(
        name: mcpTool.name,
        description: mcpTool.description,
        parameters: mcpTool.inputSchema,
      ),
    );
  }

  /// Converts a list of MCP tools to OpenRouter tools
  static List<Tool> convertMcpToolsToOpenRouterTools(List<McpTool> mcpTools) {
    return mcpTools.map(convertMcpToolToOpenRouterTool).toList();
  }
}