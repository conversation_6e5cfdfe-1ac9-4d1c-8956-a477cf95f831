class TokenCounter {
  // Simple token estimation based on GPT tokenizer approximations
  // For production, consider using a proper tokenizer like tiktoken
  
  static const double _averageCharsPerToken = 4.0;
  
  // Basic token estimation for text
  static int estimateTokens(String text) {
    if (text.isEmpty) return 0;
    
    // Basic heuristics for token counting
    int baseCount = (text.length / _averageCharsPerToken).ceil();
    
    // Adjust for common patterns
    int adjustments = 0;
    
    // Code blocks typically have more tokens due to syntax
    final codeBlocks = RegExp(r'```[\s\S]*?```').allMatches(text);
    adjustments += codeBlocks.length * 5;
    
    // Inline code has slightly higher token density
    final inlineCode = RegExp(r'`[^`]+`').allMatches(text);
    adjustments += inlineCode.length * 2;
    
    // URLs and file paths are often single tokens despite length
    final urls = RegExp(r'https?://[^\s]+').allMatches(text);
    adjustments -= urls.length * 2;
    
    // Mathematical expressions
    final mathExpressions = RegExp(r'\$[^$]+\$').allMatches(text);
    adjustments += mathExpressions.length * 3;
    
    return baseCount + adjustments;
  }
  
  // Token estimation for messages with role context
  static int estimateMessageTokens({
    required String role,
    required String content,
    String? name,
  }) {
    int baseTokens = estimateTokens(content);
    
    // Add tokens for message structure
    baseTokens += 4; // Base message overhead
    
    // Role token
    baseTokens += estimateTokens(role);
    
    // Name if present
    if (name != null && name.isNotEmpty) {
      baseTokens += estimateTokens(name) + 1;
    }
    
    return baseTokens;
  }
  
  // Estimate tokens for a conversation
  static int estimateConversationTokens(List<Map<String, String>> messages) {
    int totalTokens = 0;
    
    for (final message in messages) {
      totalTokens += estimateMessageTokens(
        role: message['role'] ?? 'user',
        content: message['content'] ?? '',
        name: message['name'],
      );
    }
    
    // Add system overhead for conversation
    totalTokens += 10;
    
    return totalTokens;
  }
  
  // Format token count for display
  static String formatTokenCount(int tokens) {
    if (tokens < 1000) {
      return tokens.toString();
    } else if (tokens < 1000000) {
      return '${(tokens / 1000).toStringAsFixed(1)}K';
    } else {
      return '${(tokens / 1000000).toStringAsFixed(1)}M';
    }
  }
  
  // Calculate cost estimation (rough approximation)
  static double estimateCost({
    required int inputTokens,
    required int outputTokens,
    double inputPricePerMillion = 3.0,  // Default pricing
    double outputPricePerMillion = 15.0, // Default pricing
  }) {
    final inputCost = (inputTokens / 1000000) * inputPricePerMillion;
    final outputCost = (outputTokens / 1000000) * outputPricePerMillion;
    return inputCost + outputCost;
  }
  
  // Format cost for display
  static String formatCost(double cost) {
    if (cost < 0.01) {
      return '<\$0.01';
    } else if (cost < 1.0) {
      return '\$${cost.toStringAsFixed(3)}';
    } else {
      return '\$${cost.toStringAsFixed(2)}';
    }
  }
}