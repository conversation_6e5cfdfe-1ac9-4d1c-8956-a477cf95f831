// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'openrouter_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ChatMessage {

 String get role; String get content; String? get name;@JsonKey(name: 'tool_calls') List<ToolCall>? get toolCalls;@JsonKey(name: 'tool_call_id') String? get toolCallId;
/// Create a copy of ChatMessage
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ChatMessageCopyWith<ChatMessage> get copyWith => _$ChatMessageCopyWithImpl<ChatMessage>(this as ChatMessage, _$identity);

  /// Serializes this ChatMessage to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChatMessage&&(identical(other.role, role) || other.role == role)&&(identical(other.content, content) || other.content == content)&&(identical(other.name, name) || other.name == name)&&const DeepCollectionEquality().equals(other.toolCalls, toolCalls)&&(identical(other.toolCallId, toolCallId) || other.toolCallId == toolCallId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,role,content,name,const DeepCollectionEquality().hash(toolCalls),toolCallId);

@override
String toString() {
  return 'ChatMessage(role: $role, content: $content, name: $name, toolCalls: $toolCalls, toolCallId: $toolCallId)';
}


}

/// @nodoc
abstract mixin class $ChatMessageCopyWith<$Res>  {
  factory $ChatMessageCopyWith(ChatMessage value, $Res Function(ChatMessage) _then) = _$ChatMessageCopyWithImpl;
@useResult
$Res call({
 String role, String content, String? name,@JsonKey(name: 'tool_calls') List<ToolCall>? toolCalls,@JsonKey(name: 'tool_call_id') String? toolCallId
});




}
/// @nodoc
class _$ChatMessageCopyWithImpl<$Res>
    implements $ChatMessageCopyWith<$Res> {
  _$ChatMessageCopyWithImpl(this._self, this._then);

  final ChatMessage _self;
  final $Res Function(ChatMessage) _then;

/// Create a copy of ChatMessage
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? role = null,Object? content = null,Object? name = freezed,Object? toolCalls = freezed,Object? toolCallId = freezed,}) {
  return _then(_self.copyWith(
role: null == role ? _self.role : role // ignore: cast_nullable_to_non_nullable
as String,content: null == content ? _self.content : content // ignore: cast_nullable_to_non_nullable
as String,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,toolCalls: freezed == toolCalls ? _self.toolCalls : toolCalls // ignore: cast_nullable_to_non_nullable
as List<ToolCall>?,toolCallId: freezed == toolCallId ? _self.toolCallId : toolCallId // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [ChatMessage].
extension ChatMessagePatterns on ChatMessage {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ChatMessage value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ChatMessage() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ChatMessage value)  $default,){
final _that = this;
switch (_that) {
case _ChatMessage():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ChatMessage value)?  $default,){
final _that = this;
switch (_that) {
case _ChatMessage() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String role,  String content,  String? name, @JsonKey(name: 'tool_calls')  List<ToolCall>? toolCalls, @JsonKey(name: 'tool_call_id')  String? toolCallId)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ChatMessage() when $default != null:
return $default(_that.role,_that.content,_that.name,_that.toolCalls,_that.toolCallId);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String role,  String content,  String? name, @JsonKey(name: 'tool_calls')  List<ToolCall>? toolCalls, @JsonKey(name: 'tool_call_id')  String? toolCallId)  $default,) {final _that = this;
switch (_that) {
case _ChatMessage():
return $default(_that.role,_that.content,_that.name,_that.toolCalls,_that.toolCallId);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String role,  String content,  String? name, @JsonKey(name: 'tool_calls')  List<ToolCall>? toolCalls, @JsonKey(name: 'tool_call_id')  String? toolCallId)?  $default,) {final _that = this;
switch (_that) {
case _ChatMessage() when $default != null:
return $default(_that.role,_that.content,_that.name,_that.toolCalls,_that.toolCallId);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ChatMessage implements ChatMessage {
  const _ChatMessage({required this.role, required this.content, this.name, @JsonKey(name: 'tool_calls') final  List<ToolCall>? toolCalls, @JsonKey(name: 'tool_call_id') this.toolCallId}): _toolCalls = toolCalls;
  factory _ChatMessage.fromJson(Map<String, dynamic> json) => _$ChatMessageFromJson(json);

@override final  String role;
@override final  String content;
@override final  String? name;
 final  List<ToolCall>? _toolCalls;
@override@JsonKey(name: 'tool_calls') List<ToolCall>? get toolCalls {
  final value = _toolCalls;
  if (value == null) return null;
  if (_toolCalls is EqualUnmodifiableListView) return _toolCalls;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override@JsonKey(name: 'tool_call_id') final  String? toolCallId;

/// Create a copy of ChatMessage
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChatMessageCopyWith<_ChatMessage> get copyWith => __$ChatMessageCopyWithImpl<_ChatMessage>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ChatMessageToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChatMessage&&(identical(other.role, role) || other.role == role)&&(identical(other.content, content) || other.content == content)&&(identical(other.name, name) || other.name == name)&&const DeepCollectionEquality().equals(other._toolCalls, _toolCalls)&&(identical(other.toolCallId, toolCallId) || other.toolCallId == toolCallId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,role,content,name,const DeepCollectionEquality().hash(_toolCalls),toolCallId);

@override
String toString() {
  return 'ChatMessage(role: $role, content: $content, name: $name, toolCalls: $toolCalls, toolCallId: $toolCallId)';
}


}

/// @nodoc
abstract mixin class _$ChatMessageCopyWith<$Res> implements $ChatMessageCopyWith<$Res> {
  factory _$ChatMessageCopyWith(_ChatMessage value, $Res Function(_ChatMessage) _then) = __$ChatMessageCopyWithImpl;
@override @useResult
$Res call({
 String role, String content, String? name,@JsonKey(name: 'tool_calls') List<ToolCall>? toolCalls,@JsonKey(name: 'tool_call_id') String? toolCallId
});




}
/// @nodoc
class __$ChatMessageCopyWithImpl<$Res>
    implements _$ChatMessageCopyWith<$Res> {
  __$ChatMessageCopyWithImpl(this._self, this._then);

  final _ChatMessage _self;
  final $Res Function(_ChatMessage) _then;

/// Create a copy of ChatMessage
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? role = null,Object? content = null,Object? name = freezed,Object? toolCalls = freezed,Object? toolCallId = freezed,}) {
  return _then(_ChatMessage(
role: null == role ? _self.role : role // ignore: cast_nullable_to_non_nullable
as String,content: null == content ? _self.content : content // ignore: cast_nullable_to_non_nullable
as String,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,toolCalls: freezed == toolCalls ? _self._toolCalls : toolCalls // ignore: cast_nullable_to_non_nullable
as List<ToolCall>?,toolCallId: freezed == toolCallId ? _self.toolCallId : toolCallId // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$ToolCall {

 String get id; String get type; ToolFunction get function;
/// Create a copy of ToolCall
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ToolCallCopyWith<ToolCall> get copyWith => _$ToolCallCopyWithImpl<ToolCall>(this as ToolCall, _$identity);

  /// Serializes this ToolCall to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ToolCall&&(identical(other.id, id) || other.id == id)&&(identical(other.type, type) || other.type == type)&&(identical(other.function, function) || other.function == function));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,type,function);

@override
String toString() {
  return 'ToolCall(id: $id, type: $type, function: $function)';
}


}

/// @nodoc
abstract mixin class $ToolCallCopyWith<$Res>  {
  factory $ToolCallCopyWith(ToolCall value, $Res Function(ToolCall) _then) = _$ToolCallCopyWithImpl;
@useResult
$Res call({
 String id, String type, ToolFunction function
});


$ToolFunctionCopyWith<$Res> get function;

}
/// @nodoc
class _$ToolCallCopyWithImpl<$Res>
    implements $ToolCallCopyWith<$Res> {
  _$ToolCallCopyWithImpl(this._self, this._then);

  final ToolCall _self;
  final $Res Function(ToolCall) _then;

/// Create a copy of ToolCall
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? type = null,Object? function = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String,function: null == function ? _self.function : function // ignore: cast_nullable_to_non_nullable
as ToolFunction,
  ));
}
/// Create a copy of ToolCall
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ToolFunctionCopyWith<$Res> get function {
  
  return $ToolFunctionCopyWith<$Res>(_self.function, (value) {
    return _then(_self.copyWith(function: value));
  });
}
}


/// Adds pattern-matching-related methods to [ToolCall].
extension ToolCallPatterns on ToolCall {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ToolCall value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ToolCall() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ToolCall value)  $default,){
final _that = this;
switch (_that) {
case _ToolCall():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ToolCall value)?  $default,){
final _that = this;
switch (_that) {
case _ToolCall() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String type,  ToolFunction function)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ToolCall() when $default != null:
return $default(_that.id,_that.type,_that.function);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String type,  ToolFunction function)  $default,) {final _that = this;
switch (_that) {
case _ToolCall():
return $default(_that.id,_that.type,_that.function);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String type,  ToolFunction function)?  $default,) {final _that = this;
switch (_that) {
case _ToolCall() when $default != null:
return $default(_that.id,_that.type,_that.function);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ToolCall implements ToolCall {
  const _ToolCall({required this.id, required this.type, required this.function});
  factory _ToolCall.fromJson(Map<String, dynamic> json) => _$ToolCallFromJson(json);

@override final  String id;
@override final  String type;
@override final  ToolFunction function;

/// Create a copy of ToolCall
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ToolCallCopyWith<_ToolCall> get copyWith => __$ToolCallCopyWithImpl<_ToolCall>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ToolCallToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ToolCall&&(identical(other.id, id) || other.id == id)&&(identical(other.type, type) || other.type == type)&&(identical(other.function, function) || other.function == function));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,type,function);

@override
String toString() {
  return 'ToolCall(id: $id, type: $type, function: $function)';
}


}

/// @nodoc
abstract mixin class _$ToolCallCopyWith<$Res> implements $ToolCallCopyWith<$Res> {
  factory _$ToolCallCopyWith(_ToolCall value, $Res Function(_ToolCall) _then) = __$ToolCallCopyWithImpl;
@override @useResult
$Res call({
 String id, String type, ToolFunction function
});


@override $ToolFunctionCopyWith<$Res> get function;

}
/// @nodoc
class __$ToolCallCopyWithImpl<$Res>
    implements _$ToolCallCopyWith<$Res> {
  __$ToolCallCopyWithImpl(this._self, this._then);

  final _ToolCall _self;
  final $Res Function(_ToolCall) _then;

/// Create a copy of ToolCall
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? type = null,Object? function = null,}) {
  return _then(_ToolCall(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String,function: null == function ? _self.function : function // ignore: cast_nullable_to_non_nullable
as ToolFunction,
  ));
}

/// Create a copy of ToolCall
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ToolFunctionCopyWith<$Res> get function {
  
  return $ToolFunctionCopyWith<$Res>(_self.function, (value) {
    return _then(_self.copyWith(function: value));
  });
}
}


/// @nodoc
mixin _$ToolFunction {

 String get name; String get arguments;
/// Create a copy of ToolFunction
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ToolFunctionCopyWith<ToolFunction> get copyWith => _$ToolFunctionCopyWithImpl<ToolFunction>(this as ToolFunction, _$identity);

  /// Serializes this ToolFunction to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ToolFunction&&(identical(other.name, name) || other.name == name)&&(identical(other.arguments, arguments) || other.arguments == arguments));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,arguments);

@override
String toString() {
  return 'ToolFunction(name: $name, arguments: $arguments)';
}


}

/// @nodoc
abstract mixin class $ToolFunctionCopyWith<$Res>  {
  factory $ToolFunctionCopyWith(ToolFunction value, $Res Function(ToolFunction) _then) = _$ToolFunctionCopyWithImpl;
@useResult
$Res call({
 String name, String arguments
});




}
/// @nodoc
class _$ToolFunctionCopyWithImpl<$Res>
    implements $ToolFunctionCopyWith<$Res> {
  _$ToolFunctionCopyWithImpl(this._self, this._then);

  final ToolFunction _self;
  final $Res Function(ToolFunction) _then;

/// Create a copy of ToolFunction
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = null,Object? arguments = null,}) {
  return _then(_self.copyWith(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,arguments: null == arguments ? _self.arguments : arguments // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [ToolFunction].
extension ToolFunctionPatterns on ToolFunction {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ToolFunction value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ToolFunction() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ToolFunction value)  $default,){
final _that = this;
switch (_that) {
case _ToolFunction():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ToolFunction value)?  $default,){
final _that = this;
switch (_that) {
case _ToolFunction() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String name,  String arguments)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ToolFunction() when $default != null:
return $default(_that.name,_that.arguments);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String name,  String arguments)  $default,) {final _that = this;
switch (_that) {
case _ToolFunction():
return $default(_that.name,_that.arguments);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String name,  String arguments)?  $default,) {final _that = this;
switch (_that) {
case _ToolFunction() when $default != null:
return $default(_that.name,_that.arguments);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ToolFunction implements ToolFunction {
  const _ToolFunction({required this.name, required this.arguments});
  factory _ToolFunction.fromJson(Map<String, dynamic> json) => _$ToolFunctionFromJson(json);

@override final  String name;
@override final  String arguments;

/// Create a copy of ToolFunction
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ToolFunctionCopyWith<_ToolFunction> get copyWith => __$ToolFunctionCopyWithImpl<_ToolFunction>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ToolFunctionToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ToolFunction&&(identical(other.name, name) || other.name == name)&&(identical(other.arguments, arguments) || other.arguments == arguments));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,arguments);

@override
String toString() {
  return 'ToolFunction(name: $name, arguments: $arguments)';
}


}

/// @nodoc
abstract mixin class _$ToolFunctionCopyWith<$Res> implements $ToolFunctionCopyWith<$Res> {
  factory _$ToolFunctionCopyWith(_ToolFunction value, $Res Function(_ToolFunction) _then) = __$ToolFunctionCopyWithImpl;
@override @useResult
$Res call({
 String name, String arguments
});




}
/// @nodoc
class __$ToolFunctionCopyWithImpl<$Res>
    implements _$ToolFunctionCopyWith<$Res> {
  __$ToolFunctionCopyWithImpl(this._self, this._then);

  final _ToolFunction _self;
  final $Res Function(_ToolFunction) _then;

/// Create a copy of ToolFunction
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = null,Object? arguments = null,}) {
  return _then(_ToolFunction(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,arguments: null == arguments ? _self.arguments : arguments // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$ChatCompletionRequest {

 String get model; List<ChatMessage> get messages;@JsonKey(name: 'max_tokens') int? get maxTokens; double? get temperature;@JsonKey(name: 'top_p') double? get topP;@JsonKey(name: 'top_k') int? get topK;@JsonKey(name: 'frequency_penalty') double? get frequencyPenalty;@JsonKey(name: 'presence_penalty') double? get presencePenalty;@JsonKey(name: 'repetition_penalty') double? get repetitionPenalty;@JsonKey(name: 'min_p') double? get minP;@JsonKey(name: 'top_a') double? get topA; int? get seed;@JsonKey(name: 'logit_bias') Map<String, int>? get logitBias; bool? get logprobs;@JsonKey(name: 'top_logprobs') int? get topLogprobs;@JsonKey(name: 'response_format') ResponseFormat? get responseFormat; List<String>? get stop; List<Tool>? get tools;@JsonKey(name: 'tool_choice') Object? get toolChoice; bool? get stream; Map<String, String>? get transforms; List<String>? get models; String? get route; bool get fallback;
/// Create a copy of ChatCompletionRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ChatCompletionRequestCopyWith<ChatCompletionRequest> get copyWith => _$ChatCompletionRequestCopyWithImpl<ChatCompletionRequest>(this as ChatCompletionRequest, _$identity);

  /// Serializes this ChatCompletionRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChatCompletionRequest&&(identical(other.model, model) || other.model == model)&&const DeepCollectionEquality().equals(other.messages, messages)&&(identical(other.maxTokens, maxTokens) || other.maxTokens == maxTokens)&&(identical(other.temperature, temperature) || other.temperature == temperature)&&(identical(other.topP, topP) || other.topP == topP)&&(identical(other.topK, topK) || other.topK == topK)&&(identical(other.frequencyPenalty, frequencyPenalty) || other.frequencyPenalty == frequencyPenalty)&&(identical(other.presencePenalty, presencePenalty) || other.presencePenalty == presencePenalty)&&(identical(other.repetitionPenalty, repetitionPenalty) || other.repetitionPenalty == repetitionPenalty)&&(identical(other.minP, minP) || other.minP == minP)&&(identical(other.topA, topA) || other.topA == topA)&&(identical(other.seed, seed) || other.seed == seed)&&const DeepCollectionEquality().equals(other.logitBias, logitBias)&&(identical(other.logprobs, logprobs) || other.logprobs == logprobs)&&(identical(other.topLogprobs, topLogprobs) || other.topLogprobs == topLogprobs)&&(identical(other.responseFormat, responseFormat) || other.responseFormat == responseFormat)&&const DeepCollectionEquality().equals(other.stop, stop)&&const DeepCollectionEquality().equals(other.tools, tools)&&const DeepCollectionEquality().equals(other.toolChoice, toolChoice)&&(identical(other.stream, stream) || other.stream == stream)&&const DeepCollectionEquality().equals(other.transforms, transforms)&&const DeepCollectionEquality().equals(other.models, models)&&(identical(other.route, route) || other.route == route)&&(identical(other.fallback, fallback) || other.fallback == fallback));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,model,const DeepCollectionEquality().hash(messages),maxTokens,temperature,topP,topK,frequencyPenalty,presencePenalty,repetitionPenalty,minP,topA,seed,const DeepCollectionEquality().hash(logitBias),logprobs,topLogprobs,responseFormat,const DeepCollectionEquality().hash(stop),const DeepCollectionEquality().hash(tools),const DeepCollectionEquality().hash(toolChoice),stream,const DeepCollectionEquality().hash(transforms),const DeepCollectionEquality().hash(models),route,fallback]);

@override
String toString() {
  return 'ChatCompletionRequest(model: $model, messages: $messages, maxTokens: $maxTokens, temperature: $temperature, topP: $topP, topK: $topK, frequencyPenalty: $frequencyPenalty, presencePenalty: $presencePenalty, repetitionPenalty: $repetitionPenalty, minP: $minP, topA: $topA, seed: $seed, logitBias: $logitBias, logprobs: $logprobs, topLogprobs: $topLogprobs, responseFormat: $responseFormat, stop: $stop, tools: $tools, toolChoice: $toolChoice, stream: $stream, transforms: $transforms, models: $models, route: $route, fallback: $fallback)';
}


}

/// @nodoc
abstract mixin class $ChatCompletionRequestCopyWith<$Res>  {
  factory $ChatCompletionRequestCopyWith(ChatCompletionRequest value, $Res Function(ChatCompletionRequest) _then) = _$ChatCompletionRequestCopyWithImpl;
@useResult
$Res call({
 String model, List<ChatMessage> messages,@JsonKey(name: 'max_tokens') int? maxTokens, double? temperature,@JsonKey(name: 'top_p') double? topP,@JsonKey(name: 'top_k') int? topK,@JsonKey(name: 'frequency_penalty') double? frequencyPenalty,@JsonKey(name: 'presence_penalty') double? presencePenalty,@JsonKey(name: 'repetition_penalty') double? repetitionPenalty,@JsonKey(name: 'min_p') double? minP,@JsonKey(name: 'top_a') double? topA, int? seed,@JsonKey(name: 'logit_bias') Map<String, int>? logitBias, bool? logprobs,@JsonKey(name: 'top_logprobs') int? topLogprobs,@JsonKey(name: 'response_format') ResponseFormat? responseFormat, List<String>? stop, List<Tool>? tools,@JsonKey(name: 'tool_choice') Object? toolChoice, bool? stream, Map<String, String>? transforms, List<String>? models, String? route, bool fallback
});


$ResponseFormatCopyWith<$Res>? get responseFormat;

}
/// @nodoc
class _$ChatCompletionRequestCopyWithImpl<$Res>
    implements $ChatCompletionRequestCopyWith<$Res> {
  _$ChatCompletionRequestCopyWithImpl(this._self, this._then);

  final ChatCompletionRequest _self;
  final $Res Function(ChatCompletionRequest) _then;

/// Create a copy of ChatCompletionRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? model = null,Object? messages = null,Object? maxTokens = freezed,Object? temperature = freezed,Object? topP = freezed,Object? topK = freezed,Object? frequencyPenalty = freezed,Object? presencePenalty = freezed,Object? repetitionPenalty = freezed,Object? minP = freezed,Object? topA = freezed,Object? seed = freezed,Object? logitBias = freezed,Object? logprobs = freezed,Object? topLogprobs = freezed,Object? responseFormat = freezed,Object? stop = freezed,Object? tools = freezed,Object? toolChoice = freezed,Object? stream = freezed,Object? transforms = freezed,Object? models = freezed,Object? route = freezed,Object? fallback = null,}) {
  return _then(_self.copyWith(
model: null == model ? _self.model : model // ignore: cast_nullable_to_non_nullable
as String,messages: null == messages ? _self.messages : messages // ignore: cast_nullable_to_non_nullable
as List<ChatMessage>,maxTokens: freezed == maxTokens ? _self.maxTokens : maxTokens // ignore: cast_nullable_to_non_nullable
as int?,temperature: freezed == temperature ? _self.temperature : temperature // ignore: cast_nullable_to_non_nullable
as double?,topP: freezed == topP ? _self.topP : topP // ignore: cast_nullable_to_non_nullable
as double?,topK: freezed == topK ? _self.topK : topK // ignore: cast_nullable_to_non_nullable
as int?,frequencyPenalty: freezed == frequencyPenalty ? _self.frequencyPenalty : frequencyPenalty // ignore: cast_nullable_to_non_nullable
as double?,presencePenalty: freezed == presencePenalty ? _self.presencePenalty : presencePenalty // ignore: cast_nullable_to_non_nullable
as double?,repetitionPenalty: freezed == repetitionPenalty ? _self.repetitionPenalty : repetitionPenalty // ignore: cast_nullable_to_non_nullable
as double?,minP: freezed == minP ? _self.minP : minP // ignore: cast_nullable_to_non_nullable
as double?,topA: freezed == topA ? _self.topA : topA // ignore: cast_nullable_to_non_nullable
as double?,seed: freezed == seed ? _self.seed : seed // ignore: cast_nullable_to_non_nullable
as int?,logitBias: freezed == logitBias ? _self.logitBias : logitBias // ignore: cast_nullable_to_non_nullable
as Map<String, int>?,logprobs: freezed == logprobs ? _self.logprobs : logprobs // ignore: cast_nullable_to_non_nullable
as bool?,topLogprobs: freezed == topLogprobs ? _self.topLogprobs : topLogprobs // ignore: cast_nullable_to_non_nullable
as int?,responseFormat: freezed == responseFormat ? _self.responseFormat : responseFormat // ignore: cast_nullable_to_non_nullable
as ResponseFormat?,stop: freezed == stop ? _self.stop : stop // ignore: cast_nullable_to_non_nullable
as List<String>?,tools: freezed == tools ? _self.tools : tools // ignore: cast_nullable_to_non_nullable
as List<Tool>?,toolChoice: freezed == toolChoice ? _self.toolChoice : toolChoice ,stream: freezed == stream ? _self.stream : stream // ignore: cast_nullable_to_non_nullable
as bool?,transforms: freezed == transforms ? _self.transforms : transforms // ignore: cast_nullable_to_non_nullable
as Map<String, String>?,models: freezed == models ? _self.models : models // ignore: cast_nullable_to_non_nullable
as List<String>?,route: freezed == route ? _self.route : route // ignore: cast_nullable_to_non_nullable
as String?,fallback: null == fallback ? _self.fallback : fallback // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}
/// Create a copy of ChatCompletionRequest
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ResponseFormatCopyWith<$Res>? get responseFormat {
    if (_self.responseFormat == null) {
    return null;
  }

  return $ResponseFormatCopyWith<$Res>(_self.responseFormat!, (value) {
    return _then(_self.copyWith(responseFormat: value));
  });
}
}


/// Adds pattern-matching-related methods to [ChatCompletionRequest].
extension ChatCompletionRequestPatterns on ChatCompletionRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ChatCompletionRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ChatCompletionRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ChatCompletionRequest value)  $default,){
final _that = this;
switch (_that) {
case _ChatCompletionRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ChatCompletionRequest value)?  $default,){
final _that = this;
switch (_that) {
case _ChatCompletionRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String model,  List<ChatMessage> messages, @JsonKey(name: 'max_tokens')  int? maxTokens,  double? temperature, @JsonKey(name: 'top_p')  double? topP, @JsonKey(name: 'top_k')  int? topK, @JsonKey(name: 'frequency_penalty')  double? frequencyPenalty, @JsonKey(name: 'presence_penalty')  double? presencePenalty, @JsonKey(name: 'repetition_penalty')  double? repetitionPenalty, @JsonKey(name: 'min_p')  double? minP, @JsonKey(name: 'top_a')  double? topA,  int? seed, @JsonKey(name: 'logit_bias')  Map<String, int>? logitBias,  bool? logprobs, @JsonKey(name: 'top_logprobs')  int? topLogprobs, @JsonKey(name: 'response_format')  ResponseFormat? responseFormat,  List<String>? stop,  List<Tool>? tools, @JsonKey(name: 'tool_choice')  Object? toolChoice,  bool? stream,  Map<String, String>? transforms,  List<String>? models,  String? route,  bool fallback)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ChatCompletionRequest() when $default != null:
return $default(_that.model,_that.messages,_that.maxTokens,_that.temperature,_that.topP,_that.topK,_that.frequencyPenalty,_that.presencePenalty,_that.repetitionPenalty,_that.minP,_that.topA,_that.seed,_that.logitBias,_that.logprobs,_that.topLogprobs,_that.responseFormat,_that.stop,_that.tools,_that.toolChoice,_that.stream,_that.transforms,_that.models,_that.route,_that.fallback);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String model,  List<ChatMessage> messages, @JsonKey(name: 'max_tokens')  int? maxTokens,  double? temperature, @JsonKey(name: 'top_p')  double? topP, @JsonKey(name: 'top_k')  int? topK, @JsonKey(name: 'frequency_penalty')  double? frequencyPenalty, @JsonKey(name: 'presence_penalty')  double? presencePenalty, @JsonKey(name: 'repetition_penalty')  double? repetitionPenalty, @JsonKey(name: 'min_p')  double? minP, @JsonKey(name: 'top_a')  double? topA,  int? seed, @JsonKey(name: 'logit_bias')  Map<String, int>? logitBias,  bool? logprobs, @JsonKey(name: 'top_logprobs')  int? topLogprobs, @JsonKey(name: 'response_format')  ResponseFormat? responseFormat,  List<String>? stop,  List<Tool>? tools, @JsonKey(name: 'tool_choice')  Object? toolChoice,  bool? stream,  Map<String, String>? transforms,  List<String>? models,  String? route,  bool fallback)  $default,) {final _that = this;
switch (_that) {
case _ChatCompletionRequest():
return $default(_that.model,_that.messages,_that.maxTokens,_that.temperature,_that.topP,_that.topK,_that.frequencyPenalty,_that.presencePenalty,_that.repetitionPenalty,_that.minP,_that.topA,_that.seed,_that.logitBias,_that.logprobs,_that.topLogprobs,_that.responseFormat,_that.stop,_that.tools,_that.toolChoice,_that.stream,_that.transforms,_that.models,_that.route,_that.fallback);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String model,  List<ChatMessage> messages, @JsonKey(name: 'max_tokens')  int? maxTokens,  double? temperature, @JsonKey(name: 'top_p')  double? topP, @JsonKey(name: 'top_k')  int? topK, @JsonKey(name: 'frequency_penalty')  double? frequencyPenalty, @JsonKey(name: 'presence_penalty')  double? presencePenalty, @JsonKey(name: 'repetition_penalty')  double? repetitionPenalty, @JsonKey(name: 'min_p')  double? minP, @JsonKey(name: 'top_a')  double? topA,  int? seed, @JsonKey(name: 'logit_bias')  Map<String, int>? logitBias,  bool? logprobs, @JsonKey(name: 'top_logprobs')  int? topLogprobs, @JsonKey(name: 'response_format')  ResponseFormat? responseFormat,  List<String>? stop,  List<Tool>? tools, @JsonKey(name: 'tool_choice')  Object? toolChoice,  bool? stream,  Map<String, String>? transforms,  List<String>? models,  String? route,  bool fallback)?  $default,) {final _that = this;
switch (_that) {
case _ChatCompletionRequest() when $default != null:
return $default(_that.model,_that.messages,_that.maxTokens,_that.temperature,_that.topP,_that.topK,_that.frequencyPenalty,_that.presencePenalty,_that.repetitionPenalty,_that.minP,_that.topA,_that.seed,_that.logitBias,_that.logprobs,_that.topLogprobs,_that.responseFormat,_that.stop,_that.tools,_that.toolChoice,_that.stream,_that.transforms,_that.models,_that.route,_that.fallback);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ChatCompletionRequest implements ChatCompletionRequest {
  const _ChatCompletionRequest({required this.model, required final  List<ChatMessage> messages, @JsonKey(name: 'max_tokens') this.maxTokens, this.temperature, @JsonKey(name: 'top_p') this.topP, @JsonKey(name: 'top_k') this.topK, @JsonKey(name: 'frequency_penalty') this.frequencyPenalty, @JsonKey(name: 'presence_penalty') this.presencePenalty, @JsonKey(name: 'repetition_penalty') this.repetitionPenalty, @JsonKey(name: 'min_p') this.minP, @JsonKey(name: 'top_a') this.topA, this.seed, @JsonKey(name: 'logit_bias') final  Map<String, int>? logitBias, this.logprobs, @JsonKey(name: 'top_logprobs') this.topLogprobs, @JsonKey(name: 'response_format') this.responseFormat, final  List<String>? stop, final  List<Tool>? tools, @JsonKey(name: 'tool_choice') this.toolChoice, this.stream, final  Map<String, String>? transforms, final  List<String>? models, this.route, this.fallback = false}): _messages = messages,_logitBias = logitBias,_stop = stop,_tools = tools,_transforms = transforms,_models = models;
  factory _ChatCompletionRequest.fromJson(Map<String, dynamic> json) => _$ChatCompletionRequestFromJson(json);

@override final  String model;
 final  List<ChatMessage> _messages;
@override List<ChatMessage> get messages {
  if (_messages is EqualUnmodifiableListView) return _messages;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_messages);
}

@override@JsonKey(name: 'max_tokens') final  int? maxTokens;
@override final  double? temperature;
@override@JsonKey(name: 'top_p') final  double? topP;
@override@JsonKey(name: 'top_k') final  int? topK;
@override@JsonKey(name: 'frequency_penalty') final  double? frequencyPenalty;
@override@JsonKey(name: 'presence_penalty') final  double? presencePenalty;
@override@JsonKey(name: 'repetition_penalty') final  double? repetitionPenalty;
@override@JsonKey(name: 'min_p') final  double? minP;
@override@JsonKey(name: 'top_a') final  double? topA;
@override final  int? seed;
 final  Map<String, int>? _logitBias;
@override@JsonKey(name: 'logit_bias') Map<String, int>? get logitBias {
  final value = _logitBias;
  if (value == null) return null;
  if (_logitBias is EqualUnmodifiableMapView) return _logitBias;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override final  bool? logprobs;
@override@JsonKey(name: 'top_logprobs') final  int? topLogprobs;
@override@JsonKey(name: 'response_format') final  ResponseFormat? responseFormat;
 final  List<String>? _stop;
@override List<String>? get stop {
  final value = _stop;
  if (value == null) return null;
  if (_stop is EqualUnmodifiableListView) return _stop;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<Tool>? _tools;
@override List<Tool>? get tools {
  final value = _tools;
  if (value == null) return null;
  if (_tools is EqualUnmodifiableListView) return _tools;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override@JsonKey(name: 'tool_choice') final  Object? toolChoice;
@override final  bool? stream;
 final  Map<String, String>? _transforms;
@override Map<String, String>? get transforms {
  final value = _transforms;
  if (value == null) return null;
  if (_transforms is EqualUnmodifiableMapView) return _transforms;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

 final  List<String>? _models;
@override List<String>? get models {
  final value = _models;
  if (value == null) return null;
  if (_models is EqualUnmodifiableListView) return _models;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override final  String? route;
@override@JsonKey() final  bool fallback;

/// Create a copy of ChatCompletionRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChatCompletionRequestCopyWith<_ChatCompletionRequest> get copyWith => __$ChatCompletionRequestCopyWithImpl<_ChatCompletionRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ChatCompletionRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChatCompletionRequest&&(identical(other.model, model) || other.model == model)&&const DeepCollectionEquality().equals(other._messages, _messages)&&(identical(other.maxTokens, maxTokens) || other.maxTokens == maxTokens)&&(identical(other.temperature, temperature) || other.temperature == temperature)&&(identical(other.topP, topP) || other.topP == topP)&&(identical(other.topK, topK) || other.topK == topK)&&(identical(other.frequencyPenalty, frequencyPenalty) || other.frequencyPenalty == frequencyPenalty)&&(identical(other.presencePenalty, presencePenalty) || other.presencePenalty == presencePenalty)&&(identical(other.repetitionPenalty, repetitionPenalty) || other.repetitionPenalty == repetitionPenalty)&&(identical(other.minP, minP) || other.minP == minP)&&(identical(other.topA, topA) || other.topA == topA)&&(identical(other.seed, seed) || other.seed == seed)&&const DeepCollectionEquality().equals(other._logitBias, _logitBias)&&(identical(other.logprobs, logprobs) || other.logprobs == logprobs)&&(identical(other.topLogprobs, topLogprobs) || other.topLogprobs == topLogprobs)&&(identical(other.responseFormat, responseFormat) || other.responseFormat == responseFormat)&&const DeepCollectionEquality().equals(other._stop, _stop)&&const DeepCollectionEquality().equals(other._tools, _tools)&&const DeepCollectionEquality().equals(other.toolChoice, toolChoice)&&(identical(other.stream, stream) || other.stream == stream)&&const DeepCollectionEquality().equals(other._transforms, _transforms)&&const DeepCollectionEquality().equals(other._models, _models)&&(identical(other.route, route) || other.route == route)&&(identical(other.fallback, fallback) || other.fallback == fallback));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,model,const DeepCollectionEquality().hash(_messages),maxTokens,temperature,topP,topK,frequencyPenalty,presencePenalty,repetitionPenalty,minP,topA,seed,const DeepCollectionEquality().hash(_logitBias),logprobs,topLogprobs,responseFormat,const DeepCollectionEquality().hash(_stop),const DeepCollectionEquality().hash(_tools),const DeepCollectionEquality().hash(toolChoice),stream,const DeepCollectionEquality().hash(_transforms),const DeepCollectionEquality().hash(_models),route,fallback]);

@override
String toString() {
  return 'ChatCompletionRequest(model: $model, messages: $messages, maxTokens: $maxTokens, temperature: $temperature, topP: $topP, topK: $topK, frequencyPenalty: $frequencyPenalty, presencePenalty: $presencePenalty, repetitionPenalty: $repetitionPenalty, minP: $minP, topA: $topA, seed: $seed, logitBias: $logitBias, logprobs: $logprobs, topLogprobs: $topLogprobs, responseFormat: $responseFormat, stop: $stop, tools: $tools, toolChoice: $toolChoice, stream: $stream, transforms: $transforms, models: $models, route: $route, fallback: $fallback)';
}


}

/// @nodoc
abstract mixin class _$ChatCompletionRequestCopyWith<$Res> implements $ChatCompletionRequestCopyWith<$Res> {
  factory _$ChatCompletionRequestCopyWith(_ChatCompletionRequest value, $Res Function(_ChatCompletionRequest) _then) = __$ChatCompletionRequestCopyWithImpl;
@override @useResult
$Res call({
 String model, List<ChatMessage> messages,@JsonKey(name: 'max_tokens') int? maxTokens, double? temperature,@JsonKey(name: 'top_p') double? topP,@JsonKey(name: 'top_k') int? topK,@JsonKey(name: 'frequency_penalty') double? frequencyPenalty,@JsonKey(name: 'presence_penalty') double? presencePenalty,@JsonKey(name: 'repetition_penalty') double? repetitionPenalty,@JsonKey(name: 'min_p') double? minP,@JsonKey(name: 'top_a') double? topA, int? seed,@JsonKey(name: 'logit_bias') Map<String, int>? logitBias, bool? logprobs,@JsonKey(name: 'top_logprobs') int? topLogprobs,@JsonKey(name: 'response_format') ResponseFormat? responseFormat, List<String>? stop, List<Tool>? tools,@JsonKey(name: 'tool_choice') Object? toolChoice, bool? stream, Map<String, String>? transforms, List<String>? models, String? route, bool fallback
});


@override $ResponseFormatCopyWith<$Res>? get responseFormat;

}
/// @nodoc
class __$ChatCompletionRequestCopyWithImpl<$Res>
    implements _$ChatCompletionRequestCopyWith<$Res> {
  __$ChatCompletionRequestCopyWithImpl(this._self, this._then);

  final _ChatCompletionRequest _self;
  final $Res Function(_ChatCompletionRequest) _then;

/// Create a copy of ChatCompletionRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? model = null,Object? messages = null,Object? maxTokens = freezed,Object? temperature = freezed,Object? topP = freezed,Object? topK = freezed,Object? frequencyPenalty = freezed,Object? presencePenalty = freezed,Object? repetitionPenalty = freezed,Object? minP = freezed,Object? topA = freezed,Object? seed = freezed,Object? logitBias = freezed,Object? logprobs = freezed,Object? topLogprobs = freezed,Object? responseFormat = freezed,Object? stop = freezed,Object? tools = freezed,Object? toolChoice = freezed,Object? stream = freezed,Object? transforms = freezed,Object? models = freezed,Object? route = freezed,Object? fallback = null,}) {
  return _then(_ChatCompletionRequest(
model: null == model ? _self.model : model // ignore: cast_nullable_to_non_nullable
as String,messages: null == messages ? _self._messages : messages // ignore: cast_nullable_to_non_nullable
as List<ChatMessage>,maxTokens: freezed == maxTokens ? _self.maxTokens : maxTokens // ignore: cast_nullable_to_non_nullable
as int?,temperature: freezed == temperature ? _self.temperature : temperature // ignore: cast_nullable_to_non_nullable
as double?,topP: freezed == topP ? _self.topP : topP // ignore: cast_nullable_to_non_nullable
as double?,topK: freezed == topK ? _self.topK : topK // ignore: cast_nullable_to_non_nullable
as int?,frequencyPenalty: freezed == frequencyPenalty ? _self.frequencyPenalty : frequencyPenalty // ignore: cast_nullable_to_non_nullable
as double?,presencePenalty: freezed == presencePenalty ? _self.presencePenalty : presencePenalty // ignore: cast_nullable_to_non_nullable
as double?,repetitionPenalty: freezed == repetitionPenalty ? _self.repetitionPenalty : repetitionPenalty // ignore: cast_nullable_to_non_nullable
as double?,minP: freezed == minP ? _self.minP : minP // ignore: cast_nullable_to_non_nullable
as double?,topA: freezed == topA ? _self.topA : topA // ignore: cast_nullable_to_non_nullable
as double?,seed: freezed == seed ? _self.seed : seed // ignore: cast_nullable_to_non_nullable
as int?,logitBias: freezed == logitBias ? _self._logitBias : logitBias // ignore: cast_nullable_to_non_nullable
as Map<String, int>?,logprobs: freezed == logprobs ? _self.logprobs : logprobs // ignore: cast_nullable_to_non_nullable
as bool?,topLogprobs: freezed == topLogprobs ? _self.topLogprobs : topLogprobs // ignore: cast_nullable_to_non_nullable
as int?,responseFormat: freezed == responseFormat ? _self.responseFormat : responseFormat // ignore: cast_nullable_to_non_nullable
as ResponseFormat?,stop: freezed == stop ? _self._stop : stop // ignore: cast_nullable_to_non_nullable
as List<String>?,tools: freezed == tools ? _self._tools : tools // ignore: cast_nullable_to_non_nullable
as List<Tool>?,toolChoice: freezed == toolChoice ? _self.toolChoice : toolChoice ,stream: freezed == stream ? _self.stream : stream // ignore: cast_nullable_to_non_nullable
as bool?,transforms: freezed == transforms ? _self._transforms : transforms // ignore: cast_nullable_to_non_nullable
as Map<String, String>?,models: freezed == models ? _self._models : models // ignore: cast_nullable_to_non_nullable
as List<String>?,route: freezed == route ? _self.route : route // ignore: cast_nullable_to_non_nullable
as String?,fallback: null == fallback ? _self.fallback : fallback // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

/// Create a copy of ChatCompletionRequest
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ResponseFormatCopyWith<$Res>? get responseFormat {
    if (_self.responseFormat == null) {
    return null;
  }

  return $ResponseFormatCopyWith<$Res>(_self.responseFormat!, (value) {
    return _then(_self.copyWith(responseFormat: value));
  });
}
}


/// @nodoc
mixin _$ResponseFormat {

 String get type;
/// Create a copy of ResponseFormat
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ResponseFormatCopyWith<ResponseFormat> get copyWith => _$ResponseFormatCopyWithImpl<ResponseFormat>(this as ResponseFormat, _$identity);

  /// Serializes this ResponseFormat to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ResponseFormat&&(identical(other.type, type) || other.type == type));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,type);

@override
String toString() {
  return 'ResponseFormat(type: $type)';
}


}

/// @nodoc
abstract mixin class $ResponseFormatCopyWith<$Res>  {
  factory $ResponseFormatCopyWith(ResponseFormat value, $Res Function(ResponseFormat) _then) = _$ResponseFormatCopyWithImpl;
@useResult
$Res call({
 String type
});




}
/// @nodoc
class _$ResponseFormatCopyWithImpl<$Res>
    implements $ResponseFormatCopyWith<$Res> {
  _$ResponseFormatCopyWithImpl(this._self, this._then);

  final ResponseFormat _self;
  final $Res Function(ResponseFormat) _then;

/// Create a copy of ResponseFormat
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? type = null,}) {
  return _then(_self.copyWith(
type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [ResponseFormat].
extension ResponseFormatPatterns on ResponseFormat {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ResponseFormat value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ResponseFormat() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ResponseFormat value)  $default,){
final _that = this;
switch (_that) {
case _ResponseFormat():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ResponseFormat value)?  $default,){
final _that = this;
switch (_that) {
case _ResponseFormat() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String type)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ResponseFormat() when $default != null:
return $default(_that.type);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String type)  $default,) {final _that = this;
switch (_that) {
case _ResponseFormat():
return $default(_that.type);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String type)?  $default,) {final _that = this;
switch (_that) {
case _ResponseFormat() when $default != null:
return $default(_that.type);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ResponseFormat implements ResponseFormat {
  const _ResponseFormat({required this.type});
  factory _ResponseFormat.fromJson(Map<String, dynamic> json) => _$ResponseFormatFromJson(json);

@override final  String type;

/// Create a copy of ResponseFormat
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ResponseFormatCopyWith<_ResponseFormat> get copyWith => __$ResponseFormatCopyWithImpl<_ResponseFormat>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ResponseFormatToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ResponseFormat&&(identical(other.type, type) || other.type == type));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,type);

@override
String toString() {
  return 'ResponseFormat(type: $type)';
}


}

/// @nodoc
abstract mixin class _$ResponseFormatCopyWith<$Res> implements $ResponseFormatCopyWith<$Res> {
  factory _$ResponseFormatCopyWith(_ResponseFormat value, $Res Function(_ResponseFormat) _then) = __$ResponseFormatCopyWithImpl;
@override @useResult
$Res call({
 String type
});




}
/// @nodoc
class __$ResponseFormatCopyWithImpl<$Res>
    implements _$ResponseFormatCopyWith<$Res> {
  __$ResponseFormatCopyWithImpl(this._self, this._then);

  final _ResponseFormat _self;
  final $Res Function(_ResponseFormat) _then;

/// Create a copy of ResponseFormat
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? type = null,}) {
  return _then(_ResponseFormat(
type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$Tool {

 String get type; ToolFunctionSchema get function;
/// Create a copy of Tool
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ToolCopyWith<Tool> get copyWith => _$ToolCopyWithImpl<Tool>(this as Tool, _$identity);

  /// Serializes this Tool to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Tool&&(identical(other.type, type) || other.type == type)&&(identical(other.function, function) || other.function == function));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,type,function);

@override
String toString() {
  return 'Tool(type: $type, function: $function)';
}


}

/// @nodoc
abstract mixin class $ToolCopyWith<$Res>  {
  factory $ToolCopyWith(Tool value, $Res Function(Tool) _then) = _$ToolCopyWithImpl;
@useResult
$Res call({
 String type, ToolFunctionSchema function
});


$ToolFunctionSchemaCopyWith<$Res> get function;

}
/// @nodoc
class _$ToolCopyWithImpl<$Res>
    implements $ToolCopyWith<$Res> {
  _$ToolCopyWithImpl(this._self, this._then);

  final Tool _self;
  final $Res Function(Tool) _then;

/// Create a copy of Tool
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? type = null,Object? function = null,}) {
  return _then(_self.copyWith(
type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String,function: null == function ? _self.function : function // ignore: cast_nullable_to_non_nullable
as ToolFunctionSchema,
  ));
}
/// Create a copy of Tool
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ToolFunctionSchemaCopyWith<$Res> get function {
  
  return $ToolFunctionSchemaCopyWith<$Res>(_self.function, (value) {
    return _then(_self.copyWith(function: value));
  });
}
}


/// Adds pattern-matching-related methods to [Tool].
extension ToolPatterns on Tool {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Tool value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Tool() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Tool value)  $default,){
final _that = this;
switch (_that) {
case _Tool():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Tool value)?  $default,){
final _that = this;
switch (_that) {
case _Tool() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String type,  ToolFunctionSchema function)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Tool() when $default != null:
return $default(_that.type,_that.function);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String type,  ToolFunctionSchema function)  $default,) {final _that = this;
switch (_that) {
case _Tool():
return $default(_that.type,_that.function);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String type,  ToolFunctionSchema function)?  $default,) {final _that = this;
switch (_that) {
case _Tool() when $default != null:
return $default(_that.type,_that.function);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Tool implements Tool {
  const _Tool({required this.type, required this.function});
  factory _Tool.fromJson(Map<String, dynamic> json) => _$ToolFromJson(json);

@override final  String type;
@override final  ToolFunctionSchema function;

/// Create a copy of Tool
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ToolCopyWith<_Tool> get copyWith => __$ToolCopyWithImpl<_Tool>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ToolToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Tool&&(identical(other.type, type) || other.type == type)&&(identical(other.function, function) || other.function == function));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,type,function);

@override
String toString() {
  return 'Tool(type: $type, function: $function)';
}


}

/// @nodoc
abstract mixin class _$ToolCopyWith<$Res> implements $ToolCopyWith<$Res> {
  factory _$ToolCopyWith(_Tool value, $Res Function(_Tool) _then) = __$ToolCopyWithImpl;
@override @useResult
$Res call({
 String type, ToolFunctionSchema function
});


@override $ToolFunctionSchemaCopyWith<$Res> get function;

}
/// @nodoc
class __$ToolCopyWithImpl<$Res>
    implements _$ToolCopyWith<$Res> {
  __$ToolCopyWithImpl(this._self, this._then);

  final _Tool _self;
  final $Res Function(_Tool) _then;

/// Create a copy of Tool
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? type = null,Object? function = null,}) {
  return _then(_Tool(
type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String,function: null == function ? _self.function : function // ignore: cast_nullable_to_non_nullable
as ToolFunctionSchema,
  ));
}

/// Create a copy of Tool
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ToolFunctionSchemaCopyWith<$Res> get function {
  
  return $ToolFunctionSchemaCopyWith<$Res>(_self.function, (value) {
    return _then(_self.copyWith(function: value));
  });
}
}


/// @nodoc
mixin _$ToolFunctionSchema {

 String get name; String? get description; Map<String, dynamic>? get parameters;
/// Create a copy of ToolFunctionSchema
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ToolFunctionSchemaCopyWith<ToolFunctionSchema> get copyWith => _$ToolFunctionSchemaCopyWithImpl<ToolFunctionSchema>(this as ToolFunctionSchema, _$identity);

  /// Serializes this ToolFunctionSchema to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ToolFunctionSchema&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&const DeepCollectionEquality().equals(other.parameters, parameters));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,description,const DeepCollectionEquality().hash(parameters));

@override
String toString() {
  return 'ToolFunctionSchema(name: $name, description: $description, parameters: $parameters)';
}


}

/// @nodoc
abstract mixin class $ToolFunctionSchemaCopyWith<$Res>  {
  factory $ToolFunctionSchemaCopyWith(ToolFunctionSchema value, $Res Function(ToolFunctionSchema) _then) = _$ToolFunctionSchemaCopyWithImpl;
@useResult
$Res call({
 String name, String? description, Map<String, dynamic>? parameters
});




}
/// @nodoc
class _$ToolFunctionSchemaCopyWithImpl<$Res>
    implements $ToolFunctionSchemaCopyWith<$Res> {
  _$ToolFunctionSchemaCopyWithImpl(this._self, this._then);

  final ToolFunctionSchema _self;
  final $Res Function(ToolFunctionSchema) _then;

/// Create a copy of ToolFunctionSchema
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = null,Object? description = freezed,Object? parameters = freezed,}) {
  return _then(_self.copyWith(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,parameters: freezed == parameters ? _self.parameters : parameters // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [ToolFunctionSchema].
extension ToolFunctionSchemaPatterns on ToolFunctionSchema {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ToolFunctionSchema value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ToolFunctionSchema() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ToolFunctionSchema value)  $default,){
final _that = this;
switch (_that) {
case _ToolFunctionSchema():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ToolFunctionSchema value)?  $default,){
final _that = this;
switch (_that) {
case _ToolFunctionSchema() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String name,  String? description,  Map<String, dynamic>? parameters)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ToolFunctionSchema() when $default != null:
return $default(_that.name,_that.description,_that.parameters);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String name,  String? description,  Map<String, dynamic>? parameters)  $default,) {final _that = this;
switch (_that) {
case _ToolFunctionSchema():
return $default(_that.name,_that.description,_that.parameters);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String name,  String? description,  Map<String, dynamic>? parameters)?  $default,) {final _that = this;
switch (_that) {
case _ToolFunctionSchema() when $default != null:
return $default(_that.name,_that.description,_that.parameters);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ToolFunctionSchema implements ToolFunctionSchema {
  const _ToolFunctionSchema({required this.name, this.description, final  Map<String, dynamic>? parameters}): _parameters = parameters;
  factory _ToolFunctionSchema.fromJson(Map<String, dynamic> json) => _$ToolFunctionSchemaFromJson(json);

@override final  String name;
@override final  String? description;
 final  Map<String, dynamic>? _parameters;
@override Map<String, dynamic>? get parameters {
  final value = _parameters;
  if (value == null) return null;
  if (_parameters is EqualUnmodifiableMapView) return _parameters;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of ToolFunctionSchema
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ToolFunctionSchemaCopyWith<_ToolFunctionSchema> get copyWith => __$ToolFunctionSchemaCopyWithImpl<_ToolFunctionSchema>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ToolFunctionSchemaToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ToolFunctionSchema&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&const DeepCollectionEquality().equals(other._parameters, _parameters));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,description,const DeepCollectionEquality().hash(_parameters));

@override
String toString() {
  return 'ToolFunctionSchema(name: $name, description: $description, parameters: $parameters)';
}


}

/// @nodoc
abstract mixin class _$ToolFunctionSchemaCopyWith<$Res> implements $ToolFunctionSchemaCopyWith<$Res> {
  factory _$ToolFunctionSchemaCopyWith(_ToolFunctionSchema value, $Res Function(_ToolFunctionSchema) _then) = __$ToolFunctionSchemaCopyWithImpl;
@override @useResult
$Res call({
 String name, String? description, Map<String, dynamic>? parameters
});




}
/// @nodoc
class __$ToolFunctionSchemaCopyWithImpl<$Res>
    implements _$ToolFunctionSchemaCopyWith<$Res> {
  __$ToolFunctionSchemaCopyWithImpl(this._self, this._then);

  final _ToolFunctionSchema _self;
  final $Res Function(_ToolFunctionSchema) _then;

/// Create a copy of ToolFunctionSchema
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = null,Object? description = freezed,Object? parameters = freezed,}) {
  return _then(_ToolFunctionSchema(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,parameters: freezed == parameters ? _self._parameters : parameters // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$ChatCompletionResponse {

 String get id; String get object; int get created; String get model; List<ChatCompletionChoice> get choices; Usage get usage;@JsonKey(name: 'system_fingerprint') String? get systemFingerprint;
/// Create a copy of ChatCompletionResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ChatCompletionResponseCopyWith<ChatCompletionResponse> get copyWith => _$ChatCompletionResponseCopyWithImpl<ChatCompletionResponse>(this as ChatCompletionResponse, _$identity);

  /// Serializes this ChatCompletionResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChatCompletionResponse&&(identical(other.id, id) || other.id == id)&&(identical(other.object, object) || other.object == object)&&(identical(other.created, created) || other.created == created)&&(identical(other.model, model) || other.model == model)&&const DeepCollectionEquality().equals(other.choices, choices)&&(identical(other.usage, usage) || other.usage == usage)&&(identical(other.systemFingerprint, systemFingerprint) || other.systemFingerprint == systemFingerprint));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,object,created,model,const DeepCollectionEquality().hash(choices),usage,systemFingerprint);

@override
String toString() {
  return 'ChatCompletionResponse(id: $id, object: $object, created: $created, model: $model, choices: $choices, usage: $usage, systemFingerprint: $systemFingerprint)';
}


}

/// @nodoc
abstract mixin class $ChatCompletionResponseCopyWith<$Res>  {
  factory $ChatCompletionResponseCopyWith(ChatCompletionResponse value, $Res Function(ChatCompletionResponse) _then) = _$ChatCompletionResponseCopyWithImpl;
@useResult
$Res call({
 String id, String object, int created, String model, List<ChatCompletionChoice> choices, Usage usage,@JsonKey(name: 'system_fingerprint') String? systemFingerprint
});


$UsageCopyWith<$Res> get usage;

}
/// @nodoc
class _$ChatCompletionResponseCopyWithImpl<$Res>
    implements $ChatCompletionResponseCopyWith<$Res> {
  _$ChatCompletionResponseCopyWithImpl(this._self, this._then);

  final ChatCompletionResponse _self;
  final $Res Function(ChatCompletionResponse) _then;

/// Create a copy of ChatCompletionResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? object = null,Object? created = null,Object? model = null,Object? choices = null,Object? usage = null,Object? systemFingerprint = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,object: null == object ? _self.object : object // ignore: cast_nullable_to_non_nullable
as String,created: null == created ? _self.created : created // ignore: cast_nullable_to_non_nullable
as int,model: null == model ? _self.model : model // ignore: cast_nullable_to_non_nullable
as String,choices: null == choices ? _self.choices : choices // ignore: cast_nullable_to_non_nullable
as List<ChatCompletionChoice>,usage: null == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as Usage,systemFingerprint: freezed == systemFingerprint ? _self.systemFingerprint : systemFingerprint // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}
/// Create a copy of ChatCompletionResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UsageCopyWith<$Res> get usage {
  
  return $UsageCopyWith<$Res>(_self.usage, (value) {
    return _then(_self.copyWith(usage: value));
  });
}
}


/// Adds pattern-matching-related methods to [ChatCompletionResponse].
extension ChatCompletionResponsePatterns on ChatCompletionResponse {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ChatCompletionResponse value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ChatCompletionResponse() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ChatCompletionResponse value)  $default,){
final _that = this;
switch (_that) {
case _ChatCompletionResponse():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ChatCompletionResponse value)?  $default,){
final _that = this;
switch (_that) {
case _ChatCompletionResponse() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String object,  int created,  String model,  List<ChatCompletionChoice> choices,  Usage usage, @JsonKey(name: 'system_fingerprint')  String? systemFingerprint)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ChatCompletionResponse() when $default != null:
return $default(_that.id,_that.object,_that.created,_that.model,_that.choices,_that.usage,_that.systemFingerprint);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String object,  int created,  String model,  List<ChatCompletionChoice> choices,  Usage usage, @JsonKey(name: 'system_fingerprint')  String? systemFingerprint)  $default,) {final _that = this;
switch (_that) {
case _ChatCompletionResponse():
return $default(_that.id,_that.object,_that.created,_that.model,_that.choices,_that.usage,_that.systemFingerprint);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String object,  int created,  String model,  List<ChatCompletionChoice> choices,  Usage usage, @JsonKey(name: 'system_fingerprint')  String? systemFingerprint)?  $default,) {final _that = this;
switch (_that) {
case _ChatCompletionResponse() when $default != null:
return $default(_that.id,_that.object,_that.created,_that.model,_that.choices,_that.usage,_that.systemFingerprint);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ChatCompletionResponse implements ChatCompletionResponse {
  const _ChatCompletionResponse({required this.id, required this.object, required this.created, required this.model, required final  List<ChatCompletionChoice> choices, required this.usage, @JsonKey(name: 'system_fingerprint') this.systemFingerprint}): _choices = choices;
  factory _ChatCompletionResponse.fromJson(Map<String, dynamic> json) => _$ChatCompletionResponseFromJson(json);

@override final  String id;
@override final  String object;
@override final  int created;
@override final  String model;
 final  List<ChatCompletionChoice> _choices;
@override List<ChatCompletionChoice> get choices {
  if (_choices is EqualUnmodifiableListView) return _choices;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_choices);
}

@override final  Usage usage;
@override@JsonKey(name: 'system_fingerprint') final  String? systemFingerprint;

/// Create a copy of ChatCompletionResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChatCompletionResponseCopyWith<_ChatCompletionResponse> get copyWith => __$ChatCompletionResponseCopyWithImpl<_ChatCompletionResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ChatCompletionResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChatCompletionResponse&&(identical(other.id, id) || other.id == id)&&(identical(other.object, object) || other.object == object)&&(identical(other.created, created) || other.created == created)&&(identical(other.model, model) || other.model == model)&&const DeepCollectionEquality().equals(other._choices, _choices)&&(identical(other.usage, usage) || other.usage == usage)&&(identical(other.systemFingerprint, systemFingerprint) || other.systemFingerprint == systemFingerprint));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,object,created,model,const DeepCollectionEquality().hash(_choices),usage,systemFingerprint);

@override
String toString() {
  return 'ChatCompletionResponse(id: $id, object: $object, created: $created, model: $model, choices: $choices, usage: $usage, systemFingerprint: $systemFingerprint)';
}


}

/// @nodoc
abstract mixin class _$ChatCompletionResponseCopyWith<$Res> implements $ChatCompletionResponseCopyWith<$Res> {
  factory _$ChatCompletionResponseCopyWith(_ChatCompletionResponse value, $Res Function(_ChatCompletionResponse) _then) = __$ChatCompletionResponseCopyWithImpl;
@override @useResult
$Res call({
 String id, String object, int created, String model, List<ChatCompletionChoice> choices, Usage usage,@JsonKey(name: 'system_fingerprint') String? systemFingerprint
});


@override $UsageCopyWith<$Res> get usage;

}
/// @nodoc
class __$ChatCompletionResponseCopyWithImpl<$Res>
    implements _$ChatCompletionResponseCopyWith<$Res> {
  __$ChatCompletionResponseCopyWithImpl(this._self, this._then);

  final _ChatCompletionResponse _self;
  final $Res Function(_ChatCompletionResponse) _then;

/// Create a copy of ChatCompletionResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? object = null,Object? created = null,Object? model = null,Object? choices = null,Object? usage = null,Object? systemFingerprint = freezed,}) {
  return _then(_ChatCompletionResponse(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,object: null == object ? _self.object : object // ignore: cast_nullable_to_non_nullable
as String,created: null == created ? _self.created : created // ignore: cast_nullable_to_non_nullable
as int,model: null == model ? _self.model : model // ignore: cast_nullable_to_non_nullable
as String,choices: null == choices ? _self._choices : choices // ignore: cast_nullable_to_non_nullable
as List<ChatCompletionChoice>,usage: null == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as Usage,systemFingerprint: freezed == systemFingerprint ? _self.systemFingerprint : systemFingerprint // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of ChatCompletionResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UsageCopyWith<$Res> get usage {
  
  return $UsageCopyWith<$Res>(_self.usage, (value) {
    return _then(_self.copyWith(usage: value));
  });
}
}


/// @nodoc
mixin _$ChatCompletionChoice {

 int get index; ChatMessage get message;@JsonKey(name: 'finish_reason') String? get finishReason; Logprobs? get logprobs;
/// Create a copy of ChatCompletionChoice
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ChatCompletionChoiceCopyWith<ChatCompletionChoice> get copyWith => _$ChatCompletionChoiceCopyWithImpl<ChatCompletionChoice>(this as ChatCompletionChoice, _$identity);

  /// Serializes this ChatCompletionChoice to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChatCompletionChoice&&(identical(other.index, index) || other.index == index)&&(identical(other.message, message) || other.message == message)&&(identical(other.finishReason, finishReason) || other.finishReason == finishReason)&&(identical(other.logprobs, logprobs) || other.logprobs == logprobs));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,index,message,finishReason,logprobs);

@override
String toString() {
  return 'ChatCompletionChoice(index: $index, message: $message, finishReason: $finishReason, logprobs: $logprobs)';
}


}

/// @nodoc
abstract mixin class $ChatCompletionChoiceCopyWith<$Res>  {
  factory $ChatCompletionChoiceCopyWith(ChatCompletionChoice value, $Res Function(ChatCompletionChoice) _then) = _$ChatCompletionChoiceCopyWithImpl;
@useResult
$Res call({
 int index, ChatMessage message,@JsonKey(name: 'finish_reason') String? finishReason, Logprobs? logprobs
});


$ChatMessageCopyWith<$Res> get message;$LogprobsCopyWith<$Res>? get logprobs;

}
/// @nodoc
class _$ChatCompletionChoiceCopyWithImpl<$Res>
    implements $ChatCompletionChoiceCopyWith<$Res> {
  _$ChatCompletionChoiceCopyWithImpl(this._self, this._then);

  final ChatCompletionChoice _self;
  final $Res Function(ChatCompletionChoice) _then;

/// Create a copy of ChatCompletionChoice
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? index = null,Object? message = null,Object? finishReason = freezed,Object? logprobs = freezed,}) {
  return _then(_self.copyWith(
index: null == index ? _self.index : index // ignore: cast_nullable_to_non_nullable
as int,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as ChatMessage,finishReason: freezed == finishReason ? _self.finishReason : finishReason // ignore: cast_nullable_to_non_nullable
as String?,logprobs: freezed == logprobs ? _self.logprobs : logprobs // ignore: cast_nullable_to_non_nullable
as Logprobs?,
  ));
}
/// Create a copy of ChatCompletionChoice
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ChatMessageCopyWith<$Res> get message {
  
  return $ChatMessageCopyWith<$Res>(_self.message, (value) {
    return _then(_self.copyWith(message: value));
  });
}/// Create a copy of ChatCompletionChoice
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LogprobsCopyWith<$Res>? get logprobs {
    if (_self.logprobs == null) {
    return null;
  }

  return $LogprobsCopyWith<$Res>(_self.logprobs!, (value) {
    return _then(_self.copyWith(logprobs: value));
  });
}
}


/// Adds pattern-matching-related methods to [ChatCompletionChoice].
extension ChatCompletionChoicePatterns on ChatCompletionChoice {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ChatCompletionChoice value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ChatCompletionChoice() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ChatCompletionChoice value)  $default,){
final _that = this;
switch (_that) {
case _ChatCompletionChoice():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ChatCompletionChoice value)?  $default,){
final _that = this;
switch (_that) {
case _ChatCompletionChoice() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int index,  ChatMessage message, @JsonKey(name: 'finish_reason')  String? finishReason,  Logprobs? logprobs)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ChatCompletionChoice() when $default != null:
return $default(_that.index,_that.message,_that.finishReason,_that.logprobs);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int index,  ChatMessage message, @JsonKey(name: 'finish_reason')  String? finishReason,  Logprobs? logprobs)  $default,) {final _that = this;
switch (_that) {
case _ChatCompletionChoice():
return $default(_that.index,_that.message,_that.finishReason,_that.logprobs);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int index,  ChatMessage message, @JsonKey(name: 'finish_reason')  String? finishReason,  Logprobs? logprobs)?  $default,) {final _that = this;
switch (_that) {
case _ChatCompletionChoice() when $default != null:
return $default(_that.index,_that.message,_that.finishReason,_that.logprobs);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ChatCompletionChoice implements ChatCompletionChoice {
  const _ChatCompletionChoice({required this.index, required this.message, @JsonKey(name: 'finish_reason') this.finishReason, this.logprobs});
  factory _ChatCompletionChoice.fromJson(Map<String, dynamic> json) => _$ChatCompletionChoiceFromJson(json);

@override final  int index;
@override final  ChatMessage message;
@override@JsonKey(name: 'finish_reason') final  String? finishReason;
@override final  Logprobs? logprobs;

/// Create a copy of ChatCompletionChoice
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChatCompletionChoiceCopyWith<_ChatCompletionChoice> get copyWith => __$ChatCompletionChoiceCopyWithImpl<_ChatCompletionChoice>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ChatCompletionChoiceToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChatCompletionChoice&&(identical(other.index, index) || other.index == index)&&(identical(other.message, message) || other.message == message)&&(identical(other.finishReason, finishReason) || other.finishReason == finishReason)&&(identical(other.logprobs, logprobs) || other.logprobs == logprobs));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,index,message,finishReason,logprobs);

@override
String toString() {
  return 'ChatCompletionChoice(index: $index, message: $message, finishReason: $finishReason, logprobs: $logprobs)';
}


}

/// @nodoc
abstract mixin class _$ChatCompletionChoiceCopyWith<$Res> implements $ChatCompletionChoiceCopyWith<$Res> {
  factory _$ChatCompletionChoiceCopyWith(_ChatCompletionChoice value, $Res Function(_ChatCompletionChoice) _then) = __$ChatCompletionChoiceCopyWithImpl;
@override @useResult
$Res call({
 int index, ChatMessage message,@JsonKey(name: 'finish_reason') String? finishReason, Logprobs? logprobs
});


@override $ChatMessageCopyWith<$Res> get message;@override $LogprobsCopyWith<$Res>? get logprobs;

}
/// @nodoc
class __$ChatCompletionChoiceCopyWithImpl<$Res>
    implements _$ChatCompletionChoiceCopyWith<$Res> {
  __$ChatCompletionChoiceCopyWithImpl(this._self, this._then);

  final _ChatCompletionChoice _self;
  final $Res Function(_ChatCompletionChoice) _then;

/// Create a copy of ChatCompletionChoice
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? index = null,Object? message = null,Object? finishReason = freezed,Object? logprobs = freezed,}) {
  return _then(_ChatCompletionChoice(
index: null == index ? _self.index : index // ignore: cast_nullable_to_non_nullable
as int,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as ChatMessage,finishReason: freezed == finishReason ? _self.finishReason : finishReason // ignore: cast_nullable_to_non_nullable
as String?,logprobs: freezed == logprobs ? _self.logprobs : logprobs // ignore: cast_nullable_to_non_nullable
as Logprobs?,
  ));
}

/// Create a copy of ChatCompletionChoice
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ChatMessageCopyWith<$Res> get message {
  
  return $ChatMessageCopyWith<$Res>(_self.message, (value) {
    return _then(_self.copyWith(message: value));
  });
}/// Create a copy of ChatCompletionChoice
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LogprobsCopyWith<$Res>? get logprobs {
    if (_self.logprobs == null) {
    return null;
  }

  return $LogprobsCopyWith<$Res>(_self.logprobs!, (value) {
    return _then(_self.copyWith(logprobs: value));
  });
}
}


/// @nodoc
mixin _$ChatCompletionChunk {

 String get id; String get object; int get created; String get model; List<ChatCompletionChunkChoice> get choices; Usage? get usage;@JsonKey(name: 'system_fingerprint') String? get systemFingerprint;
/// Create a copy of ChatCompletionChunk
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ChatCompletionChunkCopyWith<ChatCompletionChunk> get copyWith => _$ChatCompletionChunkCopyWithImpl<ChatCompletionChunk>(this as ChatCompletionChunk, _$identity);

  /// Serializes this ChatCompletionChunk to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChatCompletionChunk&&(identical(other.id, id) || other.id == id)&&(identical(other.object, object) || other.object == object)&&(identical(other.created, created) || other.created == created)&&(identical(other.model, model) || other.model == model)&&const DeepCollectionEquality().equals(other.choices, choices)&&(identical(other.usage, usage) || other.usage == usage)&&(identical(other.systemFingerprint, systemFingerprint) || other.systemFingerprint == systemFingerprint));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,object,created,model,const DeepCollectionEquality().hash(choices),usage,systemFingerprint);

@override
String toString() {
  return 'ChatCompletionChunk(id: $id, object: $object, created: $created, model: $model, choices: $choices, usage: $usage, systemFingerprint: $systemFingerprint)';
}


}

/// @nodoc
abstract mixin class $ChatCompletionChunkCopyWith<$Res>  {
  factory $ChatCompletionChunkCopyWith(ChatCompletionChunk value, $Res Function(ChatCompletionChunk) _then) = _$ChatCompletionChunkCopyWithImpl;
@useResult
$Res call({
 String id, String object, int created, String model, List<ChatCompletionChunkChoice> choices, Usage? usage,@JsonKey(name: 'system_fingerprint') String? systemFingerprint
});


$UsageCopyWith<$Res>? get usage;

}
/// @nodoc
class _$ChatCompletionChunkCopyWithImpl<$Res>
    implements $ChatCompletionChunkCopyWith<$Res> {
  _$ChatCompletionChunkCopyWithImpl(this._self, this._then);

  final ChatCompletionChunk _self;
  final $Res Function(ChatCompletionChunk) _then;

/// Create a copy of ChatCompletionChunk
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? object = null,Object? created = null,Object? model = null,Object? choices = null,Object? usage = freezed,Object? systemFingerprint = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,object: null == object ? _self.object : object // ignore: cast_nullable_to_non_nullable
as String,created: null == created ? _self.created : created // ignore: cast_nullable_to_non_nullable
as int,model: null == model ? _self.model : model // ignore: cast_nullable_to_non_nullable
as String,choices: null == choices ? _self.choices : choices // ignore: cast_nullable_to_non_nullable
as List<ChatCompletionChunkChoice>,usage: freezed == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as Usage?,systemFingerprint: freezed == systemFingerprint ? _self.systemFingerprint : systemFingerprint // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}
/// Create a copy of ChatCompletionChunk
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UsageCopyWith<$Res>? get usage {
    if (_self.usage == null) {
    return null;
  }

  return $UsageCopyWith<$Res>(_self.usage!, (value) {
    return _then(_self.copyWith(usage: value));
  });
}
}


/// Adds pattern-matching-related methods to [ChatCompletionChunk].
extension ChatCompletionChunkPatterns on ChatCompletionChunk {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ChatCompletionChunk value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ChatCompletionChunk() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ChatCompletionChunk value)  $default,){
final _that = this;
switch (_that) {
case _ChatCompletionChunk():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ChatCompletionChunk value)?  $default,){
final _that = this;
switch (_that) {
case _ChatCompletionChunk() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String object,  int created,  String model,  List<ChatCompletionChunkChoice> choices,  Usage? usage, @JsonKey(name: 'system_fingerprint')  String? systemFingerprint)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ChatCompletionChunk() when $default != null:
return $default(_that.id,_that.object,_that.created,_that.model,_that.choices,_that.usage,_that.systemFingerprint);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String object,  int created,  String model,  List<ChatCompletionChunkChoice> choices,  Usage? usage, @JsonKey(name: 'system_fingerprint')  String? systemFingerprint)  $default,) {final _that = this;
switch (_that) {
case _ChatCompletionChunk():
return $default(_that.id,_that.object,_that.created,_that.model,_that.choices,_that.usage,_that.systemFingerprint);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String object,  int created,  String model,  List<ChatCompletionChunkChoice> choices,  Usage? usage, @JsonKey(name: 'system_fingerprint')  String? systemFingerprint)?  $default,) {final _that = this;
switch (_that) {
case _ChatCompletionChunk() when $default != null:
return $default(_that.id,_that.object,_that.created,_that.model,_that.choices,_that.usage,_that.systemFingerprint);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ChatCompletionChunk implements ChatCompletionChunk {
  const _ChatCompletionChunk({required this.id, required this.object, required this.created, required this.model, required final  List<ChatCompletionChunkChoice> choices, this.usage, @JsonKey(name: 'system_fingerprint') this.systemFingerprint}): _choices = choices;
  factory _ChatCompletionChunk.fromJson(Map<String, dynamic> json) => _$ChatCompletionChunkFromJson(json);

@override final  String id;
@override final  String object;
@override final  int created;
@override final  String model;
 final  List<ChatCompletionChunkChoice> _choices;
@override List<ChatCompletionChunkChoice> get choices {
  if (_choices is EqualUnmodifiableListView) return _choices;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_choices);
}

@override final  Usage? usage;
@override@JsonKey(name: 'system_fingerprint') final  String? systemFingerprint;

/// Create a copy of ChatCompletionChunk
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChatCompletionChunkCopyWith<_ChatCompletionChunk> get copyWith => __$ChatCompletionChunkCopyWithImpl<_ChatCompletionChunk>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ChatCompletionChunkToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChatCompletionChunk&&(identical(other.id, id) || other.id == id)&&(identical(other.object, object) || other.object == object)&&(identical(other.created, created) || other.created == created)&&(identical(other.model, model) || other.model == model)&&const DeepCollectionEquality().equals(other._choices, _choices)&&(identical(other.usage, usage) || other.usage == usage)&&(identical(other.systemFingerprint, systemFingerprint) || other.systemFingerprint == systemFingerprint));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,object,created,model,const DeepCollectionEquality().hash(_choices),usage,systemFingerprint);

@override
String toString() {
  return 'ChatCompletionChunk(id: $id, object: $object, created: $created, model: $model, choices: $choices, usage: $usage, systemFingerprint: $systemFingerprint)';
}


}

/// @nodoc
abstract mixin class _$ChatCompletionChunkCopyWith<$Res> implements $ChatCompletionChunkCopyWith<$Res> {
  factory _$ChatCompletionChunkCopyWith(_ChatCompletionChunk value, $Res Function(_ChatCompletionChunk) _then) = __$ChatCompletionChunkCopyWithImpl;
@override @useResult
$Res call({
 String id, String object, int created, String model, List<ChatCompletionChunkChoice> choices, Usage? usage,@JsonKey(name: 'system_fingerprint') String? systemFingerprint
});


@override $UsageCopyWith<$Res>? get usage;

}
/// @nodoc
class __$ChatCompletionChunkCopyWithImpl<$Res>
    implements _$ChatCompletionChunkCopyWith<$Res> {
  __$ChatCompletionChunkCopyWithImpl(this._self, this._then);

  final _ChatCompletionChunk _self;
  final $Res Function(_ChatCompletionChunk) _then;

/// Create a copy of ChatCompletionChunk
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? object = null,Object? created = null,Object? model = null,Object? choices = null,Object? usage = freezed,Object? systemFingerprint = freezed,}) {
  return _then(_ChatCompletionChunk(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,object: null == object ? _self.object : object // ignore: cast_nullable_to_non_nullable
as String,created: null == created ? _self.created : created // ignore: cast_nullable_to_non_nullable
as int,model: null == model ? _self.model : model // ignore: cast_nullable_to_non_nullable
as String,choices: null == choices ? _self._choices : choices // ignore: cast_nullable_to_non_nullable
as List<ChatCompletionChunkChoice>,usage: freezed == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as Usage?,systemFingerprint: freezed == systemFingerprint ? _self.systemFingerprint : systemFingerprint // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of ChatCompletionChunk
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UsageCopyWith<$Res>? get usage {
    if (_self.usage == null) {
    return null;
  }

  return $UsageCopyWith<$Res>(_self.usage!, (value) {
    return _then(_self.copyWith(usage: value));
  });
}
}


/// @nodoc
mixin _$ChatCompletionChunkChoice {

 int get index; ChatMessageDelta get delta;@JsonKey(name: 'finish_reason') String? get finishReason; Logprobs? get logprobs;
/// Create a copy of ChatCompletionChunkChoice
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ChatCompletionChunkChoiceCopyWith<ChatCompletionChunkChoice> get copyWith => _$ChatCompletionChunkChoiceCopyWithImpl<ChatCompletionChunkChoice>(this as ChatCompletionChunkChoice, _$identity);

  /// Serializes this ChatCompletionChunkChoice to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChatCompletionChunkChoice&&(identical(other.index, index) || other.index == index)&&(identical(other.delta, delta) || other.delta == delta)&&(identical(other.finishReason, finishReason) || other.finishReason == finishReason)&&(identical(other.logprobs, logprobs) || other.logprobs == logprobs));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,index,delta,finishReason,logprobs);

@override
String toString() {
  return 'ChatCompletionChunkChoice(index: $index, delta: $delta, finishReason: $finishReason, logprobs: $logprobs)';
}


}

/// @nodoc
abstract mixin class $ChatCompletionChunkChoiceCopyWith<$Res>  {
  factory $ChatCompletionChunkChoiceCopyWith(ChatCompletionChunkChoice value, $Res Function(ChatCompletionChunkChoice) _then) = _$ChatCompletionChunkChoiceCopyWithImpl;
@useResult
$Res call({
 int index, ChatMessageDelta delta,@JsonKey(name: 'finish_reason') String? finishReason, Logprobs? logprobs
});


$ChatMessageDeltaCopyWith<$Res> get delta;$LogprobsCopyWith<$Res>? get logprobs;

}
/// @nodoc
class _$ChatCompletionChunkChoiceCopyWithImpl<$Res>
    implements $ChatCompletionChunkChoiceCopyWith<$Res> {
  _$ChatCompletionChunkChoiceCopyWithImpl(this._self, this._then);

  final ChatCompletionChunkChoice _self;
  final $Res Function(ChatCompletionChunkChoice) _then;

/// Create a copy of ChatCompletionChunkChoice
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? index = null,Object? delta = null,Object? finishReason = freezed,Object? logprobs = freezed,}) {
  return _then(_self.copyWith(
index: null == index ? _self.index : index // ignore: cast_nullable_to_non_nullable
as int,delta: null == delta ? _self.delta : delta // ignore: cast_nullable_to_non_nullable
as ChatMessageDelta,finishReason: freezed == finishReason ? _self.finishReason : finishReason // ignore: cast_nullable_to_non_nullable
as String?,logprobs: freezed == logprobs ? _self.logprobs : logprobs // ignore: cast_nullable_to_non_nullable
as Logprobs?,
  ));
}
/// Create a copy of ChatCompletionChunkChoice
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ChatMessageDeltaCopyWith<$Res> get delta {
  
  return $ChatMessageDeltaCopyWith<$Res>(_self.delta, (value) {
    return _then(_self.copyWith(delta: value));
  });
}/// Create a copy of ChatCompletionChunkChoice
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LogprobsCopyWith<$Res>? get logprobs {
    if (_self.logprobs == null) {
    return null;
  }

  return $LogprobsCopyWith<$Res>(_self.logprobs!, (value) {
    return _then(_self.copyWith(logprobs: value));
  });
}
}


/// Adds pattern-matching-related methods to [ChatCompletionChunkChoice].
extension ChatCompletionChunkChoicePatterns on ChatCompletionChunkChoice {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ChatCompletionChunkChoice value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ChatCompletionChunkChoice() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ChatCompletionChunkChoice value)  $default,){
final _that = this;
switch (_that) {
case _ChatCompletionChunkChoice():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ChatCompletionChunkChoice value)?  $default,){
final _that = this;
switch (_that) {
case _ChatCompletionChunkChoice() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int index,  ChatMessageDelta delta, @JsonKey(name: 'finish_reason')  String? finishReason,  Logprobs? logprobs)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ChatCompletionChunkChoice() when $default != null:
return $default(_that.index,_that.delta,_that.finishReason,_that.logprobs);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int index,  ChatMessageDelta delta, @JsonKey(name: 'finish_reason')  String? finishReason,  Logprobs? logprobs)  $default,) {final _that = this;
switch (_that) {
case _ChatCompletionChunkChoice():
return $default(_that.index,_that.delta,_that.finishReason,_that.logprobs);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int index,  ChatMessageDelta delta, @JsonKey(name: 'finish_reason')  String? finishReason,  Logprobs? logprobs)?  $default,) {final _that = this;
switch (_that) {
case _ChatCompletionChunkChoice() when $default != null:
return $default(_that.index,_that.delta,_that.finishReason,_that.logprobs);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ChatCompletionChunkChoice implements ChatCompletionChunkChoice {
  const _ChatCompletionChunkChoice({required this.index, required this.delta, @JsonKey(name: 'finish_reason') this.finishReason, this.logprobs});
  factory _ChatCompletionChunkChoice.fromJson(Map<String, dynamic> json) => _$ChatCompletionChunkChoiceFromJson(json);

@override final  int index;
@override final  ChatMessageDelta delta;
@override@JsonKey(name: 'finish_reason') final  String? finishReason;
@override final  Logprobs? logprobs;

/// Create a copy of ChatCompletionChunkChoice
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChatCompletionChunkChoiceCopyWith<_ChatCompletionChunkChoice> get copyWith => __$ChatCompletionChunkChoiceCopyWithImpl<_ChatCompletionChunkChoice>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ChatCompletionChunkChoiceToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChatCompletionChunkChoice&&(identical(other.index, index) || other.index == index)&&(identical(other.delta, delta) || other.delta == delta)&&(identical(other.finishReason, finishReason) || other.finishReason == finishReason)&&(identical(other.logprobs, logprobs) || other.logprobs == logprobs));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,index,delta,finishReason,logprobs);

@override
String toString() {
  return 'ChatCompletionChunkChoice(index: $index, delta: $delta, finishReason: $finishReason, logprobs: $logprobs)';
}


}

/// @nodoc
abstract mixin class _$ChatCompletionChunkChoiceCopyWith<$Res> implements $ChatCompletionChunkChoiceCopyWith<$Res> {
  factory _$ChatCompletionChunkChoiceCopyWith(_ChatCompletionChunkChoice value, $Res Function(_ChatCompletionChunkChoice) _then) = __$ChatCompletionChunkChoiceCopyWithImpl;
@override @useResult
$Res call({
 int index, ChatMessageDelta delta,@JsonKey(name: 'finish_reason') String? finishReason, Logprobs? logprobs
});


@override $ChatMessageDeltaCopyWith<$Res> get delta;@override $LogprobsCopyWith<$Res>? get logprobs;

}
/// @nodoc
class __$ChatCompletionChunkChoiceCopyWithImpl<$Res>
    implements _$ChatCompletionChunkChoiceCopyWith<$Res> {
  __$ChatCompletionChunkChoiceCopyWithImpl(this._self, this._then);

  final _ChatCompletionChunkChoice _self;
  final $Res Function(_ChatCompletionChunkChoice) _then;

/// Create a copy of ChatCompletionChunkChoice
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? index = null,Object? delta = null,Object? finishReason = freezed,Object? logprobs = freezed,}) {
  return _then(_ChatCompletionChunkChoice(
index: null == index ? _self.index : index // ignore: cast_nullable_to_non_nullable
as int,delta: null == delta ? _self.delta : delta // ignore: cast_nullable_to_non_nullable
as ChatMessageDelta,finishReason: freezed == finishReason ? _self.finishReason : finishReason // ignore: cast_nullable_to_non_nullable
as String?,logprobs: freezed == logprobs ? _self.logprobs : logprobs // ignore: cast_nullable_to_non_nullable
as Logprobs?,
  ));
}

/// Create a copy of ChatCompletionChunkChoice
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ChatMessageDeltaCopyWith<$Res> get delta {
  
  return $ChatMessageDeltaCopyWith<$Res>(_self.delta, (value) {
    return _then(_self.copyWith(delta: value));
  });
}/// Create a copy of ChatCompletionChunkChoice
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LogprobsCopyWith<$Res>? get logprobs {
    if (_self.logprobs == null) {
    return null;
  }

  return $LogprobsCopyWith<$Res>(_self.logprobs!, (value) {
    return _then(_self.copyWith(logprobs: value));
  });
}
}


/// @nodoc
mixin _$ChatMessageDelta {

 String? get role; String? get content;@JsonKey(name: 'tool_calls') List<ToolCallDelta>? get toolCalls;
/// Create a copy of ChatMessageDelta
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ChatMessageDeltaCopyWith<ChatMessageDelta> get copyWith => _$ChatMessageDeltaCopyWithImpl<ChatMessageDelta>(this as ChatMessageDelta, _$identity);

  /// Serializes this ChatMessageDelta to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChatMessageDelta&&(identical(other.role, role) || other.role == role)&&(identical(other.content, content) || other.content == content)&&const DeepCollectionEquality().equals(other.toolCalls, toolCalls));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,role,content,const DeepCollectionEquality().hash(toolCalls));

@override
String toString() {
  return 'ChatMessageDelta(role: $role, content: $content, toolCalls: $toolCalls)';
}


}

/// @nodoc
abstract mixin class $ChatMessageDeltaCopyWith<$Res>  {
  factory $ChatMessageDeltaCopyWith(ChatMessageDelta value, $Res Function(ChatMessageDelta) _then) = _$ChatMessageDeltaCopyWithImpl;
@useResult
$Res call({
 String? role, String? content,@JsonKey(name: 'tool_calls') List<ToolCallDelta>? toolCalls
});




}
/// @nodoc
class _$ChatMessageDeltaCopyWithImpl<$Res>
    implements $ChatMessageDeltaCopyWith<$Res> {
  _$ChatMessageDeltaCopyWithImpl(this._self, this._then);

  final ChatMessageDelta _self;
  final $Res Function(ChatMessageDelta) _then;

/// Create a copy of ChatMessageDelta
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? role = freezed,Object? content = freezed,Object? toolCalls = freezed,}) {
  return _then(_self.copyWith(
role: freezed == role ? _self.role : role // ignore: cast_nullable_to_non_nullable
as String?,content: freezed == content ? _self.content : content // ignore: cast_nullable_to_non_nullable
as String?,toolCalls: freezed == toolCalls ? _self.toolCalls : toolCalls // ignore: cast_nullable_to_non_nullable
as List<ToolCallDelta>?,
  ));
}

}


/// Adds pattern-matching-related methods to [ChatMessageDelta].
extension ChatMessageDeltaPatterns on ChatMessageDelta {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ChatMessageDelta value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ChatMessageDelta() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ChatMessageDelta value)  $default,){
final _that = this;
switch (_that) {
case _ChatMessageDelta():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ChatMessageDelta value)?  $default,){
final _that = this;
switch (_that) {
case _ChatMessageDelta() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String? role,  String? content, @JsonKey(name: 'tool_calls')  List<ToolCallDelta>? toolCalls)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ChatMessageDelta() when $default != null:
return $default(_that.role,_that.content,_that.toolCalls);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String? role,  String? content, @JsonKey(name: 'tool_calls')  List<ToolCallDelta>? toolCalls)  $default,) {final _that = this;
switch (_that) {
case _ChatMessageDelta():
return $default(_that.role,_that.content,_that.toolCalls);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String? role,  String? content, @JsonKey(name: 'tool_calls')  List<ToolCallDelta>? toolCalls)?  $default,) {final _that = this;
switch (_that) {
case _ChatMessageDelta() when $default != null:
return $default(_that.role,_that.content,_that.toolCalls);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ChatMessageDelta implements ChatMessageDelta {
  const _ChatMessageDelta({this.role, this.content, @JsonKey(name: 'tool_calls') final  List<ToolCallDelta>? toolCalls}): _toolCalls = toolCalls;
  factory _ChatMessageDelta.fromJson(Map<String, dynamic> json) => _$ChatMessageDeltaFromJson(json);

@override final  String? role;
@override final  String? content;
 final  List<ToolCallDelta>? _toolCalls;
@override@JsonKey(name: 'tool_calls') List<ToolCallDelta>? get toolCalls {
  final value = _toolCalls;
  if (value == null) return null;
  if (_toolCalls is EqualUnmodifiableListView) return _toolCalls;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}


/// Create a copy of ChatMessageDelta
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChatMessageDeltaCopyWith<_ChatMessageDelta> get copyWith => __$ChatMessageDeltaCopyWithImpl<_ChatMessageDelta>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ChatMessageDeltaToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChatMessageDelta&&(identical(other.role, role) || other.role == role)&&(identical(other.content, content) || other.content == content)&&const DeepCollectionEquality().equals(other._toolCalls, _toolCalls));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,role,content,const DeepCollectionEquality().hash(_toolCalls));

@override
String toString() {
  return 'ChatMessageDelta(role: $role, content: $content, toolCalls: $toolCalls)';
}


}

/// @nodoc
abstract mixin class _$ChatMessageDeltaCopyWith<$Res> implements $ChatMessageDeltaCopyWith<$Res> {
  factory _$ChatMessageDeltaCopyWith(_ChatMessageDelta value, $Res Function(_ChatMessageDelta) _then) = __$ChatMessageDeltaCopyWithImpl;
@override @useResult
$Res call({
 String? role, String? content,@JsonKey(name: 'tool_calls') List<ToolCallDelta>? toolCalls
});




}
/// @nodoc
class __$ChatMessageDeltaCopyWithImpl<$Res>
    implements _$ChatMessageDeltaCopyWith<$Res> {
  __$ChatMessageDeltaCopyWithImpl(this._self, this._then);

  final _ChatMessageDelta _self;
  final $Res Function(_ChatMessageDelta) _then;

/// Create a copy of ChatMessageDelta
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? role = freezed,Object? content = freezed,Object? toolCalls = freezed,}) {
  return _then(_ChatMessageDelta(
role: freezed == role ? _self.role : role // ignore: cast_nullable_to_non_nullable
as String?,content: freezed == content ? _self.content : content // ignore: cast_nullable_to_non_nullable
as String?,toolCalls: freezed == toolCalls ? _self._toolCalls : toolCalls // ignore: cast_nullable_to_non_nullable
as List<ToolCallDelta>?,
  ));
}


}


/// @nodoc
mixin _$ToolCallDelta {

 int? get index; String? get id; String? get type; ToolFunctionDelta? get function;
/// Create a copy of ToolCallDelta
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ToolCallDeltaCopyWith<ToolCallDelta> get copyWith => _$ToolCallDeltaCopyWithImpl<ToolCallDelta>(this as ToolCallDelta, _$identity);

  /// Serializes this ToolCallDelta to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ToolCallDelta&&(identical(other.index, index) || other.index == index)&&(identical(other.id, id) || other.id == id)&&(identical(other.type, type) || other.type == type)&&(identical(other.function, function) || other.function == function));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,index,id,type,function);

@override
String toString() {
  return 'ToolCallDelta(index: $index, id: $id, type: $type, function: $function)';
}


}

/// @nodoc
abstract mixin class $ToolCallDeltaCopyWith<$Res>  {
  factory $ToolCallDeltaCopyWith(ToolCallDelta value, $Res Function(ToolCallDelta) _then) = _$ToolCallDeltaCopyWithImpl;
@useResult
$Res call({
 int? index, String? id, String? type, ToolFunctionDelta? function
});


$ToolFunctionDeltaCopyWith<$Res>? get function;

}
/// @nodoc
class _$ToolCallDeltaCopyWithImpl<$Res>
    implements $ToolCallDeltaCopyWith<$Res> {
  _$ToolCallDeltaCopyWithImpl(this._self, this._then);

  final ToolCallDelta _self;
  final $Res Function(ToolCallDelta) _then;

/// Create a copy of ToolCallDelta
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? index = freezed,Object? id = freezed,Object? type = freezed,Object? function = freezed,}) {
  return _then(_self.copyWith(
index: freezed == index ? _self.index : index // ignore: cast_nullable_to_non_nullable
as int?,id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String?,type: freezed == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String?,function: freezed == function ? _self.function : function // ignore: cast_nullable_to_non_nullable
as ToolFunctionDelta?,
  ));
}
/// Create a copy of ToolCallDelta
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ToolFunctionDeltaCopyWith<$Res>? get function {
    if (_self.function == null) {
    return null;
  }

  return $ToolFunctionDeltaCopyWith<$Res>(_self.function!, (value) {
    return _then(_self.copyWith(function: value));
  });
}
}


/// Adds pattern-matching-related methods to [ToolCallDelta].
extension ToolCallDeltaPatterns on ToolCallDelta {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ToolCallDelta value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ToolCallDelta() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ToolCallDelta value)  $default,){
final _that = this;
switch (_that) {
case _ToolCallDelta():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ToolCallDelta value)?  $default,){
final _that = this;
switch (_that) {
case _ToolCallDelta() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int? index,  String? id,  String? type,  ToolFunctionDelta? function)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ToolCallDelta() when $default != null:
return $default(_that.index,_that.id,_that.type,_that.function);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int? index,  String? id,  String? type,  ToolFunctionDelta? function)  $default,) {final _that = this;
switch (_that) {
case _ToolCallDelta():
return $default(_that.index,_that.id,_that.type,_that.function);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int? index,  String? id,  String? type,  ToolFunctionDelta? function)?  $default,) {final _that = this;
switch (_that) {
case _ToolCallDelta() when $default != null:
return $default(_that.index,_that.id,_that.type,_that.function);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ToolCallDelta implements ToolCallDelta {
  const _ToolCallDelta({this.index, this.id, this.type, this.function});
  factory _ToolCallDelta.fromJson(Map<String, dynamic> json) => _$ToolCallDeltaFromJson(json);

@override final  int? index;
@override final  String? id;
@override final  String? type;
@override final  ToolFunctionDelta? function;

/// Create a copy of ToolCallDelta
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ToolCallDeltaCopyWith<_ToolCallDelta> get copyWith => __$ToolCallDeltaCopyWithImpl<_ToolCallDelta>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ToolCallDeltaToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ToolCallDelta&&(identical(other.index, index) || other.index == index)&&(identical(other.id, id) || other.id == id)&&(identical(other.type, type) || other.type == type)&&(identical(other.function, function) || other.function == function));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,index,id,type,function);

@override
String toString() {
  return 'ToolCallDelta(index: $index, id: $id, type: $type, function: $function)';
}


}

/// @nodoc
abstract mixin class _$ToolCallDeltaCopyWith<$Res> implements $ToolCallDeltaCopyWith<$Res> {
  factory _$ToolCallDeltaCopyWith(_ToolCallDelta value, $Res Function(_ToolCallDelta) _then) = __$ToolCallDeltaCopyWithImpl;
@override @useResult
$Res call({
 int? index, String? id, String? type, ToolFunctionDelta? function
});


@override $ToolFunctionDeltaCopyWith<$Res>? get function;

}
/// @nodoc
class __$ToolCallDeltaCopyWithImpl<$Res>
    implements _$ToolCallDeltaCopyWith<$Res> {
  __$ToolCallDeltaCopyWithImpl(this._self, this._then);

  final _ToolCallDelta _self;
  final $Res Function(_ToolCallDelta) _then;

/// Create a copy of ToolCallDelta
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? index = freezed,Object? id = freezed,Object? type = freezed,Object? function = freezed,}) {
  return _then(_ToolCallDelta(
index: freezed == index ? _self.index : index // ignore: cast_nullable_to_non_nullable
as int?,id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String?,type: freezed == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String?,function: freezed == function ? _self.function : function // ignore: cast_nullable_to_non_nullable
as ToolFunctionDelta?,
  ));
}

/// Create a copy of ToolCallDelta
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ToolFunctionDeltaCopyWith<$Res>? get function {
    if (_self.function == null) {
    return null;
  }

  return $ToolFunctionDeltaCopyWith<$Res>(_self.function!, (value) {
    return _then(_self.copyWith(function: value));
  });
}
}


/// @nodoc
mixin _$ToolFunctionDelta {

 String? get name; String? get arguments;
/// Create a copy of ToolFunctionDelta
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ToolFunctionDeltaCopyWith<ToolFunctionDelta> get copyWith => _$ToolFunctionDeltaCopyWithImpl<ToolFunctionDelta>(this as ToolFunctionDelta, _$identity);

  /// Serializes this ToolFunctionDelta to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ToolFunctionDelta&&(identical(other.name, name) || other.name == name)&&(identical(other.arguments, arguments) || other.arguments == arguments));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,arguments);

@override
String toString() {
  return 'ToolFunctionDelta(name: $name, arguments: $arguments)';
}


}

/// @nodoc
abstract mixin class $ToolFunctionDeltaCopyWith<$Res>  {
  factory $ToolFunctionDeltaCopyWith(ToolFunctionDelta value, $Res Function(ToolFunctionDelta) _then) = _$ToolFunctionDeltaCopyWithImpl;
@useResult
$Res call({
 String? name, String? arguments
});




}
/// @nodoc
class _$ToolFunctionDeltaCopyWithImpl<$Res>
    implements $ToolFunctionDeltaCopyWith<$Res> {
  _$ToolFunctionDeltaCopyWithImpl(this._self, this._then);

  final ToolFunctionDelta _self;
  final $Res Function(ToolFunctionDelta) _then;

/// Create a copy of ToolFunctionDelta
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = freezed,Object? arguments = freezed,}) {
  return _then(_self.copyWith(
name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,arguments: freezed == arguments ? _self.arguments : arguments // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [ToolFunctionDelta].
extension ToolFunctionDeltaPatterns on ToolFunctionDelta {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ToolFunctionDelta value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ToolFunctionDelta() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ToolFunctionDelta value)  $default,){
final _that = this;
switch (_that) {
case _ToolFunctionDelta():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ToolFunctionDelta value)?  $default,){
final _that = this;
switch (_that) {
case _ToolFunctionDelta() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String? name,  String? arguments)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ToolFunctionDelta() when $default != null:
return $default(_that.name,_that.arguments);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String? name,  String? arguments)  $default,) {final _that = this;
switch (_that) {
case _ToolFunctionDelta():
return $default(_that.name,_that.arguments);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String? name,  String? arguments)?  $default,) {final _that = this;
switch (_that) {
case _ToolFunctionDelta() when $default != null:
return $default(_that.name,_that.arguments);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ToolFunctionDelta implements ToolFunctionDelta {
  const _ToolFunctionDelta({this.name, this.arguments});
  factory _ToolFunctionDelta.fromJson(Map<String, dynamic> json) => _$ToolFunctionDeltaFromJson(json);

@override final  String? name;
@override final  String? arguments;

/// Create a copy of ToolFunctionDelta
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ToolFunctionDeltaCopyWith<_ToolFunctionDelta> get copyWith => __$ToolFunctionDeltaCopyWithImpl<_ToolFunctionDelta>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ToolFunctionDeltaToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ToolFunctionDelta&&(identical(other.name, name) || other.name == name)&&(identical(other.arguments, arguments) || other.arguments == arguments));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,arguments);

@override
String toString() {
  return 'ToolFunctionDelta(name: $name, arguments: $arguments)';
}


}

/// @nodoc
abstract mixin class _$ToolFunctionDeltaCopyWith<$Res> implements $ToolFunctionDeltaCopyWith<$Res> {
  factory _$ToolFunctionDeltaCopyWith(_ToolFunctionDelta value, $Res Function(_ToolFunctionDelta) _then) = __$ToolFunctionDeltaCopyWithImpl;
@override @useResult
$Res call({
 String? name, String? arguments
});




}
/// @nodoc
class __$ToolFunctionDeltaCopyWithImpl<$Res>
    implements _$ToolFunctionDeltaCopyWith<$Res> {
  __$ToolFunctionDeltaCopyWithImpl(this._self, this._then);

  final _ToolFunctionDelta _self;
  final $Res Function(_ToolFunctionDelta) _then;

/// Create a copy of ToolFunctionDelta
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = freezed,Object? arguments = freezed,}) {
  return _then(_ToolFunctionDelta(
name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,arguments: freezed == arguments ? _self.arguments : arguments // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$Usage {

@JsonKey(name: 'prompt_tokens') int get promptTokens;@JsonKey(name: 'completion_tokens') int? get completionTokens;@JsonKey(name: 'total_tokens') int get totalTokens;
/// Create a copy of Usage
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UsageCopyWith<Usage> get copyWith => _$UsageCopyWithImpl<Usage>(this as Usage, _$identity);

  /// Serializes this Usage to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Usage&&(identical(other.promptTokens, promptTokens) || other.promptTokens == promptTokens)&&(identical(other.completionTokens, completionTokens) || other.completionTokens == completionTokens)&&(identical(other.totalTokens, totalTokens) || other.totalTokens == totalTokens));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,promptTokens,completionTokens,totalTokens);

@override
String toString() {
  return 'Usage(promptTokens: $promptTokens, completionTokens: $completionTokens, totalTokens: $totalTokens)';
}


}

/// @nodoc
abstract mixin class $UsageCopyWith<$Res>  {
  factory $UsageCopyWith(Usage value, $Res Function(Usage) _then) = _$UsageCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'prompt_tokens') int promptTokens,@JsonKey(name: 'completion_tokens') int? completionTokens,@JsonKey(name: 'total_tokens') int totalTokens
});




}
/// @nodoc
class _$UsageCopyWithImpl<$Res>
    implements $UsageCopyWith<$Res> {
  _$UsageCopyWithImpl(this._self, this._then);

  final Usage _self;
  final $Res Function(Usage) _then;

/// Create a copy of Usage
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? promptTokens = null,Object? completionTokens = freezed,Object? totalTokens = null,}) {
  return _then(_self.copyWith(
promptTokens: null == promptTokens ? _self.promptTokens : promptTokens // ignore: cast_nullable_to_non_nullable
as int,completionTokens: freezed == completionTokens ? _self.completionTokens : completionTokens // ignore: cast_nullable_to_non_nullable
as int?,totalTokens: null == totalTokens ? _self.totalTokens : totalTokens // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [Usage].
extension UsagePatterns on Usage {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Usage value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Usage() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Usage value)  $default,){
final _that = this;
switch (_that) {
case _Usage():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Usage value)?  $default,){
final _that = this;
switch (_that) {
case _Usage() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: 'prompt_tokens')  int promptTokens, @JsonKey(name: 'completion_tokens')  int? completionTokens, @JsonKey(name: 'total_tokens')  int totalTokens)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Usage() when $default != null:
return $default(_that.promptTokens,_that.completionTokens,_that.totalTokens);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: 'prompt_tokens')  int promptTokens, @JsonKey(name: 'completion_tokens')  int? completionTokens, @JsonKey(name: 'total_tokens')  int totalTokens)  $default,) {final _that = this;
switch (_that) {
case _Usage():
return $default(_that.promptTokens,_that.completionTokens,_that.totalTokens);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: 'prompt_tokens')  int promptTokens, @JsonKey(name: 'completion_tokens')  int? completionTokens, @JsonKey(name: 'total_tokens')  int totalTokens)?  $default,) {final _that = this;
switch (_that) {
case _Usage() when $default != null:
return $default(_that.promptTokens,_that.completionTokens,_that.totalTokens);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Usage implements Usage {
  const _Usage({@JsonKey(name: 'prompt_tokens') required this.promptTokens, @JsonKey(name: 'completion_tokens') this.completionTokens, @JsonKey(name: 'total_tokens') required this.totalTokens});
  factory _Usage.fromJson(Map<String, dynamic> json) => _$UsageFromJson(json);

@override@JsonKey(name: 'prompt_tokens') final  int promptTokens;
@override@JsonKey(name: 'completion_tokens') final  int? completionTokens;
@override@JsonKey(name: 'total_tokens') final  int totalTokens;

/// Create a copy of Usage
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UsageCopyWith<_Usage> get copyWith => __$UsageCopyWithImpl<_Usage>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UsageToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Usage&&(identical(other.promptTokens, promptTokens) || other.promptTokens == promptTokens)&&(identical(other.completionTokens, completionTokens) || other.completionTokens == completionTokens)&&(identical(other.totalTokens, totalTokens) || other.totalTokens == totalTokens));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,promptTokens,completionTokens,totalTokens);

@override
String toString() {
  return 'Usage(promptTokens: $promptTokens, completionTokens: $completionTokens, totalTokens: $totalTokens)';
}


}

/// @nodoc
abstract mixin class _$UsageCopyWith<$Res> implements $UsageCopyWith<$Res> {
  factory _$UsageCopyWith(_Usage value, $Res Function(_Usage) _then) = __$UsageCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'prompt_tokens') int promptTokens,@JsonKey(name: 'completion_tokens') int? completionTokens,@JsonKey(name: 'total_tokens') int totalTokens
});




}
/// @nodoc
class __$UsageCopyWithImpl<$Res>
    implements _$UsageCopyWith<$Res> {
  __$UsageCopyWithImpl(this._self, this._then);

  final _Usage _self;
  final $Res Function(_Usage) _then;

/// Create a copy of Usage
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? promptTokens = null,Object? completionTokens = freezed,Object? totalTokens = null,}) {
  return _then(_Usage(
promptTokens: null == promptTokens ? _self.promptTokens : promptTokens // ignore: cast_nullable_to_non_nullable
as int,completionTokens: freezed == completionTokens ? _self.completionTokens : completionTokens // ignore: cast_nullable_to_non_nullable
as int?,totalTokens: null == totalTokens ? _self.totalTokens : totalTokens // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}


/// @nodoc
mixin _$Logprobs {

 List<Map<String, dynamic>> get content;
/// Create a copy of Logprobs
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LogprobsCopyWith<Logprobs> get copyWith => _$LogprobsCopyWithImpl<Logprobs>(this as Logprobs, _$identity);

  /// Serializes this Logprobs to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Logprobs&&const DeepCollectionEquality().equals(other.content, content));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(content));

@override
String toString() {
  return 'Logprobs(content: $content)';
}


}

/// @nodoc
abstract mixin class $LogprobsCopyWith<$Res>  {
  factory $LogprobsCopyWith(Logprobs value, $Res Function(Logprobs) _then) = _$LogprobsCopyWithImpl;
@useResult
$Res call({
 List<Map<String, dynamic>> content
});




}
/// @nodoc
class _$LogprobsCopyWithImpl<$Res>
    implements $LogprobsCopyWith<$Res> {
  _$LogprobsCopyWithImpl(this._self, this._then);

  final Logprobs _self;
  final $Res Function(Logprobs) _then;

/// Create a copy of Logprobs
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? content = null,}) {
  return _then(_self.copyWith(
content: null == content ? _self.content : content // ignore: cast_nullable_to_non_nullable
as List<Map<String, dynamic>>,
  ));
}

}


/// Adds pattern-matching-related methods to [Logprobs].
extension LogprobsPatterns on Logprobs {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Logprobs value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Logprobs() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Logprobs value)  $default,){
final _that = this;
switch (_that) {
case _Logprobs():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Logprobs value)?  $default,){
final _that = this;
switch (_that) {
case _Logprobs() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<Map<String, dynamic>> content)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Logprobs() when $default != null:
return $default(_that.content);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<Map<String, dynamic>> content)  $default,) {final _that = this;
switch (_that) {
case _Logprobs():
return $default(_that.content);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<Map<String, dynamic>> content)?  $default,) {final _that = this;
switch (_that) {
case _Logprobs() when $default != null:
return $default(_that.content);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Logprobs implements Logprobs {
  const _Logprobs({required final  List<Map<String, dynamic>> content}): _content = content;
  factory _Logprobs.fromJson(Map<String, dynamic> json) => _$LogprobsFromJson(json);

 final  List<Map<String, dynamic>> _content;
@override List<Map<String, dynamic>> get content {
  if (_content is EqualUnmodifiableListView) return _content;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_content);
}


/// Create a copy of Logprobs
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LogprobsCopyWith<_Logprobs> get copyWith => __$LogprobsCopyWithImpl<_Logprobs>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$LogprobsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Logprobs&&const DeepCollectionEquality().equals(other._content, _content));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_content));

@override
String toString() {
  return 'Logprobs(content: $content)';
}


}

/// @nodoc
abstract mixin class _$LogprobsCopyWith<$Res> implements $LogprobsCopyWith<$Res> {
  factory _$LogprobsCopyWith(_Logprobs value, $Res Function(_Logprobs) _then) = __$LogprobsCopyWithImpl;
@override @useResult
$Res call({
 List<Map<String, dynamic>> content
});




}
/// @nodoc
class __$LogprobsCopyWithImpl<$Res>
    implements _$LogprobsCopyWith<$Res> {
  __$LogprobsCopyWithImpl(this._self, this._then);

  final _Logprobs _self;
  final $Res Function(_Logprobs) _then;

/// Create a copy of Logprobs
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? content = null,}) {
  return _then(_Logprobs(
content: null == content ? _self._content : content // ignore: cast_nullable_to_non_nullable
as List<Map<String, dynamic>>,
  ));
}


}


/// @nodoc
mixin _$ModelInfo {

 String get id; String get name; String? get description; int get context; Pricing get pricing;@JsonKey(name: 'top_provider') Provider get topProvider;@JsonKey(name: 'per_request_limits') PerRequestLimits? get perRequestLimits;
/// Create a copy of ModelInfo
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ModelInfoCopyWith<ModelInfo> get copyWith => _$ModelInfoCopyWithImpl<ModelInfo>(this as ModelInfo, _$identity);

  /// Serializes this ModelInfo to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ModelInfo&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&(identical(other.context, context) || other.context == context)&&(identical(other.pricing, pricing) || other.pricing == pricing)&&(identical(other.topProvider, topProvider) || other.topProvider == topProvider)&&(identical(other.perRequestLimits, perRequestLimits) || other.perRequestLimits == perRequestLimits));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,description,context,pricing,topProvider,perRequestLimits);

@override
String toString() {
  return 'ModelInfo(id: $id, name: $name, description: $description, context: $context, pricing: $pricing, topProvider: $topProvider, perRequestLimits: $perRequestLimits)';
}


}

/// @nodoc
abstract mixin class $ModelInfoCopyWith<$Res>  {
  factory $ModelInfoCopyWith(ModelInfo value, $Res Function(ModelInfo) _then) = _$ModelInfoCopyWithImpl;
@useResult
$Res call({
 String id, String name, String? description, int context, Pricing pricing,@JsonKey(name: 'top_provider') Provider topProvider,@JsonKey(name: 'per_request_limits') PerRequestLimits? perRequestLimits
});


$PricingCopyWith<$Res> get pricing;$ProviderCopyWith<$Res> get topProvider;$PerRequestLimitsCopyWith<$Res>? get perRequestLimits;

}
/// @nodoc
class _$ModelInfoCopyWithImpl<$Res>
    implements $ModelInfoCopyWith<$Res> {
  _$ModelInfoCopyWithImpl(this._self, this._then);

  final ModelInfo _self;
  final $Res Function(ModelInfo) _then;

/// Create a copy of ModelInfo
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? description = freezed,Object? context = null,Object? pricing = null,Object? topProvider = null,Object? perRequestLimits = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,context: null == context ? _self.context : context // ignore: cast_nullable_to_non_nullable
as int,pricing: null == pricing ? _self.pricing : pricing // ignore: cast_nullable_to_non_nullable
as Pricing,topProvider: null == topProvider ? _self.topProvider : topProvider // ignore: cast_nullable_to_non_nullable
as Provider,perRequestLimits: freezed == perRequestLimits ? _self.perRequestLimits : perRequestLimits // ignore: cast_nullable_to_non_nullable
as PerRequestLimits?,
  ));
}
/// Create a copy of ModelInfo
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PricingCopyWith<$Res> get pricing {
  
  return $PricingCopyWith<$Res>(_self.pricing, (value) {
    return _then(_self.copyWith(pricing: value));
  });
}/// Create a copy of ModelInfo
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProviderCopyWith<$Res> get topProvider {
  
  return $ProviderCopyWith<$Res>(_self.topProvider, (value) {
    return _then(_self.copyWith(topProvider: value));
  });
}/// Create a copy of ModelInfo
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PerRequestLimitsCopyWith<$Res>? get perRequestLimits {
    if (_self.perRequestLimits == null) {
    return null;
  }

  return $PerRequestLimitsCopyWith<$Res>(_self.perRequestLimits!, (value) {
    return _then(_self.copyWith(perRequestLimits: value));
  });
}
}


/// Adds pattern-matching-related methods to [ModelInfo].
extension ModelInfoPatterns on ModelInfo {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ModelInfo value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ModelInfo() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ModelInfo value)  $default,){
final _that = this;
switch (_that) {
case _ModelInfo():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ModelInfo value)?  $default,){
final _that = this;
switch (_that) {
case _ModelInfo() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  String? description,  int context,  Pricing pricing, @JsonKey(name: 'top_provider')  Provider topProvider, @JsonKey(name: 'per_request_limits')  PerRequestLimits? perRequestLimits)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ModelInfo() when $default != null:
return $default(_that.id,_that.name,_that.description,_that.context,_that.pricing,_that.topProvider,_that.perRequestLimits);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  String? description,  int context,  Pricing pricing, @JsonKey(name: 'top_provider')  Provider topProvider, @JsonKey(name: 'per_request_limits')  PerRequestLimits? perRequestLimits)  $default,) {final _that = this;
switch (_that) {
case _ModelInfo():
return $default(_that.id,_that.name,_that.description,_that.context,_that.pricing,_that.topProvider,_that.perRequestLimits);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  String? description,  int context,  Pricing pricing, @JsonKey(name: 'top_provider')  Provider topProvider, @JsonKey(name: 'per_request_limits')  PerRequestLimits? perRequestLimits)?  $default,) {final _that = this;
switch (_that) {
case _ModelInfo() when $default != null:
return $default(_that.id,_that.name,_that.description,_that.context,_that.pricing,_that.topProvider,_that.perRequestLimits);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ModelInfo implements ModelInfo {
  const _ModelInfo({required this.id, required this.name, this.description, required this.context, required this.pricing, @JsonKey(name: 'top_provider') required this.topProvider, @JsonKey(name: 'per_request_limits') this.perRequestLimits});
  factory _ModelInfo.fromJson(Map<String, dynamic> json) => _$ModelInfoFromJson(json);

@override final  String id;
@override final  String name;
@override final  String? description;
@override final  int context;
@override final  Pricing pricing;
@override@JsonKey(name: 'top_provider') final  Provider topProvider;
@override@JsonKey(name: 'per_request_limits') final  PerRequestLimits? perRequestLimits;

/// Create a copy of ModelInfo
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ModelInfoCopyWith<_ModelInfo> get copyWith => __$ModelInfoCopyWithImpl<_ModelInfo>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ModelInfoToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ModelInfo&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&(identical(other.context, context) || other.context == context)&&(identical(other.pricing, pricing) || other.pricing == pricing)&&(identical(other.topProvider, topProvider) || other.topProvider == topProvider)&&(identical(other.perRequestLimits, perRequestLimits) || other.perRequestLimits == perRequestLimits));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,description,context,pricing,topProvider,perRequestLimits);

@override
String toString() {
  return 'ModelInfo(id: $id, name: $name, description: $description, context: $context, pricing: $pricing, topProvider: $topProvider, perRequestLimits: $perRequestLimits)';
}


}

/// @nodoc
abstract mixin class _$ModelInfoCopyWith<$Res> implements $ModelInfoCopyWith<$Res> {
  factory _$ModelInfoCopyWith(_ModelInfo value, $Res Function(_ModelInfo) _then) = __$ModelInfoCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String? description, int context, Pricing pricing,@JsonKey(name: 'top_provider') Provider topProvider,@JsonKey(name: 'per_request_limits') PerRequestLimits? perRequestLimits
});


@override $PricingCopyWith<$Res> get pricing;@override $ProviderCopyWith<$Res> get topProvider;@override $PerRequestLimitsCopyWith<$Res>? get perRequestLimits;

}
/// @nodoc
class __$ModelInfoCopyWithImpl<$Res>
    implements _$ModelInfoCopyWith<$Res> {
  __$ModelInfoCopyWithImpl(this._self, this._then);

  final _ModelInfo _self;
  final $Res Function(_ModelInfo) _then;

/// Create a copy of ModelInfo
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? description = freezed,Object? context = null,Object? pricing = null,Object? topProvider = null,Object? perRequestLimits = freezed,}) {
  return _then(_ModelInfo(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,context: null == context ? _self.context : context // ignore: cast_nullable_to_non_nullable
as int,pricing: null == pricing ? _self.pricing : pricing // ignore: cast_nullable_to_non_nullable
as Pricing,topProvider: null == topProvider ? _self.topProvider : topProvider // ignore: cast_nullable_to_non_nullable
as Provider,perRequestLimits: freezed == perRequestLimits ? _self.perRequestLimits : perRequestLimits // ignore: cast_nullable_to_non_nullable
as PerRequestLimits?,
  ));
}

/// Create a copy of ModelInfo
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PricingCopyWith<$Res> get pricing {
  
  return $PricingCopyWith<$Res>(_self.pricing, (value) {
    return _then(_self.copyWith(pricing: value));
  });
}/// Create a copy of ModelInfo
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProviderCopyWith<$Res> get topProvider {
  
  return $ProviderCopyWith<$Res>(_self.topProvider, (value) {
    return _then(_self.copyWith(topProvider: value));
  });
}/// Create a copy of ModelInfo
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PerRequestLimitsCopyWith<$Res>? get perRequestLimits {
    if (_self.perRequestLimits == null) {
    return null;
  }

  return $PerRequestLimitsCopyWith<$Res>(_self.perRequestLimits!, (value) {
    return _then(_self.copyWith(perRequestLimits: value));
  });
}
}


/// @nodoc
mixin _$Pricing {

 String get prompt; String get completion; String? get request; String? get image;
/// Create a copy of Pricing
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PricingCopyWith<Pricing> get copyWith => _$PricingCopyWithImpl<Pricing>(this as Pricing, _$identity);

  /// Serializes this Pricing to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Pricing&&(identical(other.prompt, prompt) || other.prompt == prompt)&&(identical(other.completion, completion) || other.completion == completion)&&(identical(other.request, request) || other.request == request)&&(identical(other.image, image) || other.image == image));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,prompt,completion,request,image);

@override
String toString() {
  return 'Pricing(prompt: $prompt, completion: $completion, request: $request, image: $image)';
}


}

/// @nodoc
abstract mixin class $PricingCopyWith<$Res>  {
  factory $PricingCopyWith(Pricing value, $Res Function(Pricing) _then) = _$PricingCopyWithImpl;
@useResult
$Res call({
 String prompt, String completion, String? request, String? image
});




}
/// @nodoc
class _$PricingCopyWithImpl<$Res>
    implements $PricingCopyWith<$Res> {
  _$PricingCopyWithImpl(this._self, this._then);

  final Pricing _self;
  final $Res Function(Pricing) _then;

/// Create a copy of Pricing
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? prompt = null,Object? completion = null,Object? request = freezed,Object? image = freezed,}) {
  return _then(_self.copyWith(
prompt: null == prompt ? _self.prompt : prompt // ignore: cast_nullable_to_non_nullable
as String,completion: null == completion ? _self.completion : completion // ignore: cast_nullable_to_non_nullable
as String,request: freezed == request ? _self.request : request // ignore: cast_nullable_to_non_nullable
as String?,image: freezed == image ? _self.image : image // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [Pricing].
extension PricingPatterns on Pricing {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Pricing value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Pricing() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Pricing value)  $default,){
final _that = this;
switch (_that) {
case _Pricing():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Pricing value)?  $default,){
final _that = this;
switch (_that) {
case _Pricing() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String prompt,  String completion,  String? request,  String? image)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Pricing() when $default != null:
return $default(_that.prompt,_that.completion,_that.request,_that.image);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String prompt,  String completion,  String? request,  String? image)  $default,) {final _that = this;
switch (_that) {
case _Pricing():
return $default(_that.prompt,_that.completion,_that.request,_that.image);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String prompt,  String completion,  String? request,  String? image)?  $default,) {final _that = this;
switch (_that) {
case _Pricing() when $default != null:
return $default(_that.prompt,_that.completion,_that.request,_that.image);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Pricing implements Pricing {
  const _Pricing({required this.prompt, required this.completion, this.request, this.image});
  factory _Pricing.fromJson(Map<String, dynamic> json) => _$PricingFromJson(json);

@override final  String prompt;
@override final  String completion;
@override final  String? request;
@override final  String? image;

/// Create a copy of Pricing
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PricingCopyWith<_Pricing> get copyWith => __$PricingCopyWithImpl<_Pricing>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PricingToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Pricing&&(identical(other.prompt, prompt) || other.prompt == prompt)&&(identical(other.completion, completion) || other.completion == completion)&&(identical(other.request, request) || other.request == request)&&(identical(other.image, image) || other.image == image));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,prompt,completion,request,image);

@override
String toString() {
  return 'Pricing(prompt: $prompt, completion: $completion, request: $request, image: $image)';
}


}

/// @nodoc
abstract mixin class _$PricingCopyWith<$Res> implements $PricingCopyWith<$Res> {
  factory _$PricingCopyWith(_Pricing value, $Res Function(_Pricing) _then) = __$PricingCopyWithImpl;
@override @useResult
$Res call({
 String prompt, String completion, String? request, String? image
});




}
/// @nodoc
class __$PricingCopyWithImpl<$Res>
    implements _$PricingCopyWith<$Res> {
  __$PricingCopyWithImpl(this._self, this._then);

  final _Pricing _self;
  final $Res Function(_Pricing) _then;

/// Create a copy of Pricing
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? prompt = null,Object? completion = null,Object? request = freezed,Object? image = freezed,}) {
  return _then(_Pricing(
prompt: null == prompt ? _self.prompt : prompt // ignore: cast_nullable_to_non_nullable
as String,completion: null == completion ? _self.completion : completion // ignore: cast_nullable_to_non_nullable
as String,request: freezed == request ? _self.request : request // ignore: cast_nullable_to_non_nullable
as String?,image: freezed == image ? _self.image : image // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$Provider {

@JsonKey(name: 'max_completion_tokens') int? get maxCompletionTokens;@JsonKey(name: 'is_moderated') bool? get isModerated;
/// Create a copy of Provider
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProviderCopyWith<Provider> get copyWith => _$ProviderCopyWithImpl<Provider>(this as Provider, _$identity);

  /// Serializes this Provider to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Provider&&(identical(other.maxCompletionTokens, maxCompletionTokens) || other.maxCompletionTokens == maxCompletionTokens)&&(identical(other.isModerated, isModerated) || other.isModerated == isModerated));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,maxCompletionTokens,isModerated);

@override
String toString() {
  return 'Provider(maxCompletionTokens: $maxCompletionTokens, isModerated: $isModerated)';
}


}

/// @nodoc
abstract mixin class $ProviderCopyWith<$Res>  {
  factory $ProviderCopyWith(Provider value, $Res Function(Provider) _then) = _$ProviderCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'max_completion_tokens') int? maxCompletionTokens,@JsonKey(name: 'is_moderated') bool? isModerated
});




}
/// @nodoc
class _$ProviderCopyWithImpl<$Res>
    implements $ProviderCopyWith<$Res> {
  _$ProviderCopyWithImpl(this._self, this._then);

  final Provider _self;
  final $Res Function(Provider) _then;

/// Create a copy of Provider
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? maxCompletionTokens = freezed,Object? isModerated = freezed,}) {
  return _then(_self.copyWith(
maxCompletionTokens: freezed == maxCompletionTokens ? _self.maxCompletionTokens : maxCompletionTokens // ignore: cast_nullable_to_non_nullable
as int?,isModerated: freezed == isModerated ? _self.isModerated : isModerated // ignore: cast_nullable_to_non_nullable
as bool?,
  ));
}

}


/// Adds pattern-matching-related methods to [Provider].
extension ProviderPatterns on Provider {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Provider value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Provider() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Provider value)  $default,){
final _that = this;
switch (_that) {
case _Provider():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Provider value)?  $default,){
final _that = this;
switch (_that) {
case _Provider() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: 'max_completion_tokens')  int? maxCompletionTokens, @JsonKey(name: 'is_moderated')  bool? isModerated)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Provider() when $default != null:
return $default(_that.maxCompletionTokens,_that.isModerated);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: 'max_completion_tokens')  int? maxCompletionTokens, @JsonKey(name: 'is_moderated')  bool? isModerated)  $default,) {final _that = this;
switch (_that) {
case _Provider():
return $default(_that.maxCompletionTokens,_that.isModerated);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: 'max_completion_tokens')  int? maxCompletionTokens, @JsonKey(name: 'is_moderated')  bool? isModerated)?  $default,) {final _that = this;
switch (_that) {
case _Provider() when $default != null:
return $default(_that.maxCompletionTokens,_that.isModerated);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Provider implements Provider {
  const _Provider({@JsonKey(name: 'max_completion_tokens') this.maxCompletionTokens, @JsonKey(name: 'is_moderated') this.isModerated});
  factory _Provider.fromJson(Map<String, dynamic> json) => _$ProviderFromJson(json);

@override@JsonKey(name: 'max_completion_tokens') final  int? maxCompletionTokens;
@override@JsonKey(name: 'is_moderated') final  bool? isModerated;

/// Create a copy of Provider
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProviderCopyWith<_Provider> get copyWith => __$ProviderCopyWithImpl<_Provider>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ProviderToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Provider&&(identical(other.maxCompletionTokens, maxCompletionTokens) || other.maxCompletionTokens == maxCompletionTokens)&&(identical(other.isModerated, isModerated) || other.isModerated == isModerated));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,maxCompletionTokens,isModerated);

@override
String toString() {
  return 'Provider(maxCompletionTokens: $maxCompletionTokens, isModerated: $isModerated)';
}


}

/// @nodoc
abstract mixin class _$ProviderCopyWith<$Res> implements $ProviderCopyWith<$Res> {
  factory _$ProviderCopyWith(_Provider value, $Res Function(_Provider) _then) = __$ProviderCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'max_completion_tokens') int? maxCompletionTokens,@JsonKey(name: 'is_moderated') bool? isModerated
});




}
/// @nodoc
class __$ProviderCopyWithImpl<$Res>
    implements _$ProviderCopyWith<$Res> {
  __$ProviderCopyWithImpl(this._self, this._then);

  final _Provider _self;
  final $Res Function(_Provider) _then;

/// Create a copy of Provider
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? maxCompletionTokens = freezed,Object? isModerated = freezed,}) {
  return _then(_Provider(
maxCompletionTokens: freezed == maxCompletionTokens ? _self.maxCompletionTokens : maxCompletionTokens // ignore: cast_nullable_to_non_nullable
as int?,isModerated: freezed == isModerated ? _self.isModerated : isModerated // ignore: cast_nullable_to_non_nullable
as bool?,
  ));
}


}


/// @nodoc
mixin _$PerRequestLimits {

@JsonKey(name: 'prompt_tokens') int? get promptTokens;@JsonKey(name: 'completion_tokens') int? get completionTokens;
/// Create a copy of PerRequestLimits
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PerRequestLimitsCopyWith<PerRequestLimits> get copyWith => _$PerRequestLimitsCopyWithImpl<PerRequestLimits>(this as PerRequestLimits, _$identity);

  /// Serializes this PerRequestLimits to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PerRequestLimits&&(identical(other.promptTokens, promptTokens) || other.promptTokens == promptTokens)&&(identical(other.completionTokens, completionTokens) || other.completionTokens == completionTokens));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,promptTokens,completionTokens);

@override
String toString() {
  return 'PerRequestLimits(promptTokens: $promptTokens, completionTokens: $completionTokens)';
}


}

/// @nodoc
abstract mixin class $PerRequestLimitsCopyWith<$Res>  {
  factory $PerRequestLimitsCopyWith(PerRequestLimits value, $Res Function(PerRequestLimits) _then) = _$PerRequestLimitsCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'prompt_tokens') int? promptTokens,@JsonKey(name: 'completion_tokens') int? completionTokens
});




}
/// @nodoc
class _$PerRequestLimitsCopyWithImpl<$Res>
    implements $PerRequestLimitsCopyWith<$Res> {
  _$PerRequestLimitsCopyWithImpl(this._self, this._then);

  final PerRequestLimits _self;
  final $Res Function(PerRequestLimits) _then;

/// Create a copy of PerRequestLimits
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? promptTokens = freezed,Object? completionTokens = freezed,}) {
  return _then(_self.copyWith(
promptTokens: freezed == promptTokens ? _self.promptTokens : promptTokens // ignore: cast_nullable_to_non_nullable
as int?,completionTokens: freezed == completionTokens ? _self.completionTokens : completionTokens // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// Adds pattern-matching-related methods to [PerRequestLimits].
extension PerRequestLimitsPatterns on PerRequestLimits {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PerRequestLimits value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PerRequestLimits() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PerRequestLimits value)  $default,){
final _that = this;
switch (_that) {
case _PerRequestLimits():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PerRequestLimits value)?  $default,){
final _that = this;
switch (_that) {
case _PerRequestLimits() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: 'prompt_tokens')  int? promptTokens, @JsonKey(name: 'completion_tokens')  int? completionTokens)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PerRequestLimits() when $default != null:
return $default(_that.promptTokens,_that.completionTokens);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: 'prompt_tokens')  int? promptTokens, @JsonKey(name: 'completion_tokens')  int? completionTokens)  $default,) {final _that = this;
switch (_that) {
case _PerRequestLimits():
return $default(_that.promptTokens,_that.completionTokens);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: 'prompt_tokens')  int? promptTokens, @JsonKey(name: 'completion_tokens')  int? completionTokens)?  $default,) {final _that = this;
switch (_that) {
case _PerRequestLimits() when $default != null:
return $default(_that.promptTokens,_that.completionTokens);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PerRequestLimits implements PerRequestLimits {
  const _PerRequestLimits({@JsonKey(name: 'prompt_tokens') this.promptTokens, @JsonKey(name: 'completion_tokens') this.completionTokens});
  factory _PerRequestLimits.fromJson(Map<String, dynamic> json) => _$PerRequestLimitsFromJson(json);

@override@JsonKey(name: 'prompt_tokens') final  int? promptTokens;
@override@JsonKey(name: 'completion_tokens') final  int? completionTokens;

/// Create a copy of PerRequestLimits
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PerRequestLimitsCopyWith<_PerRequestLimits> get copyWith => __$PerRequestLimitsCopyWithImpl<_PerRequestLimits>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PerRequestLimitsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PerRequestLimits&&(identical(other.promptTokens, promptTokens) || other.promptTokens == promptTokens)&&(identical(other.completionTokens, completionTokens) || other.completionTokens == completionTokens));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,promptTokens,completionTokens);

@override
String toString() {
  return 'PerRequestLimits(promptTokens: $promptTokens, completionTokens: $completionTokens)';
}


}

/// @nodoc
abstract mixin class _$PerRequestLimitsCopyWith<$Res> implements $PerRequestLimitsCopyWith<$Res> {
  factory _$PerRequestLimitsCopyWith(_PerRequestLimits value, $Res Function(_PerRequestLimits) _then) = __$PerRequestLimitsCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'prompt_tokens') int? promptTokens,@JsonKey(name: 'completion_tokens') int? completionTokens
});




}
/// @nodoc
class __$PerRequestLimitsCopyWithImpl<$Res>
    implements _$PerRequestLimitsCopyWith<$Res> {
  __$PerRequestLimitsCopyWithImpl(this._self, this._then);

  final _PerRequestLimits _self;
  final $Res Function(_PerRequestLimits) _then;

/// Create a copy of PerRequestLimits
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? promptTokens = freezed,Object? completionTokens = freezed,}) {
  return _then(_PerRequestLimits(
promptTokens: freezed == promptTokens ? _self.promptTokens : promptTokens // ignore: cast_nullable_to_non_nullable
as int?,completionTokens: freezed == completionTokens ? _self.completionTokens : completionTokens // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}

// dart format on
