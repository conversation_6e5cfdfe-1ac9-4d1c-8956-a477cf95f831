import 'dart:convert';
import 'package:http/http.dart' as http;

class OpenRouterModel {
  final String id;
  final String name;
  final String description;
  final int contextLength;
  final Map<String, dynamic> pricing;
  final int? maxOutput;
  final List<String> modalities;
  final bool supportsVision;
  final bool supportsFunctionCalling;
  final bool supportsSystemMessage;
  final String? architecture;
  final String? organization;

  const OpenRouterModel({
    required this.id,
    required this.name,
    required this.description,
    required this.contextLength,
    required this.pricing,
    this.maxOutput,
    required this.modalities,
    required this.supportsVision,
    required this.supportsFunctionCalling,
    required this.supportsSystemMessage,
    this.architecture,
    this.organization,
  });

  factory OpenRouterModel.fromJson(Map<String, dynamic> json) {
    final inputModalities = List<String>.from(json['input_modalities'] ?? ['text']);
    final outputModalities = List<String>.from(json['output_modalities'] ?? ['text']);
    final supportedFeatures = List<String>.from(json['supported_features'] ?? []);
    final allModalities = {...inputModalities, ...outputModalities}.toList();
    
    return OpenRouterModel(
      id: json['id'] ?? '',
      name: json['name'] ?? json['id'] ?? '',
      description: json['description'] ?? '',
      contextLength: json['context_length'] ?? 4096,
      pricing: Map<String, dynamic>.from(json['pricing'] ?? {}),
      maxOutput: json['max_output_length'],
      modalities: allModalities,
      supportsVision: inputModalities.contains('image') || supportedFeatures.contains('vision'),
      supportsFunctionCalling: supportedFeatures.contains('tools') || supportedFeatures.contains('function_calling'),
      supportsSystemMessage: true, // Most models support system messages
      architecture: json['quantization'],
      organization: _extractOrganization(json['id']),
    );
  }

  static String? _extractOrganization(String? id) {
    if (id == null || !id.contains('/')) return null;
    return id.split('/').first;
  }

  double get inputPricePerMillion {
    final prompt = pricing['prompt'];
    if (prompt == null) return 0.0;
    return double.tryParse(prompt.toString()) ?? 0.0;
  }

  double get outputPricePerMillion {
    final completion = pricing['completion'];
    if (completion == null) return 0.0;
    return double.tryParse(completion.toString()) ?? 0.0;
  }

  String get priceRange {
    final input = inputPricePerMillion;
    final output = outputPricePerMillion;
    final max = input > output ? input : output;
    
    if (max < 1) return 'Under \$1/M';
    if (max < 5) return 'Under \$5/M';
    if (max < 20) return 'Under \$20/M';
    return '\$20+/M';
  }

  String get contextSizeLabel {
    if (contextLength >= 128000) return '128k+';
    if (contextLength >= 32000) return '32k+';
    if (contextLength >= 8000) return '8k+';
    return '${(contextLength / 1000).toStringAsFixed(0)}k';
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'context_length': contextLength,
      'pricing': pricing,
      'max_output': maxOutput,
      'modalities': modalities,
      'supports_vision': supportsVision,
      'supports_function_calling': supportsFunctionCalling,
      'supports_system_message': supportsSystemMessage,
      'architecture': architecture,
      'organization': organization,
    };
  }
}

class OpenRouterModelsApi {
  static const String baseUrl = 'https://openrouter.ai/api/v1';
  final String? apiKey;
  final http.Client _client = http.Client();

  OpenRouterModelsApi({this.apiKey});

  Future<List<OpenRouterModel>> fetchAllModels() async {
    try {
      final uri = Uri.parse('$baseUrl/models');
      final headers = <String, String>{
        'Content-Type': 'application/json',
      };
      
      if (apiKey != null) {
        headers['Authorization'] = 'Bearer $apiKey';
      }

      final response = await _client.get(uri, headers: headers);
      
      if (response.statusCode != 200) {
        throw Exception('Failed to fetch models: ${response.statusCode}');
      }

      final data = jsonDecode(response.body);
      final modelsData = data['data'] as List<dynamic>? ?? [];
      
      return modelsData
          .map((json) => OpenRouterModel.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Error fetching models: $e');
    }
  }

  Future<OpenRouterModel?> fetchModelDetails(String modelId) async {
    try {
      final models = await fetchAllModels();
      return models.firstWhere(
        (model) => model.id == modelId,
        orElse: () => throw Exception('Model not found'),
      );
    } catch (e) {
      return null;
    }
  }

  void dispose() {
    _client.close();
  }
}

// Active model configuration
class ActiveModel {
  final OpenRouterModel model;
  final String? nickname;
  final double? temperatureOverride;
  final int? maxTokensOverride;
  final bool isFavorite;
  final int order;

  const ActiveModel({
    required this.model,
    this.nickname,
    this.temperatureOverride,
    this.maxTokensOverride,
    this.isFavorite = false,
    required this.order,
  });

  String get displayName => nickname ?? model.name;

  factory ActiveModel.fromJson(Map<String, dynamic> json) {
    return ActiveModel(
      model: OpenRouterModel.fromJson(json['model']),
      nickname: json['nickname'],
      temperatureOverride: json['temperature_override']?.toDouble(),
      maxTokensOverride: json['max_tokens_override'],
      isFavorite: json['is_favorite'] ?? false,
      order: json['order'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'model': model.toJson(),
      'nickname': nickname,
      'temperature_override': temperatureOverride,
      'max_tokens_override': maxTokensOverride,
      'is_favorite': isFavorite,
      'order': order,
    };
  }

  ActiveModel copyWith({
    OpenRouterModel? model,
    String? nickname,
    double? temperatureOverride,
    int? maxTokensOverride,
    bool? isFavorite,
    int? order,
  }) {
    return ActiveModel(
      model: model ?? this.model,
      nickname: nickname ?? this.nickname,
      temperatureOverride: temperatureOverride ?? this.temperatureOverride,
      maxTokensOverride: maxTokensOverride ?? this.maxTokensOverride,
      isFavorite: isFavorite ?? this.isFavorite,
      order: order ?? this.order,
    );
  }
}