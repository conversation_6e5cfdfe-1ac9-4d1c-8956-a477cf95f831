import 'package:freezed_annotation/freezed_annotation.dart';

part 'openrouter_models.freezed.dart';
part 'openrouter_models.g.dart';

@freezed
abstract class ChatMessage with _$ChatMessage {
  const factory ChatMessage({
    required String role,
    required String content,
    String? name,
    @JsonKey(name: 'tool_calls') List<ToolCall>? toolCalls,
    @JsonKey(name: 'tool_call_id') String? toolCallId,
  }) = _ChatMessage;

  factory ChatMessage.fromJson(Map<String, dynamic> json) =>
      _$ChatMessageFromJson(json);
}

@freezed
abstract class ToolCall with _$ToolCall {
  const factory ToolCall({
    required String id,
    required String type,
    required ToolFunction function,
  }) = _ToolCall;

  factory ToolCall.fromJson(Map<String, dynamic> json) =>
      _$ToolCallFromJson(json);
}

@freezed
abstract class ToolFunction with _$ToolFunction {
  const factory ToolFunction({
    required String name,
    required String arguments,
  }) = _ToolFunction;

  factory ToolFunction.from<PERSON>son(Map<String, dynamic> json) =>
      _$ToolFunctionFromJson(json);
}

@freezed
abstract class ChatCompletionRequest with _$ChatCompletionRequest {
  const factory ChatCompletionRequest({
    required String model,
    required List<ChatMessage> messages,
    @JsonKey(name: 'max_tokens') int? maxTokens,
    double? temperature,
    @JsonKey(name: 'top_p') double? topP,
    @JsonKey(name: 'top_k') int? topK,
    @JsonKey(name: 'frequency_penalty') double? frequencyPenalty,
    @JsonKey(name: 'presence_penalty') double? presencePenalty,
    @JsonKey(name: 'repetition_penalty') double? repetitionPenalty,
    @JsonKey(name: 'min_p') double? minP,
    @JsonKey(name: 'top_a') double? topA,
    int? seed,
    @JsonKey(name: 'logit_bias') Map<String, int>? logitBias,
    bool? logprobs,
    @JsonKey(name: 'top_logprobs') int? topLogprobs,
    @JsonKey(name: 'response_format') ResponseFormat? responseFormat,
    List<String>? stop,
    List<Tool>? tools,
    @JsonKey(name: 'tool_choice') Object? toolChoice,
    bool? stream,
    Map<String, String>? transforms,
    List<String>? models,
    String? route,
    @Default(false) bool fallback,
  }) = _ChatCompletionRequest;

  factory ChatCompletionRequest.fromJson(Map<String, dynamic> json) =>
      _$ChatCompletionRequestFromJson(json);
}

@freezed
abstract class ResponseFormat with _$ResponseFormat {
  const factory ResponseFormat({
    required String type,
  }) = _ResponseFormat;

  factory ResponseFormat.fromJson(Map<String, dynamic> json) =>
      _$ResponseFormatFromJson(json);
}

@freezed
abstract class Tool with _$Tool {
  const factory Tool({
    required String type,
    required ToolFunctionSchema function,
  }) = _Tool;

  factory Tool.fromJson(Map<String, dynamic> json) => _$ToolFromJson(json);
}

@freezed
abstract class ToolFunctionSchema with _$ToolFunctionSchema {
  const factory ToolFunctionSchema({
    required String name,
    String? description,
    Map<String, dynamic>? parameters,
  }) = _ToolFunctionSchema;

  factory ToolFunctionSchema.fromJson(Map<String, dynamic> json) =>
      _$ToolFunctionSchemaFromJson(json);
}

@freezed
abstract class ChatCompletionResponse with _$ChatCompletionResponse {
  const factory ChatCompletionResponse({
    required String id,
    required String object,
    required int created,
    required String model,
    required List<ChatCompletionChoice> choices,
    required Usage usage,
    @JsonKey(name: 'system_fingerprint') String? systemFingerprint,
  }) = _ChatCompletionResponse;

  factory ChatCompletionResponse.fromJson(Map<String, dynamic> json) =>
      _$ChatCompletionResponseFromJson(json);
}

@freezed
abstract class ChatCompletionChoice with _$ChatCompletionChoice {
  const factory ChatCompletionChoice({
    required int index,
    required ChatMessage message,
    @JsonKey(name: 'finish_reason') String? finishReason,
    Logprobs? logprobs,
  }) = _ChatCompletionChoice;

  factory ChatCompletionChoice.fromJson(Map<String, dynamic> json) =>
      _$ChatCompletionChoiceFromJson(json);
}

@freezed
abstract class ChatCompletionChunk with _$ChatCompletionChunk {
  const factory ChatCompletionChunk({
    required String id,
    required String object,
    required int created,
    required String model,
    required List<ChatCompletionChunkChoice> choices,
    Usage? usage,
    @JsonKey(name: 'system_fingerprint') String? systemFingerprint,
  }) = _ChatCompletionChunk;

  factory ChatCompletionChunk.fromJson(Map<String, dynamic> json) =>
      _$ChatCompletionChunkFromJson(json);
}

@freezed
abstract class ChatCompletionChunkChoice with _$ChatCompletionChunkChoice {
  const factory ChatCompletionChunkChoice({
    required int index,
    required ChatMessageDelta delta,
    @JsonKey(name: 'finish_reason') String? finishReason,
    Logprobs? logprobs,
  }) = _ChatCompletionChunkChoice;

  factory ChatCompletionChunkChoice.fromJson(Map<String, dynamic> json) =>
      _$ChatCompletionChunkChoiceFromJson(json);
}

@freezed
abstract class ChatMessageDelta with _$ChatMessageDelta {
  const factory ChatMessageDelta({
    String? role,
    String? content,
    @JsonKey(name: 'tool_calls') List<ToolCallDelta>? toolCalls,
  }) = _ChatMessageDelta;

  factory ChatMessageDelta.fromJson(Map<String, dynamic> json) =>
      _$ChatMessageDeltaFromJson(json);
}

@freezed
abstract class ToolCallDelta with _$ToolCallDelta {
  const factory ToolCallDelta({
    int? index,
    String? id,
    String? type,
    ToolFunctionDelta? function,
  }) = _ToolCallDelta;

  factory ToolCallDelta.fromJson(Map<String, dynamic> json) =>
      _$ToolCallDeltaFromJson(json);
}

@freezed
abstract class ToolFunctionDelta with _$ToolFunctionDelta {
  const factory ToolFunctionDelta({
    String? name,
    String? arguments,
  }) = _ToolFunctionDelta;

  factory ToolFunctionDelta.fromJson(Map<String, dynamic> json) =>
      _$ToolFunctionDeltaFromJson(json);
}

@freezed
abstract class Usage with _$Usage {
  const factory Usage({
    @JsonKey(name: 'prompt_tokens') required int promptTokens,
    @JsonKey(name: 'completion_tokens') int? completionTokens,
    @JsonKey(name: 'total_tokens') required int totalTokens,
  }) = _Usage;

  factory Usage.fromJson(Map<String, dynamic> json) => _$UsageFromJson(json);
}

@freezed
abstract class Logprobs with _$Logprobs {
  const factory Logprobs({
    required List<Map<String, dynamic>> content,
  }) = _Logprobs;

  factory Logprobs.fromJson(Map<String, dynamic> json) =>
      _$LogprobsFromJson(json);
}

@freezed
abstract class ModelInfo with _$ModelInfo {
  const factory ModelInfo({
    required String id,
    required String name,
    String? description,
    required int context,
    required Pricing pricing,
    @JsonKey(name: 'top_provider') required Provider topProvider,
    @JsonKey(name: 'per_request_limits') PerRequestLimits? perRequestLimits,
  }) = _ModelInfo;

  factory ModelInfo.fromJson(Map<String, dynamic> json) =>
      _$ModelInfoFromJson(json);
}

@freezed
abstract class Pricing with _$Pricing {
  const factory Pricing({
    required String prompt,
    required String completion,
    String? request,
    String? image,
  }) = _Pricing;

  factory Pricing.fromJson(Map<String, dynamic> json) => _$PricingFromJson(json);
}

@freezed
abstract class Provider with _$Provider {
  const factory Provider({
    @JsonKey(name: 'max_completion_tokens') int? maxCompletionTokens,
    @JsonKey(name: 'is_moderated') bool? isModerated,
  }) = _Provider;

  factory Provider.fromJson(Map<String, dynamic> json) => _$ProviderFromJson(json);
}

@freezed
abstract class PerRequestLimits with _$PerRequestLimits {
  const factory PerRequestLimits({
    @JsonKey(name: 'prompt_tokens') int? promptTokens,
    @JsonKey(name: 'completion_tokens') int? completionTokens,
  }) = _PerRequestLimits;

  factory PerRequestLimits.fromJson(Map<String, dynamic> json) =>
      _$PerRequestLimitsFromJson(json);
}