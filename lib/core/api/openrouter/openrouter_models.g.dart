// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'openrouter_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ChatMessage _$ChatMessageFromJson(Map<String, dynamic> json) => _ChatMessage(
  role: json['role'] as String,
  content: json['content'] as String,
  name: json['name'] as String?,
  toolCalls: (json['tool_calls'] as List<dynamic>?)
      ?.map((e) => ToolCall.fromJson(e as Map<String, dynamic>))
      .toList(),
  toolCallId: json['tool_call_id'] as String?,
);

Map<String, dynamic> _$ChatMessageToJson(_ChatMessage instance) =>
    <String, dynamic>{
      'role': instance.role,
      'content': instance.content,
      'name': instance.name,
      'tool_calls': instance.toolCalls,
      'tool_call_id': instance.toolCallId,
    };

_ToolCall _$ToolCallFromJson(Map<String, dynamic> json) => _ToolCall(
  id: json['id'] as String,
  type: json['type'] as String,
  function: ToolFunction.fromJson(json['function'] as Map<String, dynamic>),
);

Map<String, dynamic> _$ToolCallToJson(_ToolCall instance) => <String, dynamic>{
  'id': instance.id,
  'type': instance.type,
  'function': instance.function,
};

_ToolFunction _$ToolFunctionFromJson(Map<String, dynamic> json) =>
    _ToolFunction(
      name: json['name'] as String,
      arguments: json['arguments'] as String,
    );

Map<String, dynamic> _$ToolFunctionToJson(_ToolFunction instance) =>
    <String, dynamic>{'name': instance.name, 'arguments': instance.arguments};

_ChatCompletionRequest _$ChatCompletionRequestFromJson(
  Map<String, dynamic> json,
) => _ChatCompletionRequest(
  model: json['model'] as String,
  messages: (json['messages'] as List<dynamic>)
      .map((e) => ChatMessage.fromJson(e as Map<String, dynamic>))
      .toList(),
  maxTokens: (json['max_tokens'] as num?)?.toInt(),
  temperature: (json['temperature'] as num?)?.toDouble(),
  topP: (json['top_p'] as num?)?.toDouble(),
  topK: (json['top_k'] as num?)?.toInt(),
  frequencyPenalty: (json['frequency_penalty'] as num?)?.toDouble(),
  presencePenalty: (json['presence_penalty'] as num?)?.toDouble(),
  repetitionPenalty: (json['repetition_penalty'] as num?)?.toDouble(),
  minP: (json['min_p'] as num?)?.toDouble(),
  topA: (json['top_a'] as num?)?.toDouble(),
  seed: (json['seed'] as num?)?.toInt(),
  logitBias: (json['logit_bias'] as Map<String, dynamic>?)?.map(
    (k, e) => MapEntry(k, (e as num).toInt()),
  ),
  logprobs: json['logprobs'] as bool?,
  topLogprobs: (json['top_logprobs'] as num?)?.toInt(),
  responseFormat: json['response_format'] == null
      ? null
      : ResponseFormat.fromJson(
          json['response_format'] as Map<String, dynamic>,
        ),
  stop: (json['stop'] as List<dynamic>?)?.map((e) => e as String).toList(),
  tools: (json['tools'] as List<dynamic>?)
      ?.map((e) => Tool.fromJson(e as Map<String, dynamic>))
      .toList(),
  toolChoice: json['tool_choice'],
  stream: json['stream'] as bool?,
  transforms: (json['transforms'] as Map<String, dynamic>?)?.map(
    (k, e) => MapEntry(k, e as String),
  ),
  models: (json['models'] as List<dynamic>?)?.map((e) => e as String).toList(),
  route: json['route'] as String?,
  fallback: json['fallback'] as bool? ?? false,
);

Map<String, dynamic> _$ChatCompletionRequestToJson(
  _ChatCompletionRequest instance,
) => <String, dynamic>{
  'model': instance.model,
  'messages': instance.messages,
  'max_tokens': instance.maxTokens,
  'temperature': instance.temperature,
  'top_p': instance.topP,
  'top_k': instance.topK,
  'frequency_penalty': instance.frequencyPenalty,
  'presence_penalty': instance.presencePenalty,
  'repetition_penalty': instance.repetitionPenalty,
  'min_p': instance.minP,
  'top_a': instance.topA,
  'seed': instance.seed,
  'logit_bias': instance.logitBias,
  'logprobs': instance.logprobs,
  'top_logprobs': instance.topLogprobs,
  'response_format': instance.responseFormat,
  'stop': instance.stop,
  'tools': instance.tools,
  'tool_choice': instance.toolChoice,
  'stream': instance.stream,
  'transforms': instance.transforms,
  'models': instance.models,
  'route': instance.route,
  'fallback': instance.fallback,
};

_ResponseFormat _$ResponseFormatFromJson(Map<String, dynamic> json) =>
    _ResponseFormat(type: json['type'] as String);

Map<String, dynamic> _$ResponseFormatToJson(_ResponseFormat instance) =>
    <String, dynamic>{'type': instance.type};

_Tool _$ToolFromJson(Map<String, dynamic> json) => _Tool(
  type: json['type'] as String,
  function: ToolFunctionSchema.fromJson(
    json['function'] as Map<String, dynamic>,
  ),
);

Map<String, dynamic> _$ToolToJson(_Tool instance) => <String, dynamic>{
  'type': instance.type,
  'function': instance.function,
};

_ToolFunctionSchema _$ToolFunctionSchemaFromJson(Map<String, dynamic> json) =>
    _ToolFunctionSchema(
      name: json['name'] as String,
      description: json['description'] as String?,
      parameters: json['parameters'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$ToolFunctionSchemaToJson(_ToolFunctionSchema instance) =>
    <String, dynamic>{
      'name': instance.name,
      'description': instance.description,
      'parameters': instance.parameters,
    };

_ChatCompletionResponse _$ChatCompletionResponseFromJson(
  Map<String, dynamic> json,
) => _ChatCompletionResponse(
  id: json['id'] as String,
  object: json['object'] as String,
  created: (json['created'] as num).toInt(),
  model: json['model'] as String,
  choices: (json['choices'] as List<dynamic>)
      .map((e) => ChatCompletionChoice.fromJson(e as Map<String, dynamic>))
      .toList(),
  usage: Usage.fromJson(json['usage'] as Map<String, dynamic>),
  systemFingerprint: json['system_fingerprint'] as String?,
);

Map<String, dynamic> _$ChatCompletionResponseToJson(
  _ChatCompletionResponse instance,
) => <String, dynamic>{
  'id': instance.id,
  'object': instance.object,
  'created': instance.created,
  'model': instance.model,
  'choices': instance.choices,
  'usage': instance.usage,
  'system_fingerprint': instance.systemFingerprint,
};

_ChatCompletionChoice _$ChatCompletionChoiceFromJson(
  Map<String, dynamic> json,
) => _ChatCompletionChoice(
  index: (json['index'] as num).toInt(),
  message: ChatMessage.fromJson(json['message'] as Map<String, dynamic>),
  finishReason: json['finish_reason'] as String?,
  logprobs: json['logprobs'] == null
      ? null
      : Logprobs.fromJson(json['logprobs'] as Map<String, dynamic>),
);

Map<String, dynamic> _$ChatCompletionChoiceToJson(
  _ChatCompletionChoice instance,
) => <String, dynamic>{
  'index': instance.index,
  'message': instance.message,
  'finish_reason': instance.finishReason,
  'logprobs': instance.logprobs,
};

_ChatCompletionChunk _$ChatCompletionChunkFromJson(Map<String, dynamic> json) =>
    _ChatCompletionChunk(
      id: json['id'] as String,
      object: json['object'] as String,
      created: (json['created'] as num).toInt(),
      model: json['model'] as String,
      choices: (json['choices'] as List<dynamic>)
          .map(
            (e) =>
                ChatCompletionChunkChoice.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
      usage: json['usage'] == null
          ? null
          : Usage.fromJson(json['usage'] as Map<String, dynamic>),
      systemFingerprint: json['system_fingerprint'] as String?,
    );

Map<String, dynamic> _$ChatCompletionChunkToJson(
  _ChatCompletionChunk instance,
) => <String, dynamic>{
  'id': instance.id,
  'object': instance.object,
  'created': instance.created,
  'model': instance.model,
  'choices': instance.choices,
  'usage': instance.usage,
  'system_fingerprint': instance.systemFingerprint,
};

_ChatCompletionChunkChoice _$ChatCompletionChunkChoiceFromJson(
  Map<String, dynamic> json,
) => _ChatCompletionChunkChoice(
  index: (json['index'] as num).toInt(),
  delta: ChatMessageDelta.fromJson(json['delta'] as Map<String, dynamic>),
  finishReason: json['finish_reason'] as String?,
  logprobs: json['logprobs'] == null
      ? null
      : Logprobs.fromJson(json['logprobs'] as Map<String, dynamic>),
);

Map<String, dynamic> _$ChatCompletionChunkChoiceToJson(
  _ChatCompletionChunkChoice instance,
) => <String, dynamic>{
  'index': instance.index,
  'delta': instance.delta,
  'finish_reason': instance.finishReason,
  'logprobs': instance.logprobs,
};

_ChatMessageDelta _$ChatMessageDeltaFromJson(Map<String, dynamic> json) =>
    _ChatMessageDelta(
      role: json['role'] as String?,
      content: json['content'] as String?,
      toolCalls: (json['tool_calls'] as List<dynamic>?)
          ?.map((e) => ToolCallDelta.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$ChatMessageDeltaToJson(_ChatMessageDelta instance) =>
    <String, dynamic>{
      'role': instance.role,
      'content': instance.content,
      'tool_calls': instance.toolCalls,
    };

_ToolCallDelta _$ToolCallDeltaFromJson(Map<String, dynamic> json) =>
    _ToolCallDelta(
      index: (json['index'] as num?)?.toInt(),
      id: json['id'] as String?,
      type: json['type'] as String?,
      function: json['function'] == null
          ? null
          : ToolFunctionDelta.fromJson(
              json['function'] as Map<String, dynamic>,
            ),
    );

Map<String, dynamic> _$ToolCallDeltaToJson(_ToolCallDelta instance) =>
    <String, dynamic>{
      'index': instance.index,
      'id': instance.id,
      'type': instance.type,
      'function': instance.function,
    };

_ToolFunctionDelta _$ToolFunctionDeltaFromJson(Map<String, dynamic> json) =>
    _ToolFunctionDelta(
      name: json['name'] as String?,
      arguments: json['arguments'] as String?,
    );

Map<String, dynamic> _$ToolFunctionDeltaToJson(_ToolFunctionDelta instance) =>
    <String, dynamic>{'name': instance.name, 'arguments': instance.arguments};

_Usage _$UsageFromJson(Map<String, dynamic> json) => _Usage(
  promptTokens: (json['prompt_tokens'] as num).toInt(),
  completionTokens: (json['completion_tokens'] as num?)?.toInt(),
  totalTokens: (json['total_tokens'] as num).toInt(),
);

Map<String, dynamic> _$UsageToJson(_Usage instance) => <String, dynamic>{
  'prompt_tokens': instance.promptTokens,
  'completion_tokens': instance.completionTokens,
  'total_tokens': instance.totalTokens,
};

_Logprobs _$LogprobsFromJson(Map<String, dynamic> json) => _Logprobs(
  content: (json['content'] as List<dynamic>)
      .map((e) => e as Map<String, dynamic>)
      .toList(),
);

Map<String, dynamic> _$LogprobsToJson(_Logprobs instance) => <String, dynamic>{
  'content': instance.content,
};

_ModelInfo _$ModelInfoFromJson(Map<String, dynamic> json) => _ModelInfo(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String?,
  context: (json['context'] as num).toInt(),
  pricing: Pricing.fromJson(json['pricing'] as Map<String, dynamic>),
  topProvider: Provider.fromJson(json['top_provider'] as Map<String, dynamic>),
  perRequestLimits: json['per_request_limits'] == null
      ? null
      : PerRequestLimits.fromJson(
          json['per_request_limits'] as Map<String, dynamic>,
        ),
);

Map<String, dynamic> _$ModelInfoToJson(_ModelInfo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'context': instance.context,
      'pricing': instance.pricing,
      'top_provider': instance.topProvider,
      'per_request_limits': instance.perRequestLimits,
    };

_Pricing _$PricingFromJson(Map<String, dynamic> json) => _Pricing(
  prompt: json['prompt'] as String,
  completion: json['completion'] as String,
  request: json['request'] as String?,
  image: json['image'] as String?,
);

Map<String, dynamic> _$PricingToJson(_Pricing instance) => <String, dynamic>{
  'prompt': instance.prompt,
  'completion': instance.completion,
  'request': instance.request,
  'image': instance.image,
};

_Provider _$ProviderFromJson(Map<String, dynamic> json) => _Provider(
  maxCompletionTokens: (json['max_completion_tokens'] as num?)?.toInt(),
  isModerated: json['is_moderated'] as bool?,
);

Map<String, dynamic> _$ProviderToJson(_Provider instance) => <String, dynamic>{
  'max_completion_tokens': instance.maxCompletionTokens,
  'is_moderated': instance.isModerated,
};

_PerRequestLimits _$PerRequestLimitsFromJson(Map<String, dynamic> json) =>
    _PerRequestLimits(
      promptTokens: (json['prompt_tokens'] as num?)?.toInt(),
      completionTokens: (json['completion_tokens'] as num?)?.toInt(),
    );

Map<String, dynamic> _$PerRequestLimitsToJson(_PerRequestLimits instance) =>
    <String, dynamic>{
      'prompt_tokens': instance.promptTokens,
      'completion_tokens': instance.completionTokens,
    };
