abstract class ApiException implements Exception {
  final String message;
  final int? statusCode;
  final String? details;

  const ApiException({
    required this.message,
    this.statusCode,
    this.details,
  });

  @override
  String toString() => 'ApiException: $message ${statusCode != null ? '($statusCode)' : ''}';
}

class NetworkException extends ApiException {
  const NetworkException({
    required super.message,
    super.details,
  });
}

class AuthenticationException extends ApiException {
  const AuthenticationException({
    required super.message,
    super.statusCode = 401,
    super.details,
  });
}

class RateLimitException extends ApiException {
  final DateTime? resetAt;
  final int? retryAfter;

  const RateLimitException({
    required super.message,
    super.statusCode = 429,
    super.details,
    this.resetAt,
    this.retryAfter,
  });
}

class ServerException extends ApiException {
  const ServerException({
    required super.message,
    super.statusCode,
    super.details,
  });
}

class ValidationException extends ApiException {
  final Map<String, List<String>>? fieldErrors;

  const ValidationException({
    required super.message,
    super.statusCode = 400,
    super.details,
    this.fieldErrors,
  });
}

class QuotaExceededException extends ApiException {
  const QuotaExceededException({
    required super.message,
    super.statusCode = 402,
    super.details,
  });
}

class ModelUnavailableException extends ApiException {
  const ModelUnavailableException({
    required super.message,
    super.statusCode = 503,
    super.details,
  });
}