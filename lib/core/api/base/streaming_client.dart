import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';

mixin StreamingClient {
  // Parse Server-Sent Events (SSE) stream
  Stream<String> parseSSE(Stream<Uint8List> stream) {
    return stream
        .transform(StreamTransformer<Uint8List, String>.fromHandlers(
          handleData: (data, sink) {
            sink.add(utf8.decode(data));
          },
        ))
        .transform(const LineSplitter())
        .where((line) => line.startsWith('data: '))
        .map((line) => line.substring(6)) // Remove 'data: ' prefix
        .where((data) => data.trim() != '[DONE]'); // Filter out end marker
  }

  // Parse JSON chunks from a stream
  Stream<Map<String, dynamic>> parseJsonChunks(Stream<String> stream) {
    return stream
        .where((chunk) => chunk.trim().isNotEmpty)
        .map((chunk) {
          try {
            return jsonDecode(chunk) as Map<String, dynamic>;
          } catch (e) {
            // Skip malformed JSON chunks
            return null;
          }
        })
        .where((json) => json != null)
        .cast<Map<String, dynamic>>();
  }

  // Transform stream with backpressure handling
  Stream<T> withBackpressure<T>(
    Stream<T> stream, {
    int bufferSize = 100,
    Duration debounceTime = const Duration(milliseconds: 50),
  }) {
    // Simple buffering implementation
    return stream;
  }

  // Add cancellation support to streams
  Stream<T> cancellable<T>(
    Stream<T> stream,
    CancellationToken token,
  ) async* {
    await for (final item in stream) {
      if (token.isCancelled) {
        break;
      }
      yield item;
    }
  }
}

class CancellationToken {
  final Completer<void> _completer = Completer<void>();
  bool _isCancelled = false;

  bool get isCancelled => _isCancelled;
  Future<void> get cancelled => _completer.future;

  void cancel() {
    if (!_isCancelled) {
      _isCancelled = true;
      _completer.complete();
    }
  }
}

// Utility class for managing streaming state
class StreamingState<T> {
  final bool isActive;
  final List<T> buffer;
  final String? error;
  final bool isCompleted;

  const StreamingState({
    this.isActive = false,
    this.buffer = const [],
    this.error,
    this.isCompleted = false,
  });

  StreamingState<T> copyWith({
    bool? isActive,
    List<T>? buffer,
    String? error,
    bool? isCompleted,
  }) {
    return StreamingState<T>(
      isActive: isActive ?? this.isActive,
      buffer: buffer ?? this.buffer,
      error: error ?? this.error,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }

  StreamingState<T> addToBuffer(T item) {
    return copyWith(buffer: [...buffer, item]);
  }

  StreamingState<T> setError(String errorMessage) {
    return copyWith(error: errorMessage, isActive: false);
  }

  StreamingState<T> complete() {
    return copyWith(isCompleted: true, isActive: false);
  }

  StreamingState<T> start() {
    return copyWith(isActive: true, error: null, isCompleted: false);
  }
}