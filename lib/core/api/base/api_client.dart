import 'package:dio/dio.dart';
import 'dart:async';
import 'api_exceptions.dart';

abstract class ApiClient {
  final Dio _dio;
  final String baseUrl;

  Dio get dio => _dio;

  ApiClient({
    required this.baseUrl,
    Map<String, String>? defaultHeaders,
    Duration? timeout,
  }) : _dio = Dio() {
    _dio.options.baseUrl = baseUrl;
    _dio.options.connectTimeout = timeout ?? const Duration(seconds: 30);
    _dio.options.receiveTimeout = timeout ?? const Duration(seconds: 30);
    
    if (defaultHeaders != null) {
      _dio.options.headers.addAll(defaultHeaders);
    }

    _setupInterceptors();
  }

  void _setupInterceptors() {
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          onRequest(options);
          handler.next(options);
        },
        onResponse: (response, handler) {
          onResponse(response);
          handler.next(response);
        },
        onError: (error, handler) {
          final apiException = _handleError(error);
          onError(apiException);
          handler.reject(DioException(
            requestOptions: error.requestOptions,
            error: apiException,
          ));
        },
      ),
    );
  }

  ApiException _handleError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return const NetworkException(
          message: 'Connection timeout. Please check your internet connection.',
        );

      case DioExceptionType.connectionError:
        return const NetworkException(
          message: 'Unable to connect to server. Please check your internet connection.',
        );

      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        final data = error.response?.data;
        
        switch (statusCode) {
          case 401:
            return AuthenticationException(
              message: data?['error'] ?? 'Invalid API key or authentication failed.',
              details: data?.toString(),
            );
          
          case 402:
            return QuotaExceededException(
              message: data?['error'] ?? 'API quota exceeded.',
              details: data?.toString(),
            );
          
          case 429:
            final retryAfter = error.response?.headers['retry-after']?.first;
            return RateLimitException(
              message: data?['error'] ?? 'Rate limit exceeded. Please try again later.',
              details: data?.toString(),
              retryAfter: retryAfter != null ? int.tryParse(retryAfter) : null,
            );
          
          case 400:
            return ValidationException(
              message: data?['error'] ?? 'Invalid request parameters.',
              details: data?.toString(),
            );
          
          case 503:
            return ModelUnavailableException(
              message: data?['error'] ?? 'Model is temporarily unavailable.',
              details: data?.toString(),
            );
          
          default:
            return ServerException(
              message: data?['error'] ?? 'Server error occurred.',
              statusCode: statusCode,
              details: data?.toString(),
            );
        }

      case DioExceptionType.cancel:
        return const NetworkException(message: 'Request was cancelled.');

      case DioExceptionType.unknown:
      default:
        return NetworkException(
          message: error.message ?? 'Unknown error occurred.',
          details: error.toString(),
        );
    }
  }

  // Hook methods that can be overridden by subclasses
  void onRequest(RequestOptions options) {}
  void onResponse(Response response) {}
  void onError(ApiException error) {}

  // Protected methods for making HTTP requests
  Future<Response<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    return await _dio.get<T>(
      path,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
    );
  }

  Future<Response<T>> post<T>(
    String path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    return await _dio.post<T>(
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
    );
  }

  // For streaming responses
  Future<ResponseBody> postStream(
    String path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    final response = await _dio.post<ResponseBody>(
      path,
      data: data,
      queryParameters: queryParameters,
      options: (options ?? Options()).copyWith(
        responseType: ResponseType.stream,
      ),
      cancelToken: cancelToken,
    );
    return response.data!;
  }

  void setAuthHeader(String token) {
    _dio.options.headers['Authorization'] = 'Bearer $token';
  }

  void removeAuthHeader() {
    _dio.options.headers.remove('Authorization');
  }

  void close() {
    _dio.close();
  }
}