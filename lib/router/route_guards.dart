import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../features/settings/providers/settings_provider.dart';

class RouteGuards {
  // Check if API key is configured
  static Future<String?> requireApiKey(
    WidgetRef ref,
    GoRouterState state,
  ) async {
    try {
      final settings = await ref.read(settingsProvider.future);
      final hasApiKey = settings['openrouter_api_key'] != null;
      
      if (!hasApiKey && state.uri.path != '/settings/api') {
        return '/settings/api';
      }
    } catch (e) {
      // If settings fail to load, allow navigation
      return null;
    }
    
    return null;
  }

  // Check if initial setup is complete
  static Future<String?> requireSetup(
    WidgetRef ref,
    GoRouterState state,
  ) async {
    try {
      final settings = await ref.read(settingsProvider.future);
      final isSetupComplete = settings['setup_complete'] == true;
      
      if (!isSetupComplete && !state.uri.path.startsWith('/setup')) {
        return '/setup';
      }
    } catch (e) {
      // If settings fail to load, allow navigation
      return null;
    }
    
    return null;
  }

  // Validate conversation exists
  static Future<String?> validateConversation(
    WidgetRef ref,
    GoRouterState state,
    String conversationId,
  ) async {
    // This would check if conversation exists in database
    // For now, just allow all conversations
    return null;
  }

  // Check permissions for MCP operations
  static Future<String?> requireMcpPermissions(
    WidgetRef ref,
    GoRouterState state,
  ) async {
    try {
      final settings = await ref.read(settingsProvider.future);
      final mcpEnabled = settings['mcp_enabled'] == true;
      
      if (!mcpEnabled && state.uri.path.startsWith('/mcp')) {
        return '/settings';
      }
    } catch (e) {
      return null;
    }
    
    return null;
  }
}

// Route guard utility for combining multiple guards
class RouteGuardRunner {
  static Future<String?> runGuards(
    WidgetRef ref,
    GoRouterState state,
    List<Future<String?> Function(WidgetRef, GoRouterState)> guards,
  ) async {
    for (final guard in guards) {
      final redirect = await guard(ref, state);
      if (redirect != null) {
        return redirect;
      }
    }
    return null;
  }
}