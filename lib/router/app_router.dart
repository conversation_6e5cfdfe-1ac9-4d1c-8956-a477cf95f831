import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../features/chat/screens/conversation_list_screen.dart';
import '../features/chat/screens/chat_screen.dart';
import '../features/settings/screens/settings_screen.dart';
import '../features/settings/screens/api_settings_screen.dart';
import '../features/settings/screens/model_parameters_screen.dart';
import '../features/settings/screens/appearance_settings_screen.dart';
import '../features/settings/screens/openrouter_models_screen.dart';
import '../features/mcp_management/screens/mcp_servers_screen.dart';
import '../features/mcp_management/screens/tool_browser_screen.dart';
import '../features/mcp_management/screens/execution_history_screen.dart';
import '../features/models/screens/model_browser_screen.dart';
import '../shared/widgets/adaptive_scaffold.dart';

part 'app_router.g.dart';

final _rootNavigatorKey = GlobalKey<NavigatorState>();
final _shellNavigatorKey = GlobalKey<NavigatorState>();

@riverpod
GoRouter goRouter(Ref ref) {
  return GoRouter(
    navigatorKey: _rootNavigatorKey,
    initialLocation: '/chat',
    routes: [
      ShellRoute(
        navigatorKey: _shellNavigatorKey,
        builder: (context, state, child) {
          return AdaptiveScaffold(currentPath: state.uri.path, child: child);
        },
        routes: [
          // Chat routes
          GoRoute(
            path: '/chat',
            pageBuilder: (context, state) =>
                const NoTransitionPage(child: ConversationListScreen()),
            routes: [
              GoRoute(
                path: '/:id',
                pageBuilder: (context, state) {
                  final conversationId = state.pathParameters['id']!;
                  return NoTransitionPage(
                    child: ChatScreen(conversationId: conversationId),
                  );
                },
              ),
            ],
          ),

          // Tools routes
          GoRoute(
            path: '/tools',
            pageBuilder: (context, state) =>
                const NoTransitionPage(child: ToolBrowserScreen()),
            routes: [
              GoRoute(
                path: '/:serverId/:toolName',
                pageBuilder: (context, state) {
                  final serverId = state.pathParameters['serverId']!;
                  final toolName = state.pathParameters['toolName']!;
                  return NoTransitionPage(
                    child: ToolDetailScreen(
                      serverId: serverId,
                      toolName: toolName,
                    ),
                  );
                },
              ),
            ],
          ),

          // MCP routes
          GoRoute(
            path: '/mcp',
            pageBuilder: (context, state) =>
                const NoTransitionPage(child: McpServersScreen()),
            routes: [
              GoRoute(
                path: '/:serverId',
                pageBuilder: (context, state) {
                  final serverId = state.pathParameters['serverId']!;
                  return NoTransitionPage(
                    child: McpServerDetailScreen(serverId: serverId),
                  );
                },
              ),
              GoRoute(
                path: '/executions',
                pageBuilder: (context, state) =>
                    const NoTransitionPage(child: ExecutionHistoryScreen()),
              ),
            ],
          ),

          // Models route
          GoRoute(
            path: '/models',
            pageBuilder: (context, state) =>
                const NoTransitionPage(child: ModelBrowserScreen()),
          ),

          // Settings routes
          GoRoute(
            path: '/settings',
            pageBuilder: (context, state) =>
                const NoTransitionPage(child: SettingsScreen()),
            routes: [
              GoRoute(
                path: '/api',
                pageBuilder: (context, state) =>
                    const NoTransitionPage(child: ApiSettingsScreen()),
              ),
              GoRoute(
                path: '/models',
                pageBuilder: (context, state) =>
                    const NoTransitionPage(child: ModelParametersScreen()),
              ),
              GoRoute(
                path: '/appearance',
                pageBuilder: (context, state) =>
                    const NoTransitionPage(child: AppearanceSettingsScreen()),
              ),
              GoRoute(
                path: '/openrouter-models',
                pageBuilder: (context, state) =>
                    const NoTransitionPage(child: OpenRouterModelsScreen()),
              ),
            ],
          ),
        ],
      ),
    ],

    redirect: (context, state) {
      // Redirect root to chat
      if (state.uri.path == '/') {
        return '/chat';
      }
      return null;
    },

    errorPageBuilder: (context, state) => MaterialPage(
      child: Scaffold(
        appBar: AppBar(title: const Text('Error')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64),
              const SizedBox(height: 16),
              Text(
                'Page not found',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(state.uri.path),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => context.go('/chat'),
                child: const Text('Go to Chat'),
              ),
            ],
          ),
        ),
      ),
    ),
  );
}

// Helper classes for specific screens that don't exist yet
class ToolDetailScreen extends StatelessWidget {
  final String serverId;
  final String toolName;

  const ToolDetailScreen({
    super.key,
    required this.serverId,
    required this.toolName,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Tool: $toolName'),
        actions: [
          IconButton(
            icon: const Icon(Icons.play_arrow),
            onPressed: () => _executeTool(context),
            tooltip: 'Execute Tool',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoCard(),
            const SizedBox(height: 16),
            _buildParametersCard(),
            const SizedBox(height: 16),
            _buildExecutionCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.info_outline, size: 20),
                SizedBox(width: 8),
                Text(
                  'Tool Information',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Name', toolName),
            _buildInfoRow('Server', serverId),
            _buildInfoRow('Type', 'MCP Tool'),
            _buildInfoRow('Status', 'Available'),
            const SizedBox(height: 12),
            const Text(
              'Description',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 4),
            const Text(
              'This tool provides functionality through the Model Context Protocol. '
              'Execute the tool with parameters to see results.',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildParametersCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.tune, size: 20),
                SizedBox(width: 8),
                Text(
                  'Parameters',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: const Text(
                '{\n  "input": "string",\n  "options": "object"\n}',
                style: TextStyle(fontFamily: 'monospace'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExecutionCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.history, size: 20),
                SizedBox(width: 8),
                Text(
                  'Recent Executions',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'No recent executions found.',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 12),
            Builder(
              builder: (context) => ElevatedButton.icon(
                onPressed: () => _executeTool(context),
                icon: const Icon(Icons.play_arrow),
                label: const Text('Test Tool'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _executeTool(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Execute $toolName'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: InputDecoration(
                labelText: 'Input Parameters (JSON)',
                hintText: '{"key": "value"}',
                border: OutlineInputBorder(),
              ),
              maxLines: 4,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Executed tool: $toolName')),
              );
            },
            child: const Text('Execute'),
          ),
        ],
      ),
    );
  }
}

class McpServerDetailScreen extends StatelessWidget {
  final String serverId;

  const McpServerDetailScreen({super.key, required this.serverId});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Server: $serverId'),
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'restart':
                  _restartServer(context);
                  break;
                case 'configure':
                  _configureServer(context);
                  break;
                case 'remove':
                  _removeServer(context);
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'restart',
                child: Row(
                  children: [
                    Icon(Icons.refresh, size: 18),
                    SizedBox(width: 8),
                    Text('Restart Server'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'configure',
                child: Row(
                  children: [
                    Icon(Icons.settings, size: 18),
                    SizedBox(width: 8),
                    Text('Configure'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'remove',
                child: Row(
                  children: [
                    Icon(Icons.delete, size: 18, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Remove Server', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStatusCard(),
            const SizedBox(height: 16),
            _buildConfigCard(),
            const SizedBox(height: 16),
            _buildToolsCard(),
            const SizedBox(height: 16),
            _buildStatsCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.hub, size: 20),
                SizedBox(width: 8),
                Text(
                  'Server Status',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.green),
                  ),
                  child: const Text(
                    'Connected',
                    style: TextStyle(
                      color: Colors.green,
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                    ),
                  ),
                ),
                const Spacer(),
                const Text('Uptime: 2h 34m'),
              ],
            ),
            const SizedBox(height: 12),
            _buildInfoRow('Server ID', serverId),
            _buildInfoRow('Protocol', 'MCP v1.0'),
            _buildInfoRow('Transport', 'WebSocket'),
            _buildInfoRow('Last Ping', '2 seconds ago'),
          ],
        ),
      ),
    );
  }

  Widget _buildConfigCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.settings, size: 20),
                SizedBox(width: 8),
                Text(
                  'Configuration',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: const Text(
                '{\n  "name": "example-server",\n  "command": "python",\n  "args": ["server.py"],\n  "env": {\n    "API_KEY": "***"\n  }\n}',
                style: TextStyle(fontFamily: 'monospace'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildToolsCard() {
    final tools = ['file-read', 'web-search', 'database-query', 'api-call'];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.extension, size: 20),
                const SizedBox(width: 8),
                const Text(
                  'Available Tools',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                Text('${tools.length} tools'),
              ],
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: tools
                  .map(
                    (tool) => Chip(
                      label: Text(tool),
                      onDeleted: () => _disableTool(tool),
                      deleteIcon: const Icon(Icons.block, size: 16),
                    ),
                  )
                  .toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.analytics, size: 20),
                SizedBox(width: 8),
                Text(
                  'Statistics',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(child: _buildStatItem('Total Requests', '1,234')),
                Expanded(child: _buildStatItem('Successful', '1,189')),
                Expanded(child: _buildStatItem('Failed', '45')),
                Expanded(child: _buildStatItem('Avg Response', '245ms')),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        Text(label, style: const TextStyle(fontSize: 12, color: Colors.grey)),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _restartServer(BuildContext context) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('Restarting server: $serverId')));
  }

  void _configureServer(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Server configuration opened')),
    );
  }

  void _removeServer(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Remove Server'),
        content: Text('Are you sure you want to remove server "$serverId"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context); // Go back to servers list
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Removed server: $serverId')),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Remove'),
          ),
        ],
      ),
    );
  }

  void _disableTool(String tool) {
    // Mock disable tool functionality
  }
}
