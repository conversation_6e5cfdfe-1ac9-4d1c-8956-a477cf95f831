import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class AdaptiveScaffold extends StatelessWidget {
  final Widget child;
  final String currentPath;

  const AdaptiveScaffold({
    super.key,
    required this.child,
    required this.currentPath,
  });

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final isDesktop = mediaQuery.size.width >= 1200;
    final isTablet = mediaQuery.size.width >= 600 && mediaQuery.size.width < 1200;
    final isMobile = mediaQuery.size.width < 600;

    if (isDesktop) {
      return _DesktopLayout(currentPath: currentPath, child: child);
    } else if (isTablet) {
      return _TabletLayout(currentPath: currentPath, child: child);
    } else {
      return _MobileLayout(currentPath: currentPath, child: child);
    }
  }
}

class _DesktopLayout extends StatelessWidget {
  final Widget child;
  final String currentPath;

  const _DesktopLayout({
    required this.child,
    required this.currentPath,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Row(
        children: [
          NavigationRail(
            extended: true,
            destinations: _buildNavigationDestinations(),
            selectedIndex: _getSelectedIndex(),
            onDestinationSelected: (index) => _onDestinationSelected(context, index),
            leading: _buildLogo(),
            trailing: _buildUserProfile(context),
          ),
          const VerticalDivider(thickness: 1, width: 1),
          Expanded(child: child),
        ],
      ),
    );
  }

  Widget _buildLogo() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: const Row(
        children: [
          Icon(Icons.chat_bubble_outline, size: 24, color: Colors.indigo),
          SizedBox(width: 8),
          Text('Otlo', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  Widget _buildUserProfile(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: IconButton(
        icon: const Icon(Icons.person_outline),
        onPressed: () => context.go('/settings'),
        tooltip: 'Settings',
      ),
    );
  }

  int _getSelectedIndex() {
    if (currentPath.startsWith('/chat')) return 0;
    if (currentPath.startsWith('/tools')) return 1;
    if (currentPath.startsWith('/mcp')) return 2;
    if (currentPath.startsWith('/settings')) return 3;
    return 0;
  }

  void _onDestinationSelected(BuildContext context, int index) {
    switch (index) {
      case 0:
        context.go('/chat');
        break;
      case 1:
        context.go('/tools');
        break;
      case 2:
        context.go('/mcp');
        break;
      case 3:
        context.go('/settings');
        break;
    }
  }
}

class _TabletLayout extends StatelessWidget {
  final Widget child;
  final String currentPath;

  const _TabletLayout({
    required this.child,
    required this.currentPath,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Row(
        children: [
          NavigationRail(
            destinations: _buildNavigationDestinations(),
            selectedIndex: _getSelectedIndex(),
            onDestinationSelected: (index) => _onDestinationSelected(context, index),
            leading: _buildLogo(),
          ),
          const VerticalDivider(thickness: 1, width: 1),
          Expanded(child: child),
        ],
      ),
    );
  }

  Widget _buildLogo() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: const Icon(Icons.chat_bubble_outline, size: 24, color: Colors.indigo),
    );
  }

  int _getSelectedIndex() {
    if (currentPath.startsWith('/chat')) return 0;
    if (currentPath.startsWith('/tools')) return 1;
    if (currentPath.startsWith('/mcp')) return 2;
    if (currentPath.startsWith('/settings')) return 3;
    return 0;
  }

  void _onDestinationSelected(BuildContext context, int index) {
    switch (index) {
      case 0:
        context.go('/chat');
        break;
      case 1:
        context.go('/tools');
        break;
      case 2:
        context.go('/mcp');
        break;
      case 3:
        context.go('/settings');
        break;
    }
  }
}

class _MobileLayout extends StatelessWidget {
  final Widget child;
  final String currentPath;

  const _MobileLayout({
    required this.child,
    required this.currentPath,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: child,
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _getSelectedIndex(),
        onTap: (index) => _onDestinationSelected(context, index),
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.chat_outlined),
            activeIcon: Icon(Icons.chat),
            label: 'Chats',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.extension_outlined),
            activeIcon: Icon(Icons.extension),
            label: 'Tools',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.hub_outlined),
            activeIcon: Icon(Icons.hub),
            label: 'MCP',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings_outlined),
            activeIcon: Icon(Icons.settings),
            label: 'Settings',
          ),
        ],
      ),
    );
  }

  int _getSelectedIndex() {
    if (currentPath.startsWith('/chat')) return 0;
    if (currentPath.startsWith('/tools')) return 1;
    if (currentPath.startsWith('/mcp')) return 2;
    if (currentPath.startsWith('/settings')) return 3;
    return 0;
  }

  void _onDestinationSelected(BuildContext context, int index) {
    switch (index) {
      case 0:
        context.go('/chat');
        break;
      case 1:
        context.go('/tools');
        break;
      case 2:
        context.go('/mcp');
        break;
      case 3:
        context.go('/settings');
        break;
    }
  }
}

List<NavigationRailDestination> _buildNavigationDestinations() {
  return const [
    NavigationRailDestination(
      icon: Icon(Icons.chat_outlined),
      selectedIcon: Icon(Icons.chat),
      label: Text('Chats'),
    ),
    NavigationRailDestination(
      icon: Icon(Icons.extension_outlined),
      selectedIcon: Icon(Icons.extension),
      label: Text('Tools'),
    ),
    NavigationRailDestination(
      icon: Icon(Icons.hub_outlined),
      selectedIcon: Icon(Icons.hub),
      label: Text('MCP'),
    ),
    NavigationRailDestination(
      icon: Icon(Icons.settings_outlined),
      selectedIcon: Icon(Icons.settings),
      label: Text('Settings'),
    ),
  ];
}