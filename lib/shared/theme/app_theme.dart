import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'app_colors.dart';
import 'text_styles.dart';

/// Application theme configuration for Material 3 design system.
///
/// Provides comprehensive theming for both light and dark modes including:
/// - Color schemes with proper contrast ratios
/// - Typography using custom text styles
/// - Component themes (AppBar, Cards, Buttons, Inputs, etc.)
/// - Navigation themes (BottomNav, NavigationRail)
/// - Interactive element themes (Dialogs, Popups, Chips)
/// 
/// The themes are built using Material 3 principles with custom colors
/// from AppColors and typography from AppTextStyles.
class AppTheme {
  static ThemeData lightTheme = ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: AppColors.primary,
      brightness: Brightness.light,
      surface: AppColors.surfaceLight,
      background: AppColors.backgroundLight,
      onSurface: AppColors.onSurfaceLight,
      onSurfaceVariant: AppColors.onSurfaceVariantLight,
      primary: AppColors.primary,
      onPrimary: Colors.white,
      secondary: AppColors.secondary,
      onSecondary: Colors.white,
    ),
    scaffoldBackgroundColor: AppColors.backgroundLight,
    fontFamily: AppTextStyles.fontFamily,
    textTheme: _buildTextTheme(Brightness.light),
    appBarTheme: _buildAppBarTheme(Brightness.light),
    cardTheme: _buildCardTheme(Brightness.light),
    elevatedButtonTheme: _buildElevatedButtonTheme(Brightness.light),
    outlinedButtonTheme: _buildOutlinedButtonTheme(Brightness.light),
    textButtonTheme: _buildTextButtonTheme(Brightness.light),
    inputDecorationTheme: _buildInputDecorationTheme(Brightness.light),
    navigationRailTheme: _buildNavigationRailTheme(Brightness.light),
    bottomNavigationBarTheme: _buildBottomNavigationBarTheme(Brightness.light),
    chipTheme: _buildChipTheme(Brightness.light),
    dividerTheme: _buildDividerTheme(Brightness.light),
    popupMenuTheme: _buildPopupMenuTheme(Brightness.light),
    dialogTheme: _buildDialogTheme(Brightness.light),
    snackBarTheme: _buildSnackBarTheme(Brightness.light),
    floatingActionButtonTheme: _buildFloatingActionButtonTheme(Brightness.light),
    listTileTheme: _buildListTileTheme(Brightness.light),
  );

  static ThemeData darkTheme = ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: AppColors.primary,
      brightness: Brightness.dark,
      surface: AppColors.surfaceDark,
      background: AppColors.backgroundDark,
      onSurface: AppColors.onSurfaceDark,
      onSurfaceVariant: AppColors.onSurfaceVariantDark,
      primary: AppColors.primary,
      onPrimary: Colors.white,
      secondary: AppColors.secondary,
      onSecondary: Colors.white,
    ),
    scaffoldBackgroundColor: AppColors.backgroundDark,
    fontFamily: AppTextStyles.fontFamily,
    textTheme: _buildTextTheme(Brightness.dark),
    appBarTheme: _buildAppBarTheme(Brightness.dark),
    cardTheme: _buildCardTheme(Brightness.dark),
    elevatedButtonTheme: _buildElevatedButtonTheme(Brightness.dark),
    outlinedButtonTheme: _buildOutlinedButtonTheme(Brightness.dark),
    textButtonTheme: _buildTextButtonTheme(Brightness.dark),
    inputDecorationTheme: _buildInputDecorationTheme(Brightness.dark),
    navigationRailTheme: _buildNavigationRailTheme(Brightness.dark),
    bottomNavigationBarTheme: _buildBottomNavigationBarTheme(Brightness.dark),
    chipTheme: _buildChipTheme(Brightness.dark),
    dividerTheme: _buildDividerTheme(Brightness.dark),
    popupMenuTheme: _buildPopupMenuTheme(Brightness.dark),
    dialogTheme: _buildDialogTheme(Brightness.dark),
    snackBarTheme: _buildSnackBarTheme(Brightness.dark),
    floatingActionButtonTheme: _buildFloatingActionButtonTheme(Brightness.dark),
    listTileTheme: _buildListTileTheme(Brightness.dark),
  );

  /// Builds the text theme using custom AppTextStyles.
  static TextTheme _buildTextTheme(Brightness brightness) {
    return const TextTheme(
      headlineLarge: AppTextStyles.h1,
      headlineMedium: AppTextStyles.h2,
      headlineSmall: AppTextStyles.h3,
      titleLarge: AppTextStyles.h4,
      titleMedium: AppTextStyles.h5,
      titleSmall: AppTextStyles.h6,
      bodyLarge: AppTextStyles.bodyLarge,
      bodyMedium: AppTextStyles.bodyMedium,
      bodySmall: AppTextStyles.bodySmall,
      labelLarge: AppTextStyles.labelLarge,
      labelMedium: AppTextStyles.labelMedium,
      labelSmall: AppTextStyles.labelSmall,
    );
  }

  /// Builds app bar theme with flat design and proper system overlay styling.
  static AppBarTheme _buildAppBarTheme(Brightness brightness) {
    return AppBarTheme(
      elevation: 0,
      scrolledUnderElevation: 0,
      backgroundColor: brightness == Brightness.light
          ? AppColors.surfaceLight
          : AppColors.backgroundDark,
      surfaceTintColor: Colors.transparent,
      foregroundColor: brightness == Brightness.light
          ? AppColors.onSurfaceLight
          : AppColors.onSurfaceDark,
      titleTextStyle: AppTextStyles.h5.copyWith(
        color: brightness == Brightness.light
            ? AppColors.onSurfaceLight
            : AppColors.onSurfaceDark,
        fontWeight: FontWeight.w600,
      ),
      iconTheme: IconThemeData(
        color: brightness == Brightness.light
            ? AppColors.onSurfaceLight
            : AppColors.onSurfaceDark,
      ),
      systemOverlayStyle: brightness == Brightness.light
          ? SystemUiOverlayStyle.dark
          : SystemUiOverlayStyle.light,
    );
  }

  static CardThemeData _buildCardTheme(Brightness brightness) {
    return CardThemeData(
      elevation: 0,
      margin: const EdgeInsets.all(8),
      color: brightness == Brightness.light
          ? AppColors.surfaceLight
          : AppColors.surfaceDark,
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: brightness == Brightness.light
              ? AppColors.borderLight
              : AppColors.borderDark,
          width: brightness == Brightness.light ? 0.5 : 1,
        ),
      ),
    );
  }

  /// Builds elevated button theme with rounded corners and no shadow.
  static ElevatedButtonThemeData _buildElevatedButtonTheme(Brightness brightness) {
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        textStyle: AppTextStyles.button.copyWith(
          fontWeight: FontWeight.w600,
        ),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        elevation: 0,
        shadowColor: Colors.transparent,
      ),
    );
  }

  static OutlinedButtonThemeData _buildOutlinedButtonTheme(Brightness brightness) {
    return OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        textStyle: AppTextStyles.button,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  static TextButtonThemeData _buildTextButtonTheme(Brightness brightness) {
    return TextButtonThemeData(
      style: TextButton.styleFrom(
        textStyle: AppTextStyles.button,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(6),
        ),
      ),
    );
  }

  /// Builds input decoration theme with filled style and rounded borders.
  static InputDecorationTheme _buildInputDecorationTheme(Brightness brightness) {
    return InputDecorationTheme(
      filled: true,
      fillColor: brightness == Brightness.light
          ? AppColors.surfaceVariantLight
          : AppColors.surfaceVariantDark,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: brightness == Brightness.light
              ? AppColors.borderLight
              : AppColors.borderDark,
        ),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: brightness == Brightness.light
              ? AppColors.borderLight
              : AppColors.borderDark,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(
          color: AppColors.primary,
          width: 2,
        ),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      hintStyle: TextStyle(
        color: brightness == Brightness.light
            ? AppColors.onSurfaceVariantLight
            : AppColors.onSurfaceVariantDark,
        fontSize: 16,
      ),
    );
  }

  static NavigationRailThemeData _buildNavigationRailTheme(Brightness brightness) {
    return NavigationRailThemeData(
      backgroundColor: brightness == Brightness.light
          ? AppColors.surfaceLight
          : AppColors.surfaceDark,
      selectedIconTheme: const IconThemeData(
        color: AppColors.primary,
        size: 24,
      ),
      unselectedIconTheme: IconThemeData(
        color: brightness == Brightness.light
            ? AppColors.onSurfaceVariantLight
            : AppColors.onSurfaceVariantDark,
        size: 24,
      ),
      selectedLabelTextStyle: AppTextStyles.navItemActive.copyWith(
        color: AppColors.primary,
      ),
      unselectedLabelTextStyle: AppTextStyles.navItem.copyWith(
        color: brightness == Brightness.light
            ? AppColors.onSurfaceVariantLight
            : AppColors.onSurfaceVariantDark,
      ),
    );
  }

  static BottomNavigationBarThemeData _buildBottomNavigationBarTheme(Brightness brightness) {
    return BottomNavigationBarThemeData(
      backgroundColor: brightness == Brightness.light
          ? AppColors.surfaceLight
          : AppColors.surfaceDark,
      selectedItemColor: AppColors.primary,
      unselectedItemColor: brightness == Brightness.light
          ? AppColors.onSurfaceVariantLight
          : AppColors.onSurfaceVariantDark,
      selectedLabelStyle: AppTextStyles.navItemActive.copyWith(
        fontWeight: FontWeight.w600,
      ),
      unselectedLabelStyle: AppTextStyles.navItem,
      type: BottomNavigationBarType.fixed,
      elevation: 0,
    );
  }

  static ChipThemeData _buildChipTheme(Brightness brightness) {
    return ChipThemeData(
      backgroundColor: brightness == Brightness.light
          ? AppColors.surfaceVariantLight
          : AppColors.surfaceVariantDark,
      labelStyle: AppTextStyles.labelMedium,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
    );
  }

  static DividerThemeData _buildDividerTheme(Brightness brightness) {
    return DividerThemeData(
      color: brightness == Brightness.light
          ? AppColors.borderLight
          : AppColors.borderDark,
      thickness: 1,
      space: 1,
    );
  }

  static PopupMenuThemeData _buildPopupMenuTheme(Brightness brightness) {
    return PopupMenuThemeData(
      color: brightness == Brightness.light
          ? AppColors.surfaceLight
          : AppColors.surfaceDark,
      textStyle: AppTextStyles.bodyMedium,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
    );
  }

  static DialogThemeData _buildDialogTheme(Brightness brightness) {
    return DialogThemeData(
      backgroundColor: brightness == Brightness.light
          ? AppColors.surfaceLight
          : AppColors.surfaceDark,
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      titleTextStyle: AppTextStyles.h5.copyWith(
        fontWeight: FontWeight.w600,
        color: brightness == Brightness.light
            ? AppColors.onSurfaceLight
            : AppColors.onSurfaceDark,
      ),
      contentTextStyle: AppTextStyles.bodyMedium.copyWith(
        color: brightness == Brightness.light
            ? AppColors.onSurfaceLight
            : AppColors.onSurfaceDark,
      ),
    );
  }

  static SnackBarThemeData _buildSnackBarTheme(Brightness brightness) {
    return SnackBarThemeData(
      backgroundColor: brightness == Brightness.light
          ? AppColors.onSurfaceLight
          : AppColors.onSurfaceDark,
      contentTextStyle: AppTextStyles.bodyMedium.copyWith(
        color: brightness == Brightness.light
            ? AppColors.surfaceLight
            : AppColors.surfaceDark,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      behavior: SnackBarBehavior.floating,
    );
  }

  /// Builds floating action button theme with flat design.
  static FloatingActionButtonThemeData _buildFloatingActionButtonTheme(Brightness brightness) {
    return FloatingActionButtonThemeData(
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      elevation: 0,
      focusElevation: 0,
      hoverElevation: 0,
      highlightElevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
    );
  }

  /// Builds list tile theme with proper padding and selection styling.
  static ListTileThemeData _buildListTileTheme(Brightness brightness) {
    return ListTileThemeData(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      selectedTileColor: brightness == Brightness.light
          ? AppColors.primary.withOpacity(0.1)
          : AppColors.primary.withOpacity(0.2),
      textColor: brightness == Brightness.light
          ? AppColors.onSurfaceLight
          : AppColors.onSurfaceDark,
      iconColor: brightness == Brightness.light
          ? AppColors.onSurfaceVariantLight
          : AppColors.onSurfaceVariantDark,
      selectedColor: AppColors.primary,
    );
  }
}