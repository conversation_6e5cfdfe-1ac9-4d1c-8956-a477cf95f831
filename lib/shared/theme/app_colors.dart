import 'package:flutter/material.dart';

/// Application color palette and theme-based color utilities.
/// 
/// Defines all colors used throughout the Otlo application including:
/// - Brand colors (primary, secondary)
/// - Semantic colors (success, warning, error, info)  
/// - Message bubble colors for different message types
/// - Surface and background colors for light/dark themes
/// - Text colors with proper contrast ratios
/// - UI element colors (borders, status indicators, syntax highlighting)
class AppColors {
  // Primary brand colors
  static const Color primary = Color(0xFF007AFF);
  static const Color primaryVariant = Color(0xFF0056CC);
  static const Color secondary = Color(0xFF00D4AA);
  static const Color secondaryVariant = Color(0xFF00B894);

  // Semantic colors
  static const Color success = Color(0xFF10B981);
  static const Color warning = Color(0xFFF59E0B);
  static const Color error = Color(0xFFEF4444);
  static const Color info = Color(0xFF3B82F6);

  // Message bubble colors
  static const Color userMessageLight = Color(0xFF007AFF);
  static const Color userMessageDark = Color(0xFF007AFF);
  static const Color assistantMessageLight = Color(0xFFF2F2F7);
  static const Color assistantMessageDark = Color(0xFF2C2C2E);
  static const Color systemMessageLight = Color(0xFFE5E5EA);
  static const Color systemMessageDark = Color(0xFF3A3A3C);
  static const Color toolMessageLight = Color(0xFFF0F9FF);
  static const Color toolMessageDark = Color(0xFF1E3A8A);

  // Surface colors
  static const Color surfaceLight = Color(0xFFFFFFFF);
  static const Color surfaceDark = Color(0xFF1C1C1E);
  static const Color surfaceVariantLight = Color(0xFFF2F2F7);
  static const Color surfaceVariantDark = Color(0xFF2C2C2E);

  // Background colors
  static const Color backgroundLight = Color(0xFFFFFFFF);
  static const Color backgroundDark = Color(0xFF000000);

  // Text colors
  static const Color onSurfaceLight = Color(0xFF1C1C1E);
  static const Color onSurfaceDark = Color(0xFFFFFFFF);
  static const Color onSurfaceVariantLight = Color(0xFF8E8E93);
  static const Color onSurfaceVariantDark = Color(0xFF8E8E93);

  // Border colors
  static const Color borderLight = Color(0xFFE5E5EA);
  static const Color borderDark = Color(0xFF38383A);

  // Syntax highlighting colors for code
  static const Color codeKeyword = Color(0xFF8B5CF6);
  static const Color codeString = Color(0xFF10B981);
  static const Color codeComment = Color(0xFF6B7280);
  static const Color codeNumber = Color(0xFFF59E0B);
  static const Color codeFunction = Color(0xFF3B82F6);

  // Status indicator colors
  static const Color statusOnline = Color(0xFF10B981);
  static const Color statusOffline = Color(0xFF6B7280);
  static const Color statusError = Color(0xFFEF4444);
  static const Color statusWarning = Color(0xFFF59E0B);

  // Token count badge colors
  static const Color tokenLow = Color(0xFF10B981);
  static const Color tokenMedium = Color(0xFFF59E0B);
  static const Color tokenHigh = Color(0xFFEF4444);

  // Streaming indicator colors
  static const Color streamingIndicator = Color(0xFF6366F1);
  static const Color streamingPulse = Color(0xFF8B5CF6);
}

extension AppColorsExtension on ColorScheme {
  // Message bubble colors based on theme
  Color get userMessage => brightness == Brightness.light 
      ? AppColors.userMessageLight 
      : AppColors.userMessageDark;
  
  Color get assistantMessage => brightness == Brightness.light 
      ? AppColors.assistantMessageLight 
      : AppColors.assistantMessageDark;
  
  Color get systemMessage => brightness == Brightness.light 
      ? AppColors.systemMessageLight 
      : AppColors.systemMessageDark;
  
  Color get toolMessage => brightness == Brightness.light 
      ? AppColors.toolMessageLight 
      : AppColors.toolMessageDark;

  // Border color based on theme
  Color get border => brightness == Brightness.light 
      ? AppColors.borderLight 
      : AppColors.borderDark;

  // Token count color based on percentage
  Color tokenCountColor(double percentage) {
    if (percentage < 0.6) return AppColors.tokenLow;
    if (percentage < 0.8) return AppColors.tokenMedium;
    return AppColors.tokenHigh;
  }

  // Status colors
  Color get statusOnline => AppColors.statusOnline;
  Color get statusOffline => AppColors.statusOffline;
  Color get statusError => AppColors.statusError;
  Color get statusWarning => AppColors.statusWarning;
}