import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Mock data for demonstration
class ToolInfo {
  final String name;
  final String description;
  final String serverName;
  final List<ToolParameter> parameters;
  final bool isEnabled;
  final String? category;

  ToolInfo({
    required this.name,
    required this.description,
    required this.serverName,
    required this.parameters,
    this.isEnabled = true,
    this.category,
  });
}

class ToolParameter {
  final String name;
  final String type;
  final bool required;
  final String? description;

  ToolParameter({
    required this.name,
    required this.type,
    required this.required,
    this.description,
  });
}

class ToolBrowserScreen extends ConsumerStatefulWidget {
  const ToolBrowserScreen({super.key});

  @override
  ConsumerState<ToolBrowserScreen> createState() => _ToolBrowserScreenState();
}

class _ToolBrowserScreenState extends ConsumerState<ToolBrowserScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String? _selectedCategory;

  final List<ToolInfo> _tools = [
    ToolInfo(
      name: 'read_file',
      description: 'Read the contents of a file from the file system',
      serverName: 'File System Server',
      category: 'File Operations',
      parameters: [
        ToolParameter(
          name: 'path',
          type: 'string',
          required: true,
          description: 'The path to the file to read',
        ),
      ],
    ),
    ToolInfo(
      name: 'write_file',
      description: 'Write content to a file on the file system',
      serverName: 'File System Server',
      category: 'File Operations',
      parameters: [
        ToolParameter(
          name: 'path',
          type: 'string',
          required: true,
          description: 'The path where to write the file',
        ),
        ToolParameter(
          name: 'content',
          type: 'string',
          required: true,
          description: 'The content to write to the file',
        ),
      ],
    ),
    ToolInfo(
      name: 'list_directory',
      description: 'List the contents of a directory',
      serverName: 'File System Server',
      category: 'File Operations',
      parameters: [
        ToolParameter(
          name: 'path',
          type: 'string',
          required: true,
          description: 'The directory path to list',
        ),
      ],
    ),
    ToolInfo(
      name: 'web_search',
      description: 'Search the web for information',
      serverName: 'Web Search Server',
      category: 'Web',
      isEnabled: false,
      parameters: [
        ToolParameter(
          name: 'query',
          type: 'string',
          required: true,
          description: 'The search query',
        ),
        ToolParameter(
          name: 'limit',
          type: 'integer',
          required: false,
          description: 'Maximum number of results',
        ),
      ],
    ),
    ToolInfo(
      name: 'scrape_webpage',
      description: 'Extract content from a webpage',
      serverName: 'Web Search Server',
      category: 'Web',
      isEnabled: false,
      parameters: [
        ToolParameter(
          name: 'url',
          type: 'string',
          required: true,
          description: 'The URL to scrape',
        ),
      ],
    ),
  ];

  List<String> get _categories {
    return _tools.map((tool) => tool.category).whereType<String>().toSet().toList()..sort();
  }

  List<ToolInfo> get _filteredTools {
    return _tools.where((tool) {
      final matchesSearch = _searchQuery.isEmpty ||
          tool.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          tool.description.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          tool.serverName.toLowerCase().contains(_searchQuery.toLowerCase());
      
      final matchesCategory = _selectedCategory == null || tool.category == _selectedCategory;
      
      return matchesSearch && matchesCategory;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final filteredTools = _filteredTools;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Tool Browser'),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(120),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: TextField(
                  controller: _searchController,
                  decoration: const InputDecoration(
                    hintText: 'Search tools...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
              ),
              SizedBox(
                height: 40,
                child: ListView(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  children: [
                    FilterChip(
                      label: const Text('All'),
                      selected: _selectedCategory == null,
                      onSelected: (selected) {
                        setState(() {
                          _selectedCategory = selected ? null : _selectedCategory;
                        });
                      },
                    ),
                    const SizedBox(width: 8),
                    ..._categories.map((category) => Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: FilterChip(
                            label: Text(category),
                            selected: _selectedCategory == category,
                            onSelected: (selected) {
                              setState(() {
                                _selectedCategory = selected ? category : null;
                              });
                            },
                          ),
                        )),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
      body: filteredTools.isEmpty
          ? _buildEmptyState()
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: filteredTools.length,
              itemBuilder: (context, index) {
                final tool = filteredTools[index];
                return _buildToolCard(tool);
              },
            ),
    );
  }

  Widget _buildEmptyState() {
    if (_searchQuery.isEmpty && _selectedCategory == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.build_circle_outlined,
              size: 64,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 16),
            Text(
              'No Tools Available',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'Connect to MCP servers to discover available tools',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      );
    } else {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 16),
            Text(
              'No Matching Tools',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'Try adjusting your search or filter criteria',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildToolCard(ToolInfo tool) {
    final theme = Theme.of(context);
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.extension,
                  color: tool.isEnabled ? theme.colorScheme.primary : theme.colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        tool.name,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: tool.isEnabled ? null : theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                      Text(
                        'from ${tool.serverName}',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
                if (!tool.isEnabled)
                  Chip(
                    label: const Text('Disabled'),
                    backgroundColor: theme.colorScheme.errorContainer,
                    labelStyle: TextStyle(
                      color: theme.colorScheme.onErrorContainer,
                      fontSize: 11,
                    ),
                  ),
                PopupMenuButton<String>(
                  onSelected: (value) => _handleToolAction(tool, value),
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: 'test',
                      enabled: tool.isEnabled,
                      child: const Row(
                        children: [
                          Icon(Icons.play_arrow),
                          SizedBox(width: 8),
                          Text('Test Tool'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'details',
                      child: Row(
                        children: [
                          Icon(Icons.info),
                          SizedBox(width: 8),
                          Text('View Details'),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              tool.description,
              style: theme.textTheme.bodyMedium,
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 4,
              children: [
                if (tool.category != null)
                  Chip(
                    label: Text(tool.category!),
                    backgroundColor: theme.colorScheme.secondaryContainer,
                    labelStyle: TextStyle(
                      color: theme.colorScheme.onSecondaryContainer,
                      fontSize: 11,
                    ),
                  ),
                Chip(
                  label: Text('${tool.parameters.length} params'),
                  backgroundColor: theme.colorScheme.tertiaryContainer,
                  labelStyle: TextStyle(
                    color: theme.colorScheme.onTertiaryContainer,
                    fontSize: 11,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _handleToolAction(ToolInfo tool, String action) {
    switch (action) {
      case 'test':
        _showTestToolDialog(tool);
        break;
      case 'details':
        _showToolDetailsDialog(tool);
        break;
    }
  }

  void _showTestToolDialog(ToolInfo tool) {
    showDialog(
      context: context,
      builder: (context) => _TestToolDialog(tool: tool),
    );
  }

  void _showToolDetailsDialog(ToolInfo tool) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(tool.name),
        content: SizedBox(
          width: 500,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailRow('Description', tool.description),
              _buildDetailRow('Server', tool.serverName),
              if (tool.category != null)
                _buildDetailRow('Category', tool.category!),
              _buildDetailRow('Status', tool.isEnabled ? 'Enabled' : 'Disabled'),
              const SizedBox(height: 16),
              Text(
                'Parameters:',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              if (tool.parameters.isEmpty)
                const Text('No parameters')
              else
                ...tool.parameters.map((param) => Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                param.name,
                                style: const TextStyle(fontWeight: FontWeight.w500),
                              ),
                              const SizedBox(width: 8),
                              Chip(
                                label: Text(param.type),
                                backgroundColor: Theme.of(context).colorScheme.surfaceVariant,
                                labelStyle: const TextStyle(fontSize: 10),
                              ),
                              if (param.required)
                                const SizedBox(width: 4),
                              if (param.required)
                                const Chip(
                                  label: Text('Required'),
                                  backgroundColor: Colors.orange,
                                  labelStyle: TextStyle(fontSize: 10, color: Colors.white),
                                ),
                            ],
                          ),
                          if (param.description != null)
                            Padding(
                              padding: const EdgeInsets.only(top: 4),
                              child: Text(
                                param.description!,
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ),
                        ],
                      ),
                    )),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          if (tool.isEnabled)
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _showTestToolDialog(tool);
              },
              child: const Text('Test Tool'),
            ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: SelectableText(value)),
        ],
      ),
    );
  }
}

class _TestToolDialog extends StatefulWidget {
  final ToolInfo tool;

  const _TestToolDialog({required this.tool});

  @override
  State<_TestToolDialog> createState() => _TestToolDialogState();
}

class _TestToolDialogState extends State<_TestToolDialog> {
  final Map<String, TextEditingController> _controllers = {};
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    for (final param in widget.tool.parameters) {
      _controllers[param.name] = TextEditingController();
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Test ${widget.tool.name}'),
      content: SizedBox(
        width: 400,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(widget.tool.description),
            const SizedBox(height: 16),
            ...widget.tool.parameters.map((param) => Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: TextField(
                    controller: _controllers[param.name]!,
                    decoration: InputDecoration(
                      labelText: param.name,
                      hintText: param.description,
                      border: const OutlineInputBorder(),
                      suffixIcon: param.required 
                          ? const Icon(Icons.star, color: Colors.red, size: 16)
                          : null,
                    ),
                  ),
                )),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _testTool,
          child: _isLoading 
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Execute'),
        ),
      ],
    );
  }

  void _testTool() async {
    // Validate required parameters
    for (final param in widget.tool.parameters) {
      if (param.required && _controllers[param.name]!.text.trim().isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${param.name} is required')),
        );
        return;
      }
    }

    setState(() => _isLoading = true);

    // Simulate tool execution
    await Future.delayed(const Duration(seconds: 2));

    setState(() => _isLoading = false);

    Navigator.of(context).pop();
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('${widget.tool.name} executed successfully')),
    );
  }
}