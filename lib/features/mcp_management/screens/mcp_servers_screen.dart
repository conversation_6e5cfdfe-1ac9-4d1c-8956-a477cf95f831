import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/mcp/mcp_client.dart';

// Mock data for demonstration
class McpServerInfo {
  final String id;
  final String name;
  final String url;
  final McpTransportType transportType;
  final String status;
  final String? description;
  final int toolCount;

  McpServerInfo({
    required this.id,
    required this.name,
    required this.url,
    required this.transportType,
    required this.status,
    this.description,
    this.toolCount = 0,
  });
}

class McpServersScreen extends ConsumerStatefulWidget {
  const McpServersScreen({super.key});

  @override
  ConsumerState<McpServersScreen> createState() => _McpServersScreenState();
}

class _McpServersScreenState extends ConsumerState<McpServersScreen> {
  List<McpServerInfo> _servers = [
    McpServerInfo(
      id: '1',
      name: 'File System Server',
      url: 'ws://localhost:8080/mcp',
      transportType: McpTransportType.websocket,
      status: 'connected',
      description: 'Provides file system access and manipulation tools',
      toolCount: 12,
    ),
    McpServerInfo(
      id: '2',
      name: 'Web Search Server',
      url: 'https://api.example.com/mcp',
      transportType: McpTransportType.http,
      status: 'disconnected',
      description: 'Web search and scraping capabilities',
      toolCount: 5,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('MCP Servers'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddServerDialog(),
            tooltip: 'Add Server',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => _refreshServers(),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _servers.isEmpty
          ? _buildEmptyState()
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _servers.length,
              itemBuilder: (context, index) {
                final server = _servers[index];
                return _buildServerCard(server);
              },
            ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.extension_off,
            size: 64,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 16),
          Text(
            'No MCP Servers',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            'Add MCP servers to extend your chat capabilities with external tools and resources',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _showAddServerDialog(),
            icon: const Icon(Icons.add),
            label: const Text('Add Server'),
          ),
        ],
      ),
    );
  }

  Widget _buildServerCard(McpServerInfo server) {
    final theme = Theme.of(context);
    final isConnected = server.status == 'connected';
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  server.transportType == McpTransportType.websocket
                      ? Icons.wifi
                      : Icons.http,
                  color: isConnected ? Colors.green : Colors.orange,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        server.name,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        server.url,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) => _handleServerAction(server, value),
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: isConnected ? 'disconnect' : 'connect',
                      child: Row(
                        children: [
                          Icon(isConnected ? Icons.power_off : Icons.power),
                          const SizedBox(width: 8),
                          Text(isConnected ? 'Disconnect' : 'Connect'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit),
                          SizedBox(width: 8),
                          Text('Edit'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete),
                          SizedBox(width: 8),
                          Text('Delete'),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            if (server.description != null) ...[
              const SizedBox(height: 8),
              Text(
                server.description!,
                style: theme.textTheme.bodyMedium,
              ),
            ],
            const SizedBox(height: 12),
            Row(
              children: [
                _buildStatusChip(server.status, isConnected),
                const SizedBox(width: 8),
                Chip(
                  label: Text('${server.toolCount} tools'),
                  backgroundColor: theme.colorScheme.primaryContainer,
                  labelStyle: TextStyle(
                    color: theme.colorScheme.onPrimaryContainer,
                    fontSize: 12,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => _viewServerDetails(server),
                  child: const Text('View Details'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status, bool isConnected) {
    final theme = Theme.of(context);
    
    return Chip(
      label: Text(status.toUpperCase()),
      backgroundColor: isConnected 
          ? Colors.green.shade100 
          : Colors.orange.shade100,
      labelStyle: TextStyle(
        color: isConnected ? Colors.green.shade800 : Colors.orange.shade800,
        fontSize: 12,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  void _showAddServerDialog() {
    showDialog(
      context: context,
      builder: (context) => _AddServerDialog(
        onAdd: (server) {
          setState(() {
            _servers.add(server);
          });
        },
      ),
    );
  }

  void _handleServerAction(McpServerInfo server, String action) {
    switch (action) {
      case 'connect':
        _connectServer(server);
        break;
      case 'disconnect':
        _disconnectServer(server);
        break;
      case 'edit':
        _editServer(server);
        break;
      case 'delete':
        _deleteServer(server);
        break;
    }
  }

  void _connectServer(McpServerInfo server) {
    setState(() {
      final index = _servers.indexWhere((s) => s.id == server.id);
      if (index != -1) {
        _servers[index] = McpServerInfo(
          id: server.id,
          name: server.name,
          url: server.url,
          transportType: server.transportType,
          status: 'connected',
          description: server.description,
          toolCount: server.toolCount,
        );
      }
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Connected to ${server.name}')),
    );
  }

  void _disconnectServer(McpServerInfo server) {
    setState(() {
      final index = _servers.indexWhere((s) => s.id == server.id);
      if (index != -1) {
        _servers[index] = McpServerInfo(
          id: server.id,
          name: server.name,
          url: server.url,
          transportType: server.transportType,
          status: 'disconnected',
          description: server.description,
          toolCount: server.toolCount,
        );
      }
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Disconnected from ${server.name}')),
    );
  }

  void _editServer(McpServerInfo server) {
    showDialog(
      context: context,
      builder: (context) => _EditServerDialog(
        server: server,
        onSave: (updatedServer) {
          setState(() {
            final index = _servers.indexWhere((s) => s.id == server.id);
            if (index != -1) {
              _servers[index] = updatedServer;
            }
          });
        },
      ),
    );
  }

  void _deleteServer(McpServerInfo server) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Server'),
        content: Text('Are you sure you want to delete "${server.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _servers.removeWhere((s) => s.id == server.id);
              });
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Deleted ${server.name}')),
              );
            },
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _viewServerDetails(McpServerInfo server) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(server.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('URL', server.url),
            _buildDetailRow('Type', server.transportType.name),
            _buildDetailRow('Status', server.status),
            _buildDetailRow('Tools', '${server.toolCount}'),
            if (server.description != null)
              _buildDetailRow('Description', server.description!),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: SelectableText(value)),
        ],
      ),
    );
  }

  void _refreshServers() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Refreshed server status')),
    );
  }
}

class _AddServerDialog extends StatefulWidget {
  final Function(McpServerInfo) onAdd;

  const _AddServerDialog({required this.onAdd});

  @override
  State<_AddServerDialog> createState() => _AddServerDialogState();
}

class _AddServerDialogState extends State<_AddServerDialog> {
  final _nameController = TextEditingController();
  final _urlController = TextEditingController();
  final _descriptionController = TextEditingController();
  McpTransportType _transportType = McpTransportType.websocket;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Add MCP Server'),
      content: SizedBox(
        width: 400,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Server Name',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _urlController,
              decoration: const InputDecoration(
                labelText: 'Server URL',
                hintText: 'ws://localhost:8080/mcp or https://api.example.com/mcp',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<McpTransportType>(
              value: _transportType,
              decoration: const InputDecoration(
                labelText: 'Transport Type',
                border: OutlineInputBorder(),
              ),
              items: McpTransportType.values.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Text(type.name.toUpperCase()),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() => _transportType = value);
                }
              },
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description (optional)',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _nameController.text.isEmpty || _urlController.text.isEmpty
              ? null
              : () {
                  final server = McpServerInfo(
                    id: DateTime.now().millisecondsSinceEpoch.toString(),
                    name: _nameController.text,
                    url: _urlController.text,
                    transportType: _transportType,
                    status: 'disconnected',
                    description: _descriptionController.text.isEmpty 
                        ? null 
                        : _descriptionController.text,
                    toolCount: 0,
                  );
                  widget.onAdd(server);
                  Navigator.of(context).pop();
                },
          child: const Text('Add'),
        ),
      ],
    );
  }
}

class _EditServerDialog extends StatefulWidget {
  final McpServerInfo server;
  final Function(McpServerInfo) onSave;

  const _EditServerDialog({required this.server, required this.onSave});

  @override
  State<_EditServerDialog> createState() => _EditServerDialogState();
}

class _EditServerDialogState extends State<_EditServerDialog> {
  late final TextEditingController _nameController;
  late final TextEditingController _urlController;
  late final TextEditingController _descriptionController;
  late McpTransportType _transportType;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.server.name);
    _urlController = TextEditingController(text: widget.server.url);
    _descriptionController = TextEditingController(text: widget.server.description ?? '');
    _transportType = widget.server.transportType;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Edit MCP Server'),
      content: SizedBox(
        width: 400,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Server Name',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _urlController,
              decoration: const InputDecoration(
                labelText: 'Server URL',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<McpTransportType>(
              value: _transportType,
              decoration: const InputDecoration(
                labelText: 'Transport Type',
                border: OutlineInputBorder(),
              ),
              items: McpTransportType.values.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Text(type.name.toUpperCase()),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() => _transportType = value);
                }
              },
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description (optional)',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            final updatedServer = McpServerInfo(
              id: widget.server.id,
              name: _nameController.text,
              url: _urlController.text,
              transportType: _transportType,
              status: widget.server.status,
              description: _descriptionController.text.isEmpty 
                  ? null 
                  : _descriptionController.text,
              toolCount: widget.server.toolCount,
            );
            widget.onSave(updatedServer);
            Navigator.of(context).pop();
          },
          child: const Text('Save'),
        ),
      ],
    );
  }
}