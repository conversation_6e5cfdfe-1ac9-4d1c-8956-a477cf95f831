import 'dart:async';
import 'dart:convert';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uuid/uuid.dart';

import '../../../shared/providers/database_provider.dart';
import '../../../core/database/daos/message_dao.dart';
import '../../../core/database/daos/conversation_dao.dart';
import '../../../core/database/tables/messages.dart';
import '../../../core/database/database.dart';
import '../../../core/api/openrouter/openrouter_client.dart';
import '../../../core/api/openrouter/openrouter_models.dart';
import '../../../core/api/base/streaming_client.dart';
import '../../../core/services/token_counter.dart';
import '../models/message_model.dart';
import '../../settings/providers/settings_provider.dart';
import 'conversation_list_provider.dart';

part 'chat_provider.g.dart';

@riverpod
class Chat extends _$Chat {
  late MessageDao _messageDao;
  late ConversationDao _conversationDao;
  OpenRouterClient? _apiClient;
  CancellationToken? _currentCancellationToken;

  @override
  Future<List<MessageModel>> build(String conversationId) async {
    final database = ref.watch(databaseProvider);
    _messageDao = MessageDao(database);
    _conversationDao = ConversationDao(database);

    final messages = await _messageDao.getMessages(conversationId);
    return messages.map(_mapMessageDataToModel).toList();
  }

  MessageModel _mapMessageDataToModel(MessageData data) {
    MessageMetadata? metadata;
    if (data.metadata != null) {
      try {
        metadata = MessageMetadata.fromJson(jsonDecode(data.metadata!));
      } catch (e) {
        // Handle parsing error
      }
    }

    return MessageModel(
      id: data.id,
      conversationId: data.conversationId,
      content: data.content,
      role: data.role,
      tokenCount: data.tokenCount,
      createdAt: data.createdAt,
      metadata: metadata,
      isStreaming: data.isStreaming,
      parentMessageId: data.parentMessageId,
    );
  }

  Future<void> _initializeApiClient() async {
    if (_apiClient != null) return;

    final settings = await ref.read(settingsProvider.future);
    final apiKey = settings['openrouter_api_key'] as String?;
    
    if (apiKey != null) {
      _apiClient = OpenRouterClient(
        apiKey: apiKey,
        appName: 'Otlo Chat',
      );
    }
  }

  Future<void> sendMessage({
    required String content,
    String? model,
    Map<String, dynamic>? toolCalls,
  }) async {
    await _initializeApiClient();
    
    if (_apiClient == null) {
      throw Exception('API client not initialized. Please check your API key.');
    }

    final settings = await ref.read(settingsProvider.future);
    final selectedModel = model ?? settings['default_model'] as String? ?? 'openai/gpt-4o-mini';

    // Create user message
    const uuid = Uuid();
    final userMessageId = uuid.v4();
    
    await _messageDao.addMessage(
      id: userMessageId,
      conversationId: conversationId,
      content: content,
      role: MessageRole.user,
      tokenCount: TokenCounter.estimateTokens(content),
    );

    // Update conversation timestamp
    await _conversationDao.touchConversation(conversationId);

    // Refresh messages and conversation list
    ref.invalidateSelf();
    ref.invalidate(conversationListProvider);

    // Prepare assistant message for streaming
    final assistantMessageId = uuid.v4();
    await _messageDao.addMessage(
      id: assistantMessageId,
      conversationId: conversationId,
      content: '',
      role: MessageRole.assistant,
    );

    // Refresh to show streaming message
    ref.invalidateSelf();

    try {
      // Get conversation history
      final messages = await _messageDao.getMessages(conversationId);
      final chatMessages = messages.map((msg) => ChatMessage(
        role: msg.role.name,
        content: msg.content,
      )).toList();

      // Create streaming request
      final request = ChatCompletionRequest(
        model: selectedModel,
        messages: chatMessages,
        temperature: settings['temperature'] as double? ?? 0.7,
        maxTokens: settings['max_tokens'] as int?,
        topP: settings['top_p'] as double?,
        frequencyPenalty: settings['frequency_penalty'] as double?,
        presencePenalty: settings['presence_penalty'] as double?,
        stream: true,
      );

      // Cancel any existing stream
      _currentCancellationToken?.cancel();
      _currentCancellationToken = CancellationToken();

      // Start streaming
      String accumulatedContent = '';
      int totalTokens = 0;

      await for (final chunk in _apiClient!.createChatCompletionStream(
        request,
        cancellationToken: _currentCancellationToken,
      )) {
        if (_currentCancellationToken!.isCancelled) break;

        final choice = chunk.choices.firstOrNull;
        if (choice != null) {
          final content = choice.delta.content;
          if (content != null) {
            accumulatedContent += content;
            
            // Update message content
            await _messageDao.updateMessageContent(
              assistantMessageId,
              accumulatedContent,
            );
            
            // Refresh UI
            ref.invalidateSelf();
          }

          // Handle finish reason
          if (choice.finishReason != null) {
            if (chunk.usage != null) {
              totalTokens = chunk.usage!.totalTokens;
            }
            break;
          }
        }
      }

      // Finalize message
      await _messageDao.updateMessageContent(
        assistantMessageId,
        accumulatedContent,
        tokenCount: totalTokens > 0 ? totalTokens : TokenCounter.estimateTokens(accumulatedContent),
        isStreaming: false,
      );

      // Update conversation token count
      final conversationTokens = await _messageDao.getTotalTokenCount(conversationId);
      final conversation = await _conversationDao.getConversation(conversationId);
      if (conversation != null) {
        await _conversationDao.updateConversation(conversation.copyWith(
          tokenCount: conversationTokens,
          updatedAt: DateTime.now(),
        ));
      }

    } catch (e) {
      // Handle error - update message with error state
      await _messageDao.updateMessageContent(
        assistantMessageId,
        'Error: ${e.toString()}',
        isStreaming: false,
      );
    } finally {
      _currentCancellationToken = null;
      ref.invalidateSelf();
      ref.invalidate(conversationListProvider);
    }
  }

  void stopStreaming() {
    _currentCancellationToken?.cancel();
    _currentCancellationToken = null;
  }

  Future<void> regenerateMessage(String messageId) async {
    await _initializeApiClient();
    
    if (_apiClient == null) {
      throw Exception('API client not initialized. Please check your API key.');
    }

    // Find the message and regenerate from the previous user message
    final messages = await _messageDao.getMessages(conversationId);
    final messageIndex = messages.indexWhere((m) => m.id == messageId);
    
    if (messageIndex > 0) {
      final previousMessage = messages[messageIndex - 1];
      if (previousMessage.role == MessageRole.user) {
        // Delete the current assistant message
        await _messageDao.deleteMessage(messageId);
        ref.invalidateSelf();
        
        // Get settings for model and parameters
        final settings = await ref.read(settingsProvider.future);
        final selectedModel = settings['default_model'] as String? ?? 'openai/gpt-4o-mini';

        // Prepare new assistant message for streaming
        const uuid = Uuid();
        final assistantMessageId = uuid.v4();
        await _messageDao.addMessage(
          id: assistantMessageId,
          conversationId: conversationId,
          content: '',
          role: MessageRole.assistant,
        );

        // Refresh to show streaming message
        ref.invalidateSelf();

        try {
          // Get conversation history (up to the user message we want to regenerate from)
          final historyMessages = messages.take(messageIndex + 1).map((msg) => ChatMessage(
            role: msg.role.name,
            content: msg.content,
          )).toList();

          // Create streaming request
          final request = ChatCompletionRequest(
            model: selectedModel,
            messages: historyMessages,
            temperature: settings['temperature'] as double? ?? 0.7,
            maxTokens: settings['max_tokens'] as int?,
            topP: settings['top_p'] as double?,
            frequencyPenalty: settings['frequency_penalty'] as double?,
            presencePenalty: settings['presence_penalty'] as double?,
            stream: true,
          );

          // Cancel any existing stream
          _currentCancellationToken?.cancel();
          _currentCancellationToken = CancellationToken();

          // Start streaming
          String accumulatedContent = '';
          int totalTokens = 0;

          await for (final chunk in _apiClient!.createChatCompletionStream(
            request,
            cancellationToken: _currentCancellationToken,
          )) {
            if (_currentCancellationToken!.isCancelled) break;

            final choice = chunk.choices.firstOrNull;
            if (choice != null) {
              final content = choice.delta.content;
              if (content != null) {
                accumulatedContent += content;
                
                // Update message content
                await _messageDao.updateMessageContent(
                  assistantMessageId,
                  accumulatedContent,
                );
                
                // Refresh UI
                ref.invalidateSelf();
              }

              // Handle finish reason
              if (choice.finishReason != null) {
                if (chunk.usage != null) {
                  totalTokens = chunk.usage!.totalTokens;
                }
                break;
              }
            }
          }

          // Finalize message
          await _messageDao.updateMessageContent(
            assistantMessageId,
            accumulatedContent,
            tokenCount: totalTokens > 0 ? totalTokens : TokenCounter.estimateTokens(accumulatedContent),
            isStreaming: false,
          );

          // Update conversation token count
          final conversationTokens = await _messageDao.getTotalTokenCount(conversationId);
          final conversation = await _conversationDao.getConversation(conversationId);
          if (conversation != null) {
            await _conversationDao.updateConversation(conversation.copyWith(
              tokenCount: conversationTokens,
              updatedAt: DateTime.now(),
            ));
          }

        } catch (e) {
          // Handle error - update message with error state
          await _messageDao.updateMessageContent(
            assistantMessageId,
            'Error: ${e.toString()}',
            isStreaming: false,
          );
        } finally {
          _currentCancellationToken = null;
          ref.invalidateSelf();
          ref.invalidate(conversationListProvider);
        }
      }
    }
  }

  Future<void> editMessage(String messageId, String newContent) async {
    await _messageDao.updateMessageContent(messageId, newContent);
    ref.invalidateSelf();
  }

  Future<void> deleteMessage(String messageId) async {
    await _messageDao.deleteMessage(messageId);
    ref.invalidateSelf();
    ref.invalidate(conversationListProvider);
  }

  Future<void> clearConversation() async {
    await _messageDao.deleteMessagesInConversation(conversationId);
    ref.invalidateSelf();
    ref.invalidate(conversationListProvider);
  }

  bool get isStreaming => _currentCancellationToken != null;
}

@riverpod
class StreamingProvider extends _$StreamingProvider {
  @override
  StreamingState<String> build() {
    return const StreamingState<String>();
  }

  void startStreaming() {
    state = state.start();
  }

  void addChunk(String chunk) {
    state = state.addToBuffer(chunk);
  }

  void setError(String error) {
    state = state.setError(error);
  }

  void complete() {
    state = state.complete();
  }
}