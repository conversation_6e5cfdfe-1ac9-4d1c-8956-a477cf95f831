// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

@ProviderFor(Chat)
const chatProvider = ChatFamily._();

final class ChatProvider
    extends $AsyncNotifierProvider<Chat, List<MessageModel>> {
  const ChatProvider._({
    required ChatFamily super.from,
    required String super.argument,
  }) : super(
         retry: null,
         name: r'chatProvider',
         isAutoDispose: true,
         dependencies: null,
         $allTransitiveDependencies: null,
       );

  @override
  String debugGetCreateSourceHash() => _$chatHash();

  @override
  String toString() {
    return r'chatProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  Chat create() => Chat();

  @override
  bool operator ==(Object other) {
    return other is ChatProvider && other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$chatHash() => r'885f668e978dd71170c777de4d455b964542766a';

final class ChatFamily extends $Family
    with
        $ClassFamilyOverride<
          Chat,
          AsyncValue<List<MessageModel>>,
          List<MessageModel>,
          FutureOr<List<MessageModel>>,
          String
        > {
  const ChatFamily._()
    : super(
        retry: null,
        name: r'chatProvider',
        dependencies: null,
        $allTransitiveDependencies: null,
        isAutoDispose: true,
      );

  ChatProvider call(String conversationId) =>
      ChatProvider._(argument: conversationId, from: this);

  @override
  String toString() => r'chatProvider';
}

abstract class _$Chat extends $AsyncNotifier<List<MessageModel>> {
  late final _$args = ref.$arg as String;
  String get conversationId => _$args;

  FutureOr<List<MessageModel>> build(String conversationId);
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build(_$args);
    final ref =
        this.ref as $Ref<AsyncValue<List<MessageModel>>, List<MessageModel>>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<List<MessageModel>>, List<MessageModel>>,
              AsyncValue<List<MessageModel>>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

@ProviderFor(StreamingProvider)
const streamingProviderProvider = StreamingProviderProvider._();

final class StreamingProviderProvider
    extends $NotifierProvider<StreamingProvider, StreamingState<String>> {
  const StreamingProviderProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'streamingProviderProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$streamingProviderHash();

  @$internal
  @override
  StreamingProvider create() => StreamingProvider();

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(StreamingState<String> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<StreamingState<String>>(value),
    );
  }
}

String _$streamingProviderHash() => r'ec9afb9fbd46546b409b7cca634ed27e475620b1';

abstract class _$StreamingProvider extends $Notifier<StreamingState<String>> {
  StreamingState<String> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref =
        this.ref as $Ref<StreamingState<String>, StreamingState<String>>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<StreamingState<String>, StreamingState<String>>,
              StreamingState<String>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
