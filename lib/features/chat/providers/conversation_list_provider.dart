import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:drift/drift.dart';
import '../../../shared/providers/database_provider.dart';
import '../../../core/database/daos/conversation_dao.dart';
import '../models/conversation_model.dart';

part 'conversation_list_provider.g.dart';

@riverpod
class ConversationList extends _$ConversationList {
  @override
  Future<List<ConversationModel>> build() async {
    final database = ref.watch(databaseProvider);
    final dao = ConversationDao(database);
    
    final conversationsWithStats = await dao.getConversationsWithStats();
    
    return conversationsWithStats.map((stats) {
      final conv = stats.conversation;
      final lastMessage = stats.lastMessage;
      
      return ConversationModel(
        id: conv.id,
        title: conv.title,
        model: conv.model,
        lastUsedModel: conv.lastUsedModel,
        tokenCount: conv.tokenCount,
        createdAt: conv.createdAt,
        updatedAt: conv.updatedAt,
        isPinned: conv.isPinned,
        folderId: conv.folderId,
        messageCount: stats.messageCount,
        lastMessagePreview: lastMessage?.content,
        lastMessageAt: lastMessage?.createdAt,
      );
    }).toList();
  }

  Future<void> refresh() async {
    ref.invalidateSelf();
  }

  Future<ConversationModel> createConversation({
    required String model,
    String? title,
  }) async {
    final database = ref.watch(databaseProvider);
    final dao = ConversationDao(database);
    
    final id = DateTime.now().millisecondsSinceEpoch.toString();
    await dao.createConversation(id: id, model: model, title: title);
    
    // Refresh the list
    ref.invalidateSelf();
    
    return ConversationModel(
      id: id,
      title: title,
      model: model,
      lastUsedModel: model, // Initialize with the same model
      tokenCount: 0,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      isPinned: false,
      messageCount: 0,
    );
  }

  Future<void> deleteConversation(String id) async {
    final database = ref.watch(databaseProvider);
    final dao = ConversationDao(database);
    
    await dao.deleteConversation(id);
    ref.invalidateSelf();
  }

  Future<void> togglePin(String id) async {
    final database = ref.watch(databaseProvider);
    final dao = ConversationDao(database);
    
    await dao.togglePin(id);
    ref.invalidateSelf();
  }

  Future<void> updateConversationTitle(String id, String title) async {
    final database = ref.watch(databaseProvider);
    final dao = ConversationDao(database);
    
    final conversation = await dao.getConversation(id);
    if (conversation != null) {
      await dao.updateConversation(conversation.copyWith(
        title: Value(title),
        updatedAt: DateTime.now(),
      ));
      ref.invalidateSelf();
    }
  }

  /// Updates the last used model for a conversation
  Future<void> updateLastUsedModel(String id, String modelId) async {
    final database = ref.watch(databaseProvider);
    final dao = ConversationDao(database);
    
    final conversation = await dao.getConversation(id);
    if (conversation != null) {
      await dao.updateConversation(conversation.copyWith(
        lastUsedModel: Value(modelId),
        updatedAt: DateTime.now(),
      ));
      ref.invalidateSelf();
    }
  }
}

@riverpod
Future<ConversationModel?> conversation(Ref ref, String id) async {
  final database = ref.watch(databaseProvider);
  final dao = ConversationDao(database);
  
  final conversationData = await dao.getConversation(id);
  if (conversationData == null) return null;
  
  return ConversationModel(
    id: conversationData.id,
    title: conversationData.title,
    model: conversationData.model,
    lastUsedModel: conversationData.lastUsedModel,
    tokenCount: conversationData.tokenCount,
    createdAt: conversationData.createdAt,
    updatedAt: conversationData.updatedAt,
    isPinned: conversationData.isPinned,
    folderId: conversationData.folderId,
    messageCount: 0, // This would need a separate query
  );
}