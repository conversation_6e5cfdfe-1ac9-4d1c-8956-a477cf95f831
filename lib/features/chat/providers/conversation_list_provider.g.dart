// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'conversation_list_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

@ProviderFor(ConversationList)
const conversationListProvider = ConversationListProvider._();

final class ConversationListProvider
    extends $AsyncNotifierProvider<ConversationList, List<ConversationModel>> {
  const ConversationListProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'conversationListProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$conversationListHash();

  @$internal
  @override
  ConversationList create() => ConversationList();
}

String _$conversationListHash() => r'2f4948b11bfb4494fd4c7c6fa47116e79c97f42b';

abstract class _$ConversationList
    extends $AsyncNotifier<List<ConversationModel>> {
  FutureOr<List<ConversationModel>> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref =
        this.ref
            as $Ref<
              AsyncValue<List<ConversationModel>>,
              List<ConversationModel>
            >;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<
                AsyncValue<List<ConversationModel>>,
                List<ConversationModel>
              >,
              AsyncValue<List<ConversationModel>>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

@ProviderFor(conversation)
const conversationProvider = ConversationFamily._();

final class ConversationProvider
    extends
        $FunctionalProvider<
          AsyncValue<ConversationModel?>,
          ConversationModel?,
          FutureOr<ConversationModel?>
        >
    with
        $FutureModifier<ConversationModel?>,
        $FutureProvider<ConversationModel?> {
  const ConversationProvider._({
    required ConversationFamily super.from,
    required String super.argument,
  }) : super(
         retry: null,
         name: r'conversationProvider',
         isAutoDispose: true,
         dependencies: null,
         $allTransitiveDependencies: null,
       );

  @override
  String debugGetCreateSourceHash() => _$conversationHash();

  @override
  String toString() {
    return r'conversationProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  $FutureProviderElement<ConversationModel?> $createElement(
    $ProviderPointer pointer,
  ) => $FutureProviderElement(pointer);

  @override
  FutureOr<ConversationModel?> create(Ref ref) {
    final argument = this.argument as String;
    return conversation(ref, argument);
  }

  @override
  bool operator ==(Object other) {
    return other is ConversationProvider && other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$conversationHash() => r'4865c89ec219ca2a4e0f459200683c3039fe57a6';

final class ConversationFamily extends $Family
    with $FunctionalFamilyOverride<FutureOr<ConversationModel?>, String> {
  const ConversationFamily._()
    : super(
        retry: null,
        name: r'conversationProvider',
        dependencies: null,
        $allTransitiveDependencies: null,
        isAutoDispose: true,
      );

  ConversationProvider call(String id) =>
      ConversationProvider._(argument: id, from: this);

  @override
  String toString() => r'conversationProvider';
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
