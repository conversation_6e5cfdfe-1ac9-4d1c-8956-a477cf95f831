import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../core/database/tables/messages.dart';

part 'message_model.freezed.dart';
part 'message_model.g.dart';

@freezed
abstract class MessageModel with _$MessageModel {
  const factory MessageModel({
    required String id,
    required String conversationId,
    required String content,
    required MessageRole role,
    @Default(0) int tokenCount,
    required DateTime createdAt,
    MessageMetadata? metadata,
    @Default(false) bool isStreaming,
    String? parentMessageId,
  }) = _MessageModel;

  const MessageModel._();

  bool get isUser => role == MessageRole.user;
  bool get isAssistant => role == MessageRole.assistant;
  bool get isSystem => role == MessageRole.system;
  bool get isTool => role == MessageRole.tool;

  String get displayRole {
    switch (role) {
      case MessageRole.user:
        return 'You';
      case MessageRole.assistant:
        return 'Assistant';
      case MessageRole.system:
        return 'System';
      case MessageRole.tool:
        return 'Tool';
    }
  }

  String get formattedTime {
    final hour = createdAt.hour;
    final minute = createdAt.minute.toString().padLeft(2, '0');
    final period = hour >= 12 ? 'PM' : 'AM';
    final displayHour = hour == 0 ? 12 : (hour > 12 ? hour - 12 : hour);
    return '$displayHour:$minute $period';
  }
}

@freezed
abstract class MessageMetadata with _$MessageMetadata {
  const factory MessageMetadata({
    List<ToolCallMetadata>? toolCalls,
    String? finishReason,
    Map<String, dynamic>? usage,
    String? model,
    Map<String, dynamic>? extra,
  }) = _MessageMetadata;

  factory MessageMetadata.fromJson(Map<String, dynamic> json) =>
      _$MessageMetadataFromJson(json);
}

@freezed
abstract class ToolCallMetadata with _$ToolCallMetadata {
  const factory ToolCallMetadata({
    required String id,
    required String name,
    required Map<String, dynamic> arguments,
    String? result,
    String? error,
    @Default(false) bool isExecuted,
    DateTime? executedAt,
    String? serverId,
  }) = _ToolCallMetadata;

  factory ToolCallMetadata.fromJson(Map<String, dynamic> json) =>
      _$ToolCallMetadataFromJson(json);
}