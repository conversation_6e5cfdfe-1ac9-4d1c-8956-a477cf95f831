// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'message_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$MessageModel {

 String get id; String get conversationId; String get content; MessageRole get role; int get tokenCount; DateTime get createdAt; MessageMetadata? get metadata; bool get isStreaming; String? get parentMessageId;
/// Create a copy of MessageModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MessageModelCopyWith<MessageModel> get copyWith => _$MessageModelCopyWithImpl<MessageModel>(this as MessageModel, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MessageModel&&(identical(other.id, id) || other.id == id)&&(identical(other.conversationId, conversationId) || other.conversationId == conversationId)&&(identical(other.content, content) || other.content == content)&&(identical(other.role, role) || other.role == role)&&(identical(other.tokenCount, tokenCount) || other.tokenCount == tokenCount)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.metadata, metadata) || other.metadata == metadata)&&(identical(other.isStreaming, isStreaming) || other.isStreaming == isStreaming)&&(identical(other.parentMessageId, parentMessageId) || other.parentMessageId == parentMessageId));
}


@override
int get hashCode => Object.hash(runtimeType,id,conversationId,content,role,tokenCount,createdAt,metadata,isStreaming,parentMessageId);

@override
String toString() {
  return 'MessageModel(id: $id, conversationId: $conversationId, content: $content, role: $role, tokenCount: $tokenCount, createdAt: $createdAt, metadata: $metadata, isStreaming: $isStreaming, parentMessageId: $parentMessageId)';
}


}

/// @nodoc
abstract mixin class $MessageModelCopyWith<$Res>  {
  factory $MessageModelCopyWith(MessageModel value, $Res Function(MessageModel) _then) = _$MessageModelCopyWithImpl;
@useResult
$Res call({
 String id, String conversationId, String content, MessageRole role, int tokenCount, DateTime createdAt, MessageMetadata? metadata, bool isStreaming, String? parentMessageId
});


$MessageMetadataCopyWith<$Res>? get metadata;

}
/// @nodoc
class _$MessageModelCopyWithImpl<$Res>
    implements $MessageModelCopyWith<$Res> {
  _$MessageModelCopyWithImpl(this._self, this._then);

  final MessageModel _self;
  final $Res Function(MessageModel) _then;

/// Create a copy of MessageModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? conversationId = null,Object? content = null,Object? role = null,Object? tokenCount = null,Object? createdAt = null,Object? metadata = freezed,Object? isStreaming = null,Object? parentMessageId = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,conversationId: null == conversationId ? _self.conversationId : conversationId // ignore: cast_nullable_to_non_nullable
as String,content: null == content ? _self.content : content // ignore: cast_nullable_to_non_nullable
as String,role: null == role ? _self.role : role // ignore: cast_nullable_to_non_nullable
as MessageRole,tokenCount: null == tokenCount ? _self.tokenCount : tokenCount // ignore: cast_nullable_to_non_nullable
as int,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as MessageMetadata?,isStreaming: null == isStreaming ? _self.isStreaming : isStreaming // ignore: cast_nullable_to_non_nullable
as bool,parentMessageId: freezed == parentMessageId ? _self.parentMessageId : parentMessageId // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}
/// Create a copy of MessageModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MessageMetadataCopyWith<$Res>? get metadata {
    if (_self.metadata == null) {
    return null;
  }

  return $MessageMetadataCopyWith<$Res>(_self.metadata!, (value) {
    return _then(_self.copyWith(metadata: value));
  });
}
}


/// Adds pattern-matching-related methods to [MessageModel].
extension MessageModelPatterns on MessageModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _MessageModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _MessageModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _MessageModel value)  $default,){
final _that = this;
switch (_that) {
case _MessageModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _MessageModel value)?  $default,){
final _that = this;
switch (_that) {
case _MessageModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String conversationId,  String content,  MessageRole role,  int tokenCount,  DateTime createdAt,  MessageMetadata? metadata,  bool isStreaming,  String? parentMessageId)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _MessageModel() when $default != null:
return $default(_that.id,_that.conversationId,_that.content,_that.role,_that.tokenCount,_that.createdAt,_that.metadata,_that.isStreaming,_that.parentMessageId);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String conversationId,  String content,  MessageRole role,  int tokenCount,  DateTime createdAt,  MessageMetadata? metadata,  bool isStreaming,  String? parentMessageId)  $default,) {final _that = this;
switch (_that) {
case _MessageModel():
return $default(_that.id,_that.conversationId,_that.content,_that.role,_that.tokenCount,_that.createdAt,_that.metadata,_that.isStreaming,_that.parentMessageId);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String conversationId,  String content,  MessageRole role,  int tokenCount,  DateTime createdAt,  MessageMetadata? metadata,  bool isStreaming,  String? parentMessageId)?  $default,) {final _that = this;
switch (_that) {
case _MessageModel() when $default != null:
return $default(_that.id,_that.conversationId,_that.content,_that.role,_that.tokenCount,_that.createdAt,_that.metadata,_that.isStreaming,_that.parentMessageId);case _:
  return null;

}
}

}

/// @nodoc


class _MessageModel extends MessageModel {
  const _MessageModel({required this.id, required this.conversationId, required this.content, required this.role, this.tokenCount = 0, required this.createdAt, this.metadata, this.isStreaming = false, this.parentMessageId}): super._();
  

@override final  String id;
@override final  String conversationId;
@override final  String content;
@override final  MessageRole role;
@override@JsonKey() final  int tokenCount;
@override final  DateTime createdAt;
@override final  MessageMetadata? metadata;
@override@JsonKey() final  bool isStreaming;
@override final  String? parentMessageId;

/// Create a copy of MessageModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MessageModelCopyWith<_MessageModel> get copyWith => __$MessageModelCopyWithImpl<_MessageModel>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _MessageModel&&(identical(other.id, id) || other.id == id)&&(identical(other.conversationId, conversationId) || other.conversationId == conversationId)&&(identical(other.content, content) || other.content == content)&&(identical(other.role, role) || other.role == role)&&(identical(other.tokenCount, tokenCount) || other.tokenCount == tokenCount)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.metadata, metadata) || other.metadata == metadata)&&(identical(other.isStreaming, isStreaming) || other.isStreaming == isStreaming)&&(identical(other.parentMessageId, parentMessageId) || other.parentMessageId == parentMessageId));
}


@override
int get hashCode => Object.hash(runtimeType,id,conversationId,content,role,tokenCount,createdAt,metadata,isStreaming,parentMessageId);

@override
String toString() {
  return 'MessageModel(id: $id, conversationId: $conversationId, content: $content, role: $role, tokenCount: $tokenCount, createdAt: $createdAt, metadata: $metadata, isStreaming: $isStreaming, parentMessageId: $parentMessageId)';
}


}

/// @nodoc
abstract mixin class _$MessageModelCopyWith<$Res> implements $MessageModelCopyWith<$Res> {
  factory _$MessageModelCopyWith(_MessageModel value, $Res Function(_MessageModel) _then) = __$MessageModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String conversationId, String content, MessageRole role, int tokenCount, DateTime createdAt, MessageMetadata? metadata, bool isStreaming, String? parentMessageId
});


@override $MessageMetadataCopyWith<$Res>? get metadata;

}
/// @nodoc
class __$MessageModelCopyWithImpl<$Res>
    implements _$MessageModelCopyWith<$Res> {
  __$MessageModelCopyWithImpl(this._self, this._then);

  final _MessageModel _self;
  final $Res Function(_MessageModel) _then;

/// Create a copy of MessageModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? conversationId = null,Object? content = null,Object? role = null,Object? tokenCount = null,Object? createdAt = null,Object? metadata = freezed,Object? isStreaming = null,Object? parentMessageId = freezed,}) {
  return _then(_MessageModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,conversationId: null == conversationId ? _self.conversationId : conversationId // ignore: cast_nullable_to_non_nullable
as String,content: null == content ? _self.content : content // ignore: cast_nullable_to_non_nullable
as String,role: null == role ? _self.role : role // ignore: cast_nullable_to_non_nullable
as MessageRole,tokenCount: null == tokenCount ? _self.tokenCount : tokenCount // ignore: cast_nullable_to_non_nullable
as int,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as MessageMetadata?,isStreaming: null == isStreaming ? _self.isStreaming : isStreaming // ignore: cast_nullable_to_non_nullable
as bool,parentMessageId: freezed == parentMessageId ? _self.parentMessageId : parentMessageId // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of MessageModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MessageMetadataCopyWith<$Res>? get metadata {
    if (_self.metadata == null) {
    return null;
  }

  return $MessageMetadataCopyWith<$Res>(_self.metadata!, (value) {
    return _then(_self.copyWith(metadata: value));
  });
}
}


/// @nodoc
mixin _$MessageMetadata {

 List<ToolCallMetadata>? get toolCalls; String? get finishReason; Map<String, dynamic>? get usage; String? get model; Map<String, dynamic>? get extra;
/// Create a copy of MessageMetadata
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MessageMetadataCopyWith<MessageMetadata> get copyWith => _$MessageMetadataCopyWithImpl<MessageMetadata>(this as MessageMetadata, _$identity);

  /// Serializes this MessageMetadata to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MessageMetadata&&const DeepCollectionEquality().equals(other.toolCalls, toolCalls)&&(identical(other.finishReason, finishReason) || other.finishReason == finishReason)&&const DeepCollectionEquality().equals(other.usage, usage)&&(identical(other.model, model) || other.model == model)&&const DeepCollectionEquality().equals(other.extra, extra));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(toolCalls),finishReason,const DeepCollectionEquality().hash(usage),model,const DeepCollectionEquality().hash(extra));

@override
String toString() {
  return 'MessageMetadata(toolCalls: $toolCalls, finishReason: $finishReason, usage: $usage, model: $model, extra: $extra)';
}


}

/// @nodoc
abstract mixin class $MessageMetadataCopyWith<$Res>  {
  factory $MessageMetadataCopyWith(MessageMetadata value, $Res Function(MessageMetadata) _then) = _$MessageMetadataCopyWithImpl;
@useResult
$Res call({
 List<ToolCallMetadata>? toolCalls, String? finishReason, Map<String, dynamic>? usage, String? model, Map<String, dynamic>? extra
});




}
/// @nodoc
class _$MessageMetadataCopyWithImpl<$Res>
    implements $MessageMetadataCopyWith<$Res> {
  _$MessageMetadataCopyWithImpl(this._self, this._then);

  final MessageMetadata _self;
  final $Res Function(MessageMetadata) _then;

/// Create a copy of MessageMetadata
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? toolCalls = freezed,Object? finishReason = freezed,Object? usage = freezed,Object? model = freezed,Object? extra = freezed,}) {
  return _then(_self.copyWith(
toolCalls: freezed == toolCalls ? _self.toolCalls : toolCalls // ignore: cast_nullable_to_non_nullable
as List<ToolCallMetadata>?,finishReason: freezed == finishReason ? _self.finishReason : finishReason // ignore: cast_nullable_to_non_nullable
as String?,usage: freezed == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,model: freezed == model ? _self.model : model // ignore: cast_nullable_to_non_nullable
as String?,extra: freezed == extra ? _self.extra : extra // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [MessageMetadata].
extension MessageMetadataPatterns on MessageMetadata {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _MessageMetadata value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _MessageMetadata() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _MessageMetadata value)  $default,){
final _that = this;
switch (_that) {
case _MessageMetadata():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _MessageMetadata value)?  $default,){
final _that = this;
switch (_that) {
case _MessageMetadata() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<ToolCallMetadata>? toolCalls,  String? finishReason,  Map<String, dynamic>? usage,  String? model,  Map<String, dynamic>? extra)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _MessageMetadata() when $default != null:
return $default(_that.toolCalls,_that.finishReason,_that.usage,_that.model,_that.extra);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<ToolCallMetadata>? toolCalls,  String? finishReason,  Map<String, dynamic>? usage,  String? model,  Map<String, dynamic>? extra)  $default,) {final _that = this;
switch (_that) {
case _MessageMetadata():
return $default(_that.toolCalls,_that.finishReason,_that.usage,_that.model,_that.extra);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<ToolCallMetadata>? toolCalls,  String? finishReason,  Map<String, dynamic>? usage,  String? model,  Map<String, dynamic>? extra)?  $default,) {final _that = this;
switch (_that) {
case _MessageMetadata() when $default != null:
return $default(_that.toolCalls,_that.finishReason,_that.usage,_that.model,_that.extra);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _MessageMetadata implements MessageMetadata {
  const _MessageMetadata({final  List<ToolCallMetadata>? toolCalls, this.finishReason, final  Map<String, dynamic>? usage, this.model, final  Map<String, dynamic>? extra}): _toolCalls = toolCalls,_usage = usage,_extra = extra;
  factory _MessageMetadata.fromJson(Map<String, dynamic> json) => _$MessageMetadataFromJson(json);

 final  List<ToolCallMetadata>? _toolCalls;
@override List<ToolCallMetadata>? get toolCalls {
  final value = _toolCalls;
  if (value == null) return null;
  if (_toolCalls is EqualUnmodifiableListView) return _toolCalls;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override final  String? finishReason;
 final  Map<String, dynamic>? _usage;
@override Map<String, dynamic>? get usage {
  final value = _usage;
  if (value == null) return null;
  if (_usage is EqualUnmodifiableMapView) return _usage;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override final  String? model;
 final  Map<String, dynamic>? _extra;
@override Map<String, dynamic>? get extra {
  final value = _extra;
  if (value == null) return null;
  if (_extra is EqualUnmodifiableMapView) return _extra;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of MessageMetadata
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MessageMetadataCopyWith<_MessageMetadata> get copyWith => __$MessageMetadataCopyWithImpl<_MessageMetadata>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$MessageMetadataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _MessageMetadata&&const DeepCollectionEquality().equals(other._toolCalls, _toolCalls)&&(identical(other.finishReason, finishReason) || other.finishReason == finishReason)&&const DeepCollectionEquality().equals(other._usage, _usage)&&(identical(other.model, model) || other.model == model)&&const DeepCollectionEquality().equals(other._extra, _extra));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_toolCalls),finishReason,const DeepCollectionEquality().hash(_usage),model,const DeepCollectionEquality().hash(_extra));

@override
String toString() {
  return 'MessageMetadata(toolCalls: $toolCalls, finishReason: $finishReason, usage: $usage, model: $model, extra: $extra)';
}


}

/// @nodoc
abstract mixin class _$MessageMetadataCopyWith<$Res> implements $MessageMetadataCopyWith<$Res> {
  factory _$MessageMetadataCopyWith(_MessageMetadata value, $Res Function(_MessageMetadata) _then) = __$MessageMetadataCopyWithImpl;
@override @useResult
$Res call({
 List<ToolCallMetadata>? toolCalls, String? finishReason, Map<String, dynamic>? usage, String? model, Map<String, dynamic>? extra
});




}
/// @nodoc
class __$MessageMetadataCopyWithImpl<$Res>
    implements _$MessageMetadataCopyWith<$Res> {
  __$MessageMetadataCopyWithImpl(this._self, this._then);

  final _MessageMetadata _self;
  final $Res Function(_MessageMetadata) _then;

/// Create a copy of MessageMetadata
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? toolCalls = freezed,Object? finishReason = freezed,Object? usage = freezed,Object? model = freezed,Object? extra = freezed,}) {
  return _then(_MessageMetadata(
toolCalls: freezed == toolCalls ? _self._toolCalls : toolCalls // ignore: cast_nullable_to_non_nullable
as List<ToolCallMetadata>?,finishReason: freezed == finishReason ? _self.finishReason : finishReason // ignore: cast_nullable_to_non_nullable
as String?,usage: freezed == usage ? _self._usage : usage // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,model: freezed == model ? _self.model : model // ignore: cast_nullable_to_non_nullable
as String?,extra: freezed == extra ? _self._extra : extra // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$ToolCallMetadata {

 String get id; String get name; Map<String, dynamic> get arguments; String? get result; String? get error; bool get isExecuted; DateTime? get executedAt; String? get serverId;
/// Create a copy of ToolCallMetadata
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ToolCallMetadataCopyWith<ToolCallMetadata> get copyWith => _$ToolCallMetadataCopyWithImpl<ToolCallMetadata>(this as ToolCallMetadata, _$identity);

  /// Serializes this ToolCallMetadata to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ToolCallMetadata&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&const DeepCollectionEquality().equals(other.arguments, arguments)&&(identical(other.result, result) || other.result == result)&&(identical(other.error, error) || other.error == error)&&(identical(other.isExecuted, isExecuted) || other.isExecuted == isExecuted)&&(identical(other.executedAt, executedAt) || other.executedAt == executedAt)&&(identical(other.serverId, serverId) || other.serverId == serverId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,const DeepCollectionEquality().hash(arguments),result,error,isExecuted,executedAt,serverId);

@override
String toString() {
  return 'ToolCallMetadata(id: $id, name: $name, arguments: $arguments, result: $result, error: $error, isExecuted: $isExecuted, executedAt: $executedAt, serverId: $serverId)';
}


}

/// @nodoc
abstract mixin class $ToolCallMetadataCopyWith<$Res>  {
  factory $ToolCallMetadataCopyWith(ToolCallMetadata value, $Res Function(ToolCallMetadata) _then) = _$ToolCallMetadataCopyWithImpl;
@useResult
$Res call({
 String id, String name, Map<String, dynamic> arguments, String? result, String? error, bool isExecuted, DateTime? executedAt, String? serverId
});




}
/// @nodoc
class _$ToolCallMetadataCopyWithImpl<$Res>
    implements $ToolCallMetadataCopyWith<$Res> {
  _$ToolCallMetadataCopyWithImpl(this._self, this._then);

  final ToolCallMetadata _self;
  final $Res Function(ToolCallMetadata) _then;

/// Create a copy of ToolCallMetadata
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? arguments = null,Object? result = freezed,Object? error = freezed,Object? isExecuted = null,Object? executedAt = freezed,Object? serverId = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,arguments: null == arguments ? _self.arguments : arguments // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,result: freezed == result ? _self.result : result // ignore: cast_nullable_to_non_nullable
as String?,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,isExecuted: null == isExecuted ? _self.isExecuted : isExecuted // ignore: cast_nullable_to_non_nullable
as bool,executedAt: freezed == executedAt ? _self.executedAt : executedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,serverId: freezed == serverId ? _self.serverId : serverId // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [ToolCallMetadata].
extension ToolCallMetadataPatterns on ToolCallMetadata {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ToolCallMetadata value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ToolCallMetadata() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ToolCallMetadata value)  $default,){
final _that = this;
switch (_that) {
case _ToolCallMetadata():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ToolCallMetadata value)?  $default,){
final _that = this;
switch (_that) {
case _ToolCallMetadata() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  Map<String, dynamic> arguments,  String? result,  String? error,  bool isExecuted,  DateTime? executedAt,  String? serverId)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ToolCallMetadata() when $default != null:
return $default(_that.id,_that.name,_that.arguments,_that.result,_that.error,_that.isExecuted,_that.executedAt,_that.serverId);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  Map<String, dynamic> arguments,  String? result,  String? error,  bool isExecuted,  DateTime? executedAt,  String? serverId)  $default,) {final _that = this;
switch (_that) {
case _ToolCallMetadata():
return $default(_that.id,_that.name,_that.arguments,_that.result,_that.error,_that.isExecuted,_that.executedAt,_that.serverId);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  Map<String, dynamic> arguments,  String? result,  String? error,  bool isExecuted,  DateTime? executedAt,  String? serverId)?  $default,) {final _that = this;
switch (_that) {
case _ToolCallMetadata() when $default != null:
return $default(_that.id,_that.name,_that.arguments,_that.result,_that.error,_that.isExecuted,_that.executedAt,_that.serverId);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ToolCallMetadata implements ToolCallMetadata {
  const _ToolCallMetadata({required this.id, required this.name, required final  Map<String, dynamic> arguments, this.result, this.error, this.isExecuted = false, this.executedAt, this.serverId}): _arguments = arguments;
  factory _ToolCallMetadata.fromJson(Map<String, dynamic> json) => _$ToolCallMetadataFromJson(json);

@override final  String id;
@override final  String name;
 final  Map<String, dynamic> _arguments;
@override Map<String, dynamic> get arguments {
  if (_arguments is EqualUnmodifiableMapView) return _arguments;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_arguments);
}

@override final  String? result;
@override final  String? error;
@override@JsonKey() final  bool isExecuted;
@override final  DateTime? executedAt;
@override final  String? serverId;

/// Create a copy of ToolCallMetadata
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ToolCallMetadataCopyWith<_ToolCallMetadata> get copyWith => __$ToolCallMetadataCopyWithImpl<_ToolCallMetadata>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ToolCallMetadataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ToolCallMetadata&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&const DeepCollectionEquality().equals(other._arguments, _arguments)&&(identical(other.result, result) || other.result == result)&&(identical(other.error, error) || other.error == error)&&(identical(other.isExecuted, isExecuted) || other.isExecuted == isExecuted)&&(identical(other.executedAt, executedAt) || other.executedAt == executedAt)&&(identical(other.serverId, serverId) || other.serverId == serverId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,const DeepCollectionEquality().hash(_arguments),result,error,isExecuted,executedAt,serverId);

@override
String toString() {
  return 'ToolCallMetadata(id: $id, name: $name, arguments: $arguments, result: $result, error: $error, isExecuted: $isExecuted, executedAt: $executedAt, serverId: $serverId)';
}


}

/// @nodoc
abstract mixin class _$ToolCallMetadataCopyWith<$Res> implements $ToolCallMetadataCopyWith<$Res> {
  factory _$ToolCallMetadataCopyWith(_ToolCallMetadata value, $Res Function(_ToolCallMetadata) _then) = __$ToolCallMetadataCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, Map<String, dynamic> arguments, String? result, String? error, bool isExecuted, DateTime? executedAt, String? serverId
});




}
/// @nodoc
class __$ToolCallMetadataCopyWithImpl<$Res>
    implements _$ToolCallMetadataCopyWith<$Res> {
  __$ToolCallMetadataCopyWithImpl(this._self, this._then);

  final _ToolCallMetadata _self;
  final $Res Function(_ToolCallMetadata) _then;

/// Create a copy of ToolCallMetadata
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? arguments = null,Object? result = freezed,Object? error = freezed,Object? isExecuted = null,Object? executedAt = freezed,Object? serverId = freezed,}) {
  return _then(_ToolCallMetadata(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,arguments: null == arguments ? _self._arguments : arguments // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,result: freezed == result ? _self.result : result // ignore: cast_nullable_to_non_nullable
as String?,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,isExecuted: null == isExecuted ? _self.isExecuted : isExecuted // ignore: cast_nullable_to_non_nullable
as bool,executedAt: freezed == executedAt ? _self.executedAt : executedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,serverId: freezed == serverId ? _self.serverId : serverId // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
