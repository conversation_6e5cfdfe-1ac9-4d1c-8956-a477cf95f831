import 'package:freezed_annotation/freezed_annotation.dart';

part 'conversation_model.freezed.dart';

@freezed
abstract class ConversationModel with _$ConversationModel {
  const factory ConversationModel({
    required String id,
    String? title,
    required String model,
    String? lastUsedModel, // Track the last model used in this conversation
    @Default(0) int tokenCount,
    required DateTime createdAt,
    required DateTime updatedAt,
    @Default(false) bool isPinned,
    String? folderId,
    @Default(0) int messageCount,
    String? lastMessagePreview,
    DateTime? lastMessageAt,
  }) = _ConversationModel;

  const ConversationModel._();

  String get displayTitle => title ?? 'New Chat';
  
  String get formattedDate {
    final now = DateTime.now();
    final difference = now.difference(updatedAt);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${updatedAt.day}/${updatedAt.month}/${updatedAt.year}';
    }
  }
}