// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'message_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_MessageMetadata _$MessageMetadataFromJson(Map<String, dynamic> json) =>
    _MessageMetadata(
      toolCalls: (json['toolCalls'] as List<dynamic>?)
          ?.map((e) => ToolCallMetadata.fromJson(e as Map<String, dynamic>))
          .toList(),
      finishReason: json['finishReason'] as String?,
      usage: json['usage'] as Map<String, dynamic>?,
      model: json['model'] as String?,
      extra: json['extra'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$MessageMetadataToJson(_MessageMetadata instance) =>
    <String, dynamic>{
      'toolCalls': instance.toolCalls,
      'finishReason': instance.finishReason,
      'usage': instance.usage,
      'model': instance.model,
      'extra': instance.extra,
    };

_ToolCallMetadata _$ToolCallMetadataFromJson(Map<String, dynamic> json) =>
    _ToolCallMetadata(
      id: json['id'] as String,
      name: json['name'] as String,
      arguments: json['arguments'] as Map<String, dynamic>,
      result: json['result'] as String?,
      error: json['error'] as String?,
      isExecuted: json['isExecuted'] as bool? ?? false,
      executedAt: json['executedAt'] == null
          ? null
          : DateTime.parse(json['executedAt'] as String),
      serverId: json['serverId'] as String?,
    );

Map<String, dynamic> _$ToolCallMetadataToJson(_ToolCallMetadata instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'arguments': instance.arguments,
      'result': instance.result,
      'error': instance.error,
      'isExecuted': instance.isExecuted,
      'executedAt': instance.executedAt?.toIso8601String(),
      'serverId': instance.serverId,
    };
