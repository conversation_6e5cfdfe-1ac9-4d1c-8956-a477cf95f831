// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'conversation_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ConversationModel {

 String get id; String? get title; String get model; String? get lastUsedModel;// Track the last model used in this conversation
 int get tokenCount; DateTime get createdAt; DateTime get updatedAt; bool get isPinned; String? get folderId; int get messageCount; String? get lastMessagePreview; DateTime? get lastMessageAt;
/// Create a copy of ConversationModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ConversationModelCopyWith<ConversationModel> get copyWith => _$ConversationModelCopyWithImpl<ConversationModel>(this as ConversationModel, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ConversationModel&&(identical(other.id, id) || other.id == id)&&(identical(other.title, title) || other.title == title)&&(identical(other.model, model) || other.model == model)&&(identical(other.lastUsedModel, lastUsedModel) || other.lastUsedModel == lastUsedModel)&&(identical(other.tokenCount, tokenCount) || other.tokenCount == tokenCount)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isPinned, isPinned) || other.isPinned == isPinned)&&(identical(other.folderId, folderId) || other.folderId == folderId)&&(identical(other.messageCount, messageCount) || other.messageCount == messageCount)&&(identical(other.lastMessagePreview, lastMessagePreview) || other.lastMessagePreview == lastMessagePreview)&&(identical(other.lastMessageAt, lastMessageAt) || other.lastMessageAt == lastMessageAt));
}


@override
int get hashCode => Object.hash(runtimeType,id,title,model,lastUsedModel,tokenCount,createdAt,updatedAt,isPinned,folderId,messageCount,lastMessagePreview,lastMessageAt);

@override
String toString() {
  return 'ConversationModel(id: $id, title: $title, model: $model, lastUsedModel: $lastUsedModel, tokenCount: $tokenCount, createdAt: $createdAt, updatedAt: $updatedAt, isPinned: $isPinned, folderId: $folderId, messageCount: $messageCount, lastMessagePreview: $lastMessagePreview, lastMessageAt: $lastMessageAt)';
}


}

/// @nodoc
abstract mixin class $ConversationModelCopyWith<$Res>  {
  factory $ConversationModelCopyWith(ConversationModel value, $Res Function(ConversationModel) _then) = _$ConversationModelCopyWithImpl;
@useResult
$Res call({
 String id, String? title, String model, String? lastUsedModel, int tokenCount, DateTime createdAt, DateTime updatedAt, bool isPinned, String? folderId, int messageCount, String? lastMessagePreview, DateTime? lastMessageAt
});




}
/// @nodoc
class _$ConversationModelCopyWithImpl<$Res>
    implements $ConversationModelCopyWith<$Res> {
  _$ConversationModelCopyWithImpl(this._self, this._then);

  final ConversationModel _self;
  final $Res Function(ConversationModel) _then;

/// Create a copy of ConversationModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? title = freezed,Object? model = null,Object? lastUsedModel = freezed,Object? tokenCount = null,Object? createdAt = null,Object? updatedAt = null,Object? isPinned = null,Object? folderId = freezed,Object? messageCount = null,Object? lastMessagePreview = freezed,Object? lastMessageAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,title: freezed == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String?,model: null == model ? _self.model : model // ignore: cast_nullable_to_non_nullable
as String,lastUsedModel: freezed == lastUsedModel ? _self.lastUsedModel : lastUsedModel // ignore: cast_nullable_to_non_nullable
as String?,tokenCount: null == tokenCount ? _self.tokenCount : tokenCount // ignore: cast_nullable_to_non_nullable
as int,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,isPinned: null == isPinned ? _self.isPinned : isPinned // ignore: cast_nullable_to_non_nullable
as bool,folderId: freezed == folderId ? _self.folderId : folderId // ignore: cast_nullable_to_non_nullable
as String?,messageCount: null == messageCount ? _self.messageCount : messageCount // ignore: cast_nullable_to_non_nullable
as int,lastMessagePreview: freezed == lastMessagePreview ? _self.lastMessagePreview : lastMessagePreview // ignore: cast_nullable_to_non_nullable
as String?,lastMessageAt: freezed == lastMessageAt ? _self.lastMessageAt : lastMessageAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [ConversationModel].
extension ConversationModelPatterns on ConversationModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ConversationModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ConversationModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ConversationModel value)  $default,){
final _that = this;
switch (_that) {
case _ConversationModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ConversationModel value)?  $default,){
final _that = this;
switch (_that) {
case _ConversationModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String? title,  String model,  String? lastUsedModel,  int tokenCount,  DateTime createdAt,  DateTime updatedAt,  bool isPinned,  String? folderId,  int messageCount,  String? lastMessagePreview,  DateTime? lastMessageAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ConversationModel() when $default != null:
return $default(_that.id,_that.title,_that.model,_that.lastUsedModel,_that.tokenCount,_that.createdAt,_that.updatedAt,_that.isPinned,_that.folderId,_that.messageCount,_that.lastMessagePreview,_that.lastMessageAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String? title,  String model,  String? lastUsedModel,  int tokenCount,  DateTime createdAt,  DateTime updatedAt,  bool isPinned,  String? folderId,  int messageCount,  String? lastMessagePreview,  DateTime? lastMessageAt)  $default,) {final _that = this;
switch (_that) {
case _ConversationModel():
return $default(_that.id,_that.title,_that.model,_that.lastUsedModel,_that.tokenCount,_that.createdAt,_that.updatedAt,_that.isPinned,_that.folderId,_that.messageCount,_that.lastMessagePreview,_that.lastMessageAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String? title,  String model,  String? lastUsedModel,  int tokenCount,  DateTime createdAt,  DateTime updatedAt,  bool isPinned,  String? folderId,  int messageCount,  String? lastMessagePreview,  DateTime? lastMessageAt)?  $default,) {final _that = this;
switch (_that) {
case _ConversationModel() when $default != null:
return $default(_that.id,_that.title,_that.model,_that.lastUsedModel,_that.tokenCount,_that.createdAt,_that.updatedAt,_that.isPinned,_that.folderId,_that.messageCount,_that.lastMessagePreview,_that.lastMessageAt);case _:
  return null;

}
}

}

/// @nodoc


class _ConversationModel extends ConversationModel {
  const _ConversationModel({required this.id, this.title, required this.model, this.lastUsedModel, this.tokenCount = 0, required this.createdAt, required this.updatedAt, this.isPinned = false, this.folderId, this.messageCount = 0, this.lastMessagePreview, this.lastMessageAt}): super._();
  

@override final  String id;
@override final  String? title;
@override final  String model;
@override final  String? lastUsedModel;
// Track the last model used in this conversation
@override@JsonKey() final  int tokenCount;
@override final  DateTime createdAt;
@override final  DateTime updatedAt;
@override@JsonKey() final  bool isPinned;
@override final  String? folderId;
@override@JsonKey() final  int messageCount;
@override final  String? lastMessagePreview;
@override final  DateTime? lastMessageAt;

/// Create a copy of ConversationModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ConversationModelCopyWith<_ConversationModel> get copyWith => __$ConversationModelCopyWithImpl<_ConversationModel>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ConversationModel&&(identical(other.id, id) || other.id == id)&&(identical(other.title, title) || other.title == title)&&(identical(other.model, model) || other.model == model)&&(identical(other.lastUsedModel, lastUsedModel) || other.lastUsedModel == lastUsedModel)&&(identical(other.tokenCount, tokenCount) || other.tokenCount == tokenCount)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isPinned, isPinned) || other.isPinned == isPinned)&&(identical(other.folderId, folderId) || other.folderId == folderId)&&(identical(other.messageCount, messageCount) || other.messageCount == messageCount)&&(identical(other.lastMessagePreview, lastMessagePreview) || other.lastMessagePreview == lastMessagePreview)&&(identical(other.lastMessageAt, lastMessageAt) || other.lastMessageAt == lastMessageAt));
}


@override
int get hashCode => Object.hash(runtimeType,id,title,model,lastUsedModel,tokenCount,createdAt,updatedAt,isPinned,folderId,messageCount,lastMessagePreview,lastMessageAt);

@override
String toString() {
  return 'ConversationModel(id: $id, title: $title, model: $model, lastUsedModel: $lastUsedModel, tokenCount: $tokenCount, createdAt: $createdAt, updatedAt: $updatedAt, isPinned: $isPinned, folderId: $folderId, messageCount: $messageCount, lastMessagePreview: $lastMessagePreview, lastMessageAt: $lastMessageAt)';
}


}

/// @nodoc
abstract mixin class _$ConversationModelCopyWith<$Res> implements $ConversationModelCopyWith<$Res> {
  factory _$ConversationModelCopyWith(_ConversationModel value, $Res Function(_ConversationModel) _then) = __$ConversationModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String? title, String model, String? lastUsedModel, int tokenCount, DateTime createdAt, DateTime updatedAt, bool isPinned, String? folderId, int messageCount, String? lastMessagePreview, DateTime? lastMessageAt
});




}
/// @nodoc
class __$ConversationModelCopyWithImpl<$Res>
    implements _$ConversationModelCopyWith<$Res> {
  __$ConversationModelCopyWithImpl(this._self, this._then);

  final _ConversationModel _self;
  final $Res Function(_ConversationModel) _then;

/// Create a copy of ConversationModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? title = freezed,Object? model = null,Object? lastUsedModel = freezed,Object? tokenCount = null,Object? createdAt = null,Object? updatedAt = null,Object? isPinned = null,Object? folderId = freezed,Object? messageCount = null,Object? lastMessagePreview = freezed,Object? lastMessageAt = freezed,}) {
  return _then(_ConversationModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,title: freezed == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String?,model: null == model ? _self.model : model // ignore: cast_nullable_to_non_nullable
as String,lastUsedModel: freezed == lastUsedModel ? _self.lastUsedModel : lastUsedModel // ignore: cast_nullable_to_non_nullable
as String?,tokenCount: null == tokenCount ? _self.tokenCount : tokenCount // ignore: cast_nullable_to_non_nullable
as int,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,isPinned: null == isPinned ? _self.isPinned : isPinned // ignore: cast_nullable_to_non_nullable
as bool,folderId: freezed == folderId ? _self.folderId : folderId // ignore: cast_nullable_to_non_nullable
as String?,messageCount: null == messageCount ? _self.messageCount : messageCount // ignore: cast_nullable_to_non_nullable
as int,lastMessagePreview: freezed == lastMessagePreview ? _self.lastMessagePreview : lastMessagePreview // ignore: cast_nullable_to_non_nullable
as String?,lastMessageAt: freezed == lastMessageAt ? _self.lastMessageAt : lastMessageAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}

// dart format on
