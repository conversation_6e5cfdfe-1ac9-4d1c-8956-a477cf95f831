import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../core/services/token_counter.dart';

class MessageInput extends StatefulWidget {
  final TextEditingController controller;
  final VoidCallback? onSend;
  final VoidCallback? onStop;
  final bool isStreaming;
  final bool enabled;
  final String? placeholder;
  final int? maxLines;
  final bool showTokenCount;

  const MessageInput({
    super.key,
    required this.controller,
    this.onSend,
    this.onStop,
    this.isStreaming = false,
    this.enabled = true,
    this.placeholder,
    this.maxLines = 5,
    this.showTokenCount = true,
  });

  @override
  State<MessageInput> createState() => _MessageInputState();
}

class _MessageInputState extends State<MessageInput> {
  bool _canSend = false;
  int _tokenCount = 0;

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onTextChanged);
    super.dispose();
  }

  void _onTextChanged() {
    final text = widget.controller.text.trim();
    final tokens = TokenCounter.estimateTokens(text);
    
    setState(() {
      _canSend = text.isNotEmpty && !widget.isStreaming;
      _tokenCount = tokens;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        border: Border(
          top: BorderSide(color: colorScheme.outline, width: 0.5),
        ),
      ),
      child: Column(
        children: [
          if (widget.showTokenCount && _tokenCount > 0) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${TokenCounter.formatTokenCount(_tokenCount)} tokens',
                    style: theme.textTheme.labelSmall,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
          ],
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Expanded(
                child: TextField(
                  controller: widget.controller,
                  enabled: widget.enabled,
                  maxLines: widget.maxLines,
                  minLines: 1,
                  textCapitalization: TextCapitalization.sentences,
                  decoration: InputDecoration(
                    hintText: widget.placeholder ?? 'Type a message...',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  onSubmitted: _canSend ? (_) => _handleSend() : null,
                  onChanged: (_) => _onTextChanged(),
                ),
              ),
              const SizedBox(width: 8),
              _buildActionButton(context),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    if (widget.isStreaming) {
      return FloatingActionButton.small(
        onPressed: widget.onStop,
        backgroundColor: colorScheme.error,
        foregroundColor: colorScheme.onError,
        child: const Icon(Icons.stop),
      );
    } else {
      return FloatingActionButton.small(
        onPressed: _canSend ? _handleSend : null,
        backgroundColor: _canSend ? colorScheme.primary : colorScheme.surfaceContainerHighest,
        foregroundColor: _canSend ? colorScheme.onPrimary : colorScheme.onSurfaceVariant,
        child: const Icon(Icons.send),
      );
    }
  }

  void _handleSend() {
    if (_canSend && widget.onSend != null) {
      widget.onSend!();
    }
  }
}

class AdvancedMessageInput extends StatefulWidget {
  final TextEditingController controller;
  final VoidCallback? onSend;
  final VoidCallback? onStop;
  final bool isStreaming;
  final bool enabled;
  final String? placeholder;
  final List<String> availableModels;
  final String? selectedModel;
  final ValueChanged<String?>? onModelChanged;
  final VoidCallback? onAttachFile;
  final bool showModelSelector;

  const AdvancedMessageInput({
    super.key,
    required this.controller,
    this.onSend,
    this.onStop,
    this.isStreaming = false,
    this.enabled = true,
    this.placeholder,
    this.availableModels = const [],
    this.selectedModel,
    this.onModelChanged,
    this.onAttachFile,
    this.showModelSelector = true,
  });

  @override
  State<AdvancedMessageInput> createState() => _AdvancedMessageInputState();
}

class _AdvancedMessageInputState extends State<AdvancedMessageInput> {
  bool _canSend = false;
  int _tokenCount = 0;

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onTextChanged);
    super.dispose();
  }

  void _onTextChanged() {
    final text = widget.controller.text.trim();
    final tokens = TokenCounter.estimateTokens(text);
    
    setState(() {
      _canSend = text.isNotEmpty && !widget.isStreaming;
      _tokenCount = tokens;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        border: Border(
          top: BorderSide(color: colorScheme.outline, width: 0.5),
        ),
      ),
      child: Column(
        children: [
          if (widget.showModelSelector && widget.availableModels.isNotEmpty) ...[
            Row(
              children: [
                Text(
                  'Model:',
                  style: theme.textTheme.labelMedium,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: DropdownButton<String>(
                    value: widget.selectedModel,
                    isExpanded: true,
                    underline: const SizedBox(),
                    items: widget.availableModels.map((model) {
                      return DropdownMenuItem(
                        value: model,
                        child: Text(
                          model,
                          style: theme.textTheme.bodySmall,
                          overflow: TextOverflow.ellipsis,
                        ),
                      );
                    }).toList(),
                    onChanged: widget.onModelChanged,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
          ],
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  if (widget.onAttachFile != null)
                    IconButton(
                      icon: const Icon(Icons.attach_file),
                      onPressed: widget.onAttachFile,
                      tooltip: 'Attach file',
                    ),
                ],
              ),
              if (_tokenCount > 0)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${TokenCounter.formatTokenCount(_tokenCount)} tokens',
                    style: theme.textTheme.labelSmall,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Expanded(
                child: TextField(
                  controller: widget.controller,
                  enabled: widget.enabled,
                  maxLines: 5,
                  minLines: 1,
                  textCapitalization: TextCapitalization.sentences,
                  decoration: InputDecoration(
                    hintText: widget.placeholder ?? 'Type a message...',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  onSubmitted: _canSend ? (_) => _handleSend() : null,
                ),
              ),
              const SizedBox(width: 8),
              _buildActionButton(context),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    if (widget.isStreaming) {
      return FloatingActionButton.small(
        onPressed: widget.onStop,
        backgroundColor: colorScheme.error,
        foregroundColor: colorScheme.onError,
        child: const Icon(Icons.stop),
      );
    } else {
      return FloatingActionButton.small(
        onPressed: _canSend ? _handleSend : null,
        backgroundColor: _canSend ? colorScheme.primary : colorScheme.surfaceContainerHighest,
        foregroundColor: _canSend ? colorScheme.onPrimary : colorScheme.onSurfaceVariant,
        child: const Icon(Icons.send),
      );
    }
  }

  void _handleSend() {
    if (_canSend && widget.onSend != null) {
      widget.onSend!();
    }
  }
}