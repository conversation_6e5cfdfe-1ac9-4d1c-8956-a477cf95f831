import 'package:flutter/material.dart';
import 'dart:convert';

import '../models/message_model.dart';

class ToolCallCard extends StatefulWidget {
  final ToolCallMetadata toolCall;

  const ToolCallCard({
    super.key,
    required this.toolCall,
  });

  @override
  State<ToolCallCard> createState() => _ToolCallCardState();
}

class _ToolCallCardState extends State<ToolCallCard> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Card(
      margin: EdgeInsets.zero,
      child: Column(
        children: [
          ListTile(
            dense: true,
            leading: Icon(
              Icons.extension,
              color: _getStatusColor(colorScheme),
              size: 20,
            ),
            title: Text(
              widget.toolCall.name,
              style: theme.textTheme.titleSmall,
            ),
            subtitle: Text(
              _getStatusText(),
              style: theme.textTheme.bodySmall?.copyWith(
                color: _getStatusColor(colorScheme),
              ),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildStatusIcon(colorScheme),
                IconButton(
                  icon: Icon(_isExpanded ? Icons.expand_less : Icons.expand_more),
                  onPressed: () {
                    setState(() {
                      _isExpanded = !_isExpanded;
                    });
                  },
                  constraints: const BoxConstraints.tightFor(
                    width: 32,
                    height: 32,
                  ),
                  padding: EdgeInsets.zero,
                ),
              ],
            ),
            onTap: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
          ),
          if (_isExpanded) ...[
            const Divider(height: 1),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (widget.toolCall.arguments.isNotEmpty) ...[
                    Text(
                      'Arguments:',
                      style: theme.textTheme.labelMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: colorScheme.surfaceVariant,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        _formatJson(widget.toolCall.arguments),
                        style: theme.textTheme.bodySmall?.copyWith(
                          fontFamily: 'JetBrains Mono',
                        ),
                      ),
                    ),
                    const SizedBox(height: 12),
                  ],
                  if (widget.toolCall.result != null) ...[
                    Text(
                      'Result:',
                      style: theme.textTheme.labelMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: colorScheme.surfaceVariant,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        widget.toolCall.result!,
                        style: theme.textTheme.bodySmall?.copyWith(
                          fontFamily: 'JetBrains Mono',
                        ),
                      ),
                    ),
                  ],
                  if (widget.toolCall.error != null) ...[
                    Text(
                      'Error:',
                      style: theme.textTheme.labelMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: colorScheme.error,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: colorScheme.errorContainer,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        widget.toolCall.error!,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: colorScheme.onErrorContainer,
                        ),
                      ),
                    ),
                  ],
                  if (widget.toolCall.executedAt != null) ...[
                    const SizedBox(height: 8),
                    Text(
                      'Executed at: ${_formatDateTime(widget.toolCall.executedAt!)}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                  if (widget.toolCall.serverId != null) ...[
                    Text(
                      'Server: ${widget.toolCall.serverId}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Color _getStatusColor(ColorScheme colorScheme) {
    if (widget.toolCall.error != null) {
      return colorScheme.error;
    } else if (widget.toolCall.isExecuted) {
      return colorScheme.primary;
    } else {
      return colorScheme.onSurfaceVariant;
    }
  }

  String _getStatusText() {
    if (widget.toolCall.error != null) {
      return 'Failed';
    } else if (widget.toolCall.isExecuted) {
      return 'Completed';
    } else {
      return 'Pending';
    }
  }

  Widget _buildStatusIcon(ColorScheme colorScheme) {
    if (widget.toolCall.error != null) {
      return Icon(
        Icons.error,
        color: colorScheme.error,
        size: 16,
      );
    } else if (widget.toolCall.isExecuted) {
      return Icon(
        Icons.check_circle,
        color: colorScheme.primary,
        size: 16,
      );
    } else {
      return SizedBox(
        width: 16,
        height: 16,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          color: colorScheme.onSurfaceVariant,
        ),
      );
    }
  }

  String _formatJson(Map<String, dynamic> json) {
    try {
      const encoder = JsonEncoder.withIndent('  ');
      return encoder.convert(json);
    } catch (e) {
      return json.toString();
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:'
           '${dateTime.minute.toString().padLeft(2, '0')}:'
           '${dateTime.second.toString().padLeft(2, '0')}';
  }
}