import 'package:flutter/material.dart';

import '../models/conversation_model.dart';
import '../../../core/services/token_counter.dart';

class ConversationTile extends StatelessWidget {
  final ConversationModel conversation;
  final VoidCallback? onTap;
  final VoidCallback? onDelete;
  final VoidCallback? onPin;
  final VoidCallback? onRename;
  final VoidCallback? onDuplicate;

  const ConversationTile({
    super.key,
    required this.conversation,
    this.onTap,
    this.onDelete,
    this.onPin,
    this.onRename,
    this.onDuplicate,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      child: ListTile(
        leading: _buildLeading(context),
        title: _buildTitle(context),
        subtitle: _buildSubtitle(context),
        trailing: _buildTrailing(context),
        onTap: onTap,
        onLongPress: () => _showContextMenu(context),
      ),
    );
  }

  Widget _buildLeading(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Stack(
      children: [
        CircleAvatar(
          backgroundColor: colorScheme.surfaceVariant,
          child: Icon(
            Icons.chat_bubble_outline,
            color: colorScheme.onSurfaceVariant,
            size: 20,
          ),
        ),
        if (conversation.isPinned)
          Positioned(
            right: 0,
            top: 0,
            child: Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: colorScheme.primary,
                shape: BoxShape.circle,
                border: Border.all(
                  color: colorScheme.surface,
                  width: 1,
                ),
              ),
              child: Icon(
                Icons.push_pin,
                color: colorScheme.onPrimary,
                size: 8,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildTitle(BuildContext context) {
    final theme = Theme.of(context);
    
    return Row(
      children: [
        Expanded(
          child: Text(
            conversation.displayTitle,
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        const SizedBox(width: 8),
        _buildModelChip(context),
      ],
    );
  }

  Widget _buildSubtitle(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (conversation.lastMessagePreview != null) ...[
          const SizedBox(height: 2),
          Text(
            conversation.lastMessagePreview!,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
        const SizedBox(height: 4),
        Row(
          children: [
            Text(
              conversation.formattedDate,
              style: theme.textTheme.labelSmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            const Spacer(),
            if (conversation.messageCount > 0) ...[
              Icon(
                Icons.message,
                size: 12,
                color: theme.colorScheme.onSurfaceVariant,
              ),
              const SizedBox(width: 2),
              Text(
                '${conversation.messageCount}',
                style: theme.textTheme.labelSmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
            if (conversation.tokenCount > 0) ...[
              const SizedBox(width: 8),
              Icon(
                Icons.token,
                size: 12,
                color: theme.colorScheme.onSurfaceVariant,
              ),
              const SizedBox(width: 2),
              Text(
                TokenCounter.formatTokenCount(conversation.tokenCount),
                style: theme.textTheme.labelSmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ],
        ),
      ],
    );
  }

  Widget _buildModelChip(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Extract model name from full model string (e.g., "openai/gpt-4" -> "GPT-4")
    final modelName = _formatModelName(conversation.model);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        modelName,
        style: theme.textTheme.labelSmall?.copyWith(
          color: colorScheme.onSurfaceVariant,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildTrailing(BuildContext context) {
    return PopupMenuButton<String>(
      icon: const Icon(Icons.more_vert, size: 16),
      onSelected: (action) => _handleAction(context, action),
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 'pin',
          child: Row(
            children: [
              Icon(conversation.isPinned ? Icons.push_pin_outlined : Icons.push_pin),
              const SizedBox(width: 8),
              Text(conversation.isPinned ? 'Unpin' : 'Pin'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'rename',
          child: Row(
            children: [
              Icon(Icons.edit),
              SizedBox(width: 8),
              Text('Rename'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'duplicate',
          child: Row(
            children: [
              Icon(Icons.copy),
              SizedBox(width: 8),
              Text('Duplicate'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'export',
          child: Row(
            children: [
              Icon(Icons.download),
              SizedBox(width: 8),
              Text('Export'),
            ],
          ),
        ),
        const PopupMenuDivider(),
        const PopupMenuItem(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete, color: Colors.red),
              SizedBox(width: 8),
              Text('Delete', style: TextStyle(color: Colors.red)),
            ],
          ),
        ),
      ],
    );
  }

  String _formatModelName(String model) {
    // Extract and format model name for display
    final parts = model.split('/');
    final modelName = parts.length > 1 ? parts[1] : model;
    
    // Common model name mappings
    final modelMappings = {
      'gpt-4o': 'GPT-4o',
      'gpt-4o-mini': 'GPT-4o Mini',
      'gpt-4': 'GPT-4',
      'gpt-3.5-turbo': 'GPT-3.5',
      'claude-3-5-sonnet': 'Claude 3.5',
      'claude-3-haiku': 'Claude 3 Haiku',
      'gemini-pro': 'Gemini Pro',
    };

    return modelMappings[modelName] ?? modelName.toUpperCase();
  }

  void _handleAction(BuildContext context, String action) {
    switch (action) {
      case 'pin':
        onPin?.call();
        break;
      case 'rename':
        onRename?.call();
        break;
      case 'duplicate':
        onDuplicate?.call();
        break;
      case 'export':
        _exportConversation(context);
        break;
      case 'delete':
        onDelete?.call();
        break;
    }
  }

  void _showContextMenu(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(conversation.isPinned ? Icons.push_pin_outlined : Icons.push_pin),
              title: Text(conversation.isPinned ? 'Unpin' : 'Pin'),
              onTap: () {
                Navigator.pop(context);
                onPin?.call();
              },
            ),
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('Rename'),
              onTap: () {
                Navigator.pop(context);
                onRename?.call();
              },
            ),
            ListTile(
              leading: const Icon(Icons.copy),
              title: const Text('Duplicate'),
              onTap: () {
                Navigator.pop(context);
                onDuplicate?.call();
              },
            ),
            ListTile(
              leading: const Icon(Icons.download),
              title: const Text('Export'),
              onTap: () {
                Navigator.pop(context);
                _exportConversation(context);
              },
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('Delete', style: TextStyle(color: Colors.red)),
              onTap: () {
                Navigator.pop(context);
                onDelete?.call();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _exportConversation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Conversation'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Choose export format:'),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.article),
              title: const Text('Markdown (.md)'),
              onTap: () {
                Navigator.pop(context);
                _performExport(context, 'markdown');
              },
            ),
            ListTile(
              leading: const Icon(Icons.description),
              title: const Text('JSON (.json)'),
              onTap: () {
                Navigator.pop(context);
                _performExport(context, 'json');
              },
            ),
            ListTile(
              leading: const Icon(Icons.text_snippet),
              title: const Text('Plain Text (.txt)'),
              onTap: () {
                Navigator.pop(context);
                _performExport(context, 'txt');
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _performExport(BuildContext context, String format) {
    final title = conversation.title ?? 'Untitled';
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Exported "$title" as $format file')),
    );
  }
}