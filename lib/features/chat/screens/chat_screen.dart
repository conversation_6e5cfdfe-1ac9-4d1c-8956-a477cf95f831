import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../providers/chat_provider.dart';
import '../providers/conversation_list_provider.dart';
import '../models/conversation_model.dart';
import '../widgets/message_bubble.dart';
import '../../../shared/widgets/loading_overlay.dart';
import '../../../core/api/openrouter/models_api.dart';
import '../../settings/providers/settings_provider.dart';
import '../../models/providers/selected_models_provider.dart';

class ChatScreen extends ConsumerStatefulWidget {
  final String conversationId;

  const ChatScreen({super.key, required this.conversationId});

  @override
  ConsumerState<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends ConsumerState<ChatScreen> {
  final _messageController = TextEditingController();
  final _scrollController = ScrollController();
  final _focusNode = FocusNode();
  bool _isComposing = false;
  String? _currentModel;

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    // Check configuration when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkConfiguration();
    });
  }

  /// Checks if user has proper API configuration and selected models
  void _checkConfiguration() async {
    final settings = await ref.read(settingsProvider.future);
    final selectedModels = await ref.read(selectedModelsProvider.future);
    final apiKey = settings['openrouter_api_key'] as String?;
    
    if (apiKey == null || apiKey.trim().isEmpty) {
      _showConfigurationDialog('API Key Required', 
        'Please configure your OpenRouter API key to start chatting.');
      return;
    }
    
    if (selectedModels.isEmpty) {
      _showConfigurationDialog('No Models Selected', 
        'Please select at least one model to start chatting.');
      return;
    }
  }

  /// Shows configuration dialog and navigates to settings
  void _showConfigurationDialog(String title, String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.go('/settings/api');
            },
            child: const Text('Configure'),
          ),
        ],
      ),
    );
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final conversationAsync = ref.watch(conversationProvider(widget.conversationId));
    final messagesAsync = ref.watch(chatProvider(widget.conversationId));
    final chatNotifier = ref.read(chatProvider(widget.conversationId).notifier);
    
    // Initialize current model from conversation's last used model
    conversationAsync.whenData((conversation) {
      if (conversation != null && _currentModel == null) {
        _currentModel = conversation.lastUsedModel ?? conversation.model;
      }
    });

    return Scaffold(
      appBar: _buildAppBar(context, conversationAsync),
      drawer: _buildConversationDrawer(),
      body: Column(
        children: [
          Expanded(
            child: messagesAsync.when(
              data: (messages) => _buildMessageArea(messages),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => _buildErrorState(error),
            ),
          ),
          _buildInputArea(theme, chatNotifier),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(
    BuildContext context,
    AsyncValue<ConversationModel?> conversationAsync,
  ) {
    final theme = Theme.of(context);
    
    return AppBar(
      leading: Builder(
        builder: (context) => IconButton(
          icon: const Icon(Icons.menu),
          onPressed: () => Scaffold.of(context).openDrawer(),
        ),
      ),
      title: conversationAsync.when(
        data: (conversation) => GestureDetector(
          onTap: () => _showModelSelectionDialog(conversation),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildModelDisplay(conversation),
              const SizedBox(width: 4),
              Icon(
                Icons.keyboard_arrow_down,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
        loading: () => const Text('Loading...'),
        error: (_, __) => const Text('Error'),
      ),
      centerTitle: false,
      actions: [
        // New Chat Button
        IconButton(
          onPressed: _createNewChat,
          icon: Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.add,
              size: 18,
              color: theme.colorScheme.onPrimary,
            ),
          ),
        ),
        const SizedBox(width: 8),
      ],
    );
  }



  Widget _buildModelDisplay(ConversationModel? conversation) {
    final theme = Theme.of(context);
    final modelId = conversation?.lastUsedModel ?? conversation?.model ?? '';
    
    if (modelId.isEmpty) {
      return Text(
        'No Model Selected',
        style: theme.textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      );
    }
    
    final parts = modelId.split('/');
    final provider = parts.isNotEmpty ? parts[0] : '';
    final modelName = parts.length > 1 ? parts[1] : modelId;
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (provider.isNotEmpty) ...[
          Text(
            provider.toLowerCase(),
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w400,
            ),
          ),
          Text(
            ' • ',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
        Text(
          _formatModelName(modelName),
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w700,
          ),
        ),
      ],
    );
  }
  
  String _formatModelName(String modelName) {
    // Convert model names to a more readable format
    return modelName
        .replaceAll('-', ' ')
        .split(' ')
        .map((word) => word.isNotEmpty ? word[0].toUpperCase() + word.substring(1) : '')
        .join(' ');
  }
  
  void _showModelSelectionDialog(ConversationModel? conversation) {
    if (conversation == null) return;
    
    showDialog(
      context: context,
      builder: (context) => _ModelSelectionDialog(
        conversationId: conversation.id,
        currentModelId: conversation.lastUsedModel ?? conversation.model,
        onModelSelected: (modelId) {
          setState(() {
            _currentModel = modelId;
          });
          final conversationNotifier = ref.read(conversationListProvider.notifier);
          conversationNotifier.updateLastUsedModel(conversation.id, modelId);
        },
      ),
    );
  }

  Widget _buildTokenCounter() {
    final theme = Theme.of(context);
    // Mock token count - in real app, calculate from messages
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        '1.2k/128k',
        style: theme.textTheme.bodySmall?.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildConversationDrawer() {
    final theme = Theme.of(context);
    return Drawer(
      child: Column(
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              color: theme.colorScheme.primary,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text(
                  'Otlo Chat',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'AI-powered conversations',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          ListTile(
            leading: const Icon(Icons.add),
            title: const Text('New Chat'),
            onTap: () {
              Navigator.of(context).pop();
              _createNewChat();
            },
          ),
          ListTile(
            leading: const Icon(Icons.psychology),
            title: const Text('Model Browser'),
            onTap: () {
              Navigator.of(context).pop();
              context.push('/models');
            },
          ),
          ListTile(
            leading: const Icon(Icons.settings),
            title: const Text('Settings'),
            onTap: () {
              Navigator.of(context).pop();
              context.push('/settings');
            },
          ),
          const Divider(),
          Expanded(
            child: _buildConversationList(),
          ),
        ],
      ),
    );
  }

  Widget _buildConversationList() {
    final conversationsAsync = ref.watch(conversationListProvider);
    
    return conversationsAsync.when(
      data: (conversations) => ListView.builder(
        itemCount: conversations.length,
        itemBuilder: (context, index) {
          final conv = conversations[index];
          final isActive = conv.id == widget.conversationId;
          
          return ListTile(
            selected: isActive,
            leading: Icon(
              conv.isPinned ? Icons.push_pin : Icons.chat_bubble_outline,
              size: 20,
            ),
            title: Text(
              conv.title ?? 'Untitled',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            subtitle: conv.lastMessagePreview != null
                ? Text(
                    conv.lastMessagePreview!,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  )
                : null,
            trailing: Text(
              '${conv.messageCount}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            onTap: () {
              Navigator.of(context).pop();
              if (!isActive) {
                context.go('/chat/${conv.id}');
              }
            },
          );
        },
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (_, __) => const Center(child: Text('Error loading conversations')),
    );
  }

  Widget _buildMessageArea(List<dynamic> messages) {
    if (messages.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      itemCount: messages.length,
      itemBuilder: (context, index) {
        final message = messages[index];
        
        // Auto-scroll to bottom when new messages arrive
        if (index == messages.length - 1) {
          WidgetsBinding.instance.addPostFrameCallback((_) => _scrollToBottom());
        }
        
        return Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: MessageBubble(
            message: message,
            onEdit: message.isUser ? () => _editMessage(message.id, message.content) : null,
            onRegenerate: message.isAssistant ? () => _regenerateMessage(message.id) : null,
            onDelete: () => _deleteMessage(message.id),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const SizedBox(height: 32),
          const Text(
            'What would you like to discuss?',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 24),
          _buildSuggestedPrompts(),
        ],
      ),
    );
  }

  Widget _buildSuggestedPrompts() {
    final prompts = [
      'Explain quantum computing',
      'Write Python code for...',
      'Analyze this data...',
      'Help me debug...',
    ];
    
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: prompts.map((prompt) {
        return ActionChip(
          label: Text(prompt),
          onPressed: () {
            _messageController.text = prompt;
            _focusNode.requestFocus();
          },
        );
      }).toList(),
    );
  }

  Widget _buildInputArea(ThemeData theme, chatNotifier) {
    final isStreaming = chatNotifier.isStreaming;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Editable text input area
          TextField(
            controller: _messageController,
            focusNode: _focusNode,
            maxLines: null,
            minLines: 1,
            textCapitalization: TextCapitalization.sentences,
            decoration: InputDecoration(
              hintText: 'Type a message...',
              border: InputBorder.none,
              contentPadding: EdgeInsets.zero,
              hintStyle: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
            ),
            style: theme.textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w500,
            ),
            onChanged: (text) {
              setState(() {
                _isComposing = text.trim().isNotEmpty;
              });
            },
            onSubmitted: _isComposing && !isStreaming ? (_) => _sendMessage() : null,
          ),
          const SizedBox(height: 12),
          
          // Input button row
          Row(
            children: [
              // Add attachment button
              _buildInputButton(
                theme,
                icon: Icons.add,
                onTap: () => _showAttachmentOptions(),
                tooltip: 'Add attachment',
              ),
              const SizedBox(width: 8),
              
              // Menu/Filter button
              _buildInputButton(
                theme,
                icon: Icons.menu,
                onTap: () => _showFilterOptions(),
                tooltip: 'Filter options',
              ),
              const SizedBox(width: 8),
              
              // Extended thinking toggle
              _buildInputButton(
                theme,
                icon: Icons.psychology,
                onTap: () => _toggleExtendedThinking(),
                tooltip: 'Extended thinking',
                isActive: false, // TODO: Track extended thinking state
              ),
              const SizedBox(width: 8),
              
              // Research mode chip
              _buildResearchChip(theme),
              
              // Spacer
              Expanded(child: Container()),
              
              // Voice button
              _buildInputButton(
                theme,
                icon: Icons.mic,
                onTap: () => _startVoiceInput(),
                tooltip: 'Voice input',
              ),
              const SizedBox(width: 8),
              
              // Send/Stop button
              if (_isComposing && !isStreaming)
                _buildInputButton(
                  theme,
                  icon: Icons.send,
                  onTap: _sendMessage,
                  tooltip: 'Send message',
                  isPrimary: true,
                )
              else if (isStreaming)
                _buildInputButton(
                  theme,
                  icon: Icons.stop,
                  onTap: _stopStreaming,
                  tooltip: 'Stop',
                  isPrimary: true,
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInputButton(
    ThemeData theme, {
    required IconData icon,
    required VoidCallback onTap,
    required String tooltip,
    bool isActive = false,
    bool isPrimary = false,
  }) {
    Color backgroundColor;
    Color iconColor;
    
    if (isPrimary) {
      backgroundColor = theme.colorScheme.primary;
      iconColor = theme.colorScheme.onPrimary;
    } else if (isActive) {
      backgroundColor = theme.colorScheme.primaryContainer;
      iconColor = theme.colorScheme.onPrimaryContainer;
    } else {
      backgroundColor = theme.colorScheme.surfaceVariant;
      iconColor = theme.colorScheme.onSurfaceVariant;
    }
    
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20),
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: backgroundColor,
          shape: BoxShape.circle,
        ),
        child: Icon(
          icon,
          size: 20,
          color: iconColor,
        ),
      ),
    );
  }
  
  Widget _buildResearchChip(ThemeData theme) {
    return InkWell(
      onTap: () => _toggleResearchMode(),
      borderRadius: BorderRadius.circular(20),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceVariant,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.search,
              size: 16,
              color: theme.colorScheme.onSurfaceVariant,
            ),
            const SizedBox(width: 4),
            Text(
              'Research',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  // Placeholder methods for new functionality
  void _showAttachmentOptions() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Attachment options not implemented yet'),
        duration: Duration(seconds: 2),
      ),
    );
  }
  
  void _showFilterOptions() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Filter options not implemented yet'),
        duration: Duration(seconds: 2),
      ),
    );
  }
  
  void _toggleExtendedThinking() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Extended thinking toggle not implemented yet'),
        duration: Duration(seconds: 2),
      ),
    );
  }
  
  void _toggleResearchMode() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Research mode toggle not implemented yet'),
        duration: Duration(seconds: 2),
      ),
    );
  }
  
  void _startVoiceInput() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Voice input not implemented yet'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  Widget _buildSendButton(ThemeData theme, bool isStreaming) {
    if (isStreaming) {
      return FloatingActionButton.small(
        onPressed: _stopStreaming,
        backgroundColor: theme.colorScheme.error,
        child: const Icon(Icons.stop),
      );
    }
    
    return FloatingActionButton.small(
      onPressed: _isComposing ? _sendMessage : null,
      backgroundColor: _isComposing 
          ? theme.colorScheme.primary 
          : theme.colorScheme.surfaceVariant,
      child: Icon(
        Icons.send,
        color: _isComposing 
            ? theme.colorScheme.onPrimary 
            : theme.colorScheme.onSurfaceVariant,
      ),
    );
  }

  Widget _buildErrorState(dynamic error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 48),
          const SizedBox(height: 16),
          Text('Error loading messages: $error'),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => ref.invalidate(chatProvider(widget.conversationId)),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  void _sendMessage() async {
    final content = _messageController.text.trim();
    if (content.isEmpty) return;

    // Check if we have a current model selected
    if (_currentModel == null) {
      final selectedModels = await ref.read(selectedModelsProvider.future);
      if (selectedModels.isEmpty) {
        _showConfigurationDialog('No Models Selected', 
          'Please select at least one model to start chatting.');
        return;
      }
      _currentModel = selectedModels.first;
    }

    _messageController.clear();
    setState(() {
      _isComposing = false;
    });
    
    final chatNotifier = ref.read(chatProvider(widget.conversationId).notifier);
    
    chatNotifier.sendMessage(
      content: content,
      model: _currentModel!,
    );
  }

  void _stopStreaming() {
    final chatNotifier = ref.read(chatProvider(widget.conversationId).notifier);
    chatNotifier.stopStreaming();
  }

  void _editMessage(String messageId, String currentContent) {
    showDialog(
      context: context,
      builder: (context) => _EditMessageDialog(
        initialContent: currentContent,
        onSave: (newContent) {
          final chatNotifier = ref.read(chatProvider(widget.conversationId).notifier);
          chatNotifier.editMessage(messageId, newContent);
        },
      ),
    );
  }

  void _regenerateMessage(String messageId) {
    final chatNotifier = ref.read(chatProvider(widget.conversationId).notifier);
    chatNotifier.regenerateMessage(messageId);
  }

  void _deleteMessage(String messageId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Message'),
        content: const Text('Are you sure you want to delete this message?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              final chatNotifier = ref.read(chatProvider(widget.conversationId).notifier);
              chatNotifier.deleteMessage(messageId);
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _editTitle(ConversationModel? conversation) async {
    if (conversation == null) return;
    
    final controller = TextEditingController(text: conversation.title);
    final newTitle = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Title'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'Conversation Title',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(controller.text),
            child: const Text('Save'),
          ),
        ],
      ),
    );

    if (newTitle != null && newTitle.trim().isNotEmpty) {
      final conversationNotifier = ref.read(conversationListProvider.notifier);
      await conversationNotifier.updateConversationTitle(
        widget.conversationId,
        newTitle.trim(),
      );
    }
  }

  void _showModelSwitchMessage(OpenRouterModel model) async {
    final chatNotifier = ref.read(chatProvider(widget.conversationId).notifier);
    final conversationNotifier = ref.read(conversationListProvider.notifier);
    
    // Update the conversation's last used model
    await conversationNotifier.updateLastUsedModel(widget.conversationId, model.id);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Switched to ${_getModelDisplayName(model)}'),
        duration: const Duration(seconds: 2),
      ),
    );
  }
  
  String _getModelDisplayName(OpenRouterModel model) {
    // Simplify model names for better display
    if (model.name.length > 25) {
      return model.name.split(' ').take(3).join(' ');
    }
    return model.name;
  }

  void _handleMenuAction(String action) {
    final chatNotifier = ref.read(chatProvider(widget.conversationId).notifier);
    
    switch (action) {
      case 'fork':
        _showForkDialog();
        break;
      case 'export':
        _showExportDialog();
        break;
      case 'clear':
        _showClearDialog(chatNotifier);
        break;
      case 'delete':
        _showDeleteDialog();
        break;
    }
  }

  void _showClearDialog(chatNotifier) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Chat'),
        content: const Text('Are you sure you want to clear all messages?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              chatNotifier.clearConversation();
            },
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Conversation'),
        content: const Text('Are you sure you want to delete this conversation? This cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final conversationNotifier = ref.read(conversationListProvider.notifier);
              await conversationNotifier.deleteConversation(widget.conversationId);
              if (mounted) {
                context.go('/');
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _createNewChat() async {
    final selectedModels = await ref.read(selectedModelsProvider.future);
    if (selectedModels.isEmpty) {
      _showConfigurationDialog('No Models Selected', 
        'Please select at least one model to start chatting.');
      return;
    }

    final conversationNotifier = ref.read(conversationListProvider.notifier);
    final modelId = _currentModel ?? selectedModels.first;
    
    try {
      final conversation = await conversationNotifier.createConversation(
        model: modelId,
        title: null, // Let it auto-generate
      );
      if (mounted) {
        context.go('/chat/${conversation.id}');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error creating chat: $e')),
        );
      }
    }
  }

  void _showForkDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Fork Conversation'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Create a new conversation starting from this point.'),
            const SizedBox(height: 16),
            const TextField(
              decoration: InputDecoration(
                labelText: 'New Conversation Title (optional)',
                hintText: 'Auto-generated if empty',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'All messages up to this point will be copied to the new conversation.',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Conversation forked successfully')),
              );
            },
            child: const Text('Fork'),
          ),
        ],
      ),
    );
  }

  void _showExportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Conversation'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Choose export format:'),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.article),
              title: const Text('Markdown (.md)'),
              subtitle: const Text('Readable format with formatting'),
              onTap: () {
                Navigator.pop(context);
                _performExport('markdown');
              },
            ),
            ListTile(
              leading: const Icon(Icons.description),
              title: const Text('JSON (.json)'),
              subtitle: const Text('Structured data format'),
              onTap: () {
                Navigator.pop(context);
                _performExport('json');
              },
            ),
            ListTile(
              leading: const Icon(Icons.text_snippet),
              title: const Text('Plain Text (.txt)'),
              subtitle: const Text('Simple text format'),
              onTap: () {
                Navigator.pop(context);
                _performExport('txt');
              },
            ),
            ListTile(
              leading: const Icon(Icons.share),
              title: const Text('Share Link'),
              subtitle: const Text('Generate shareable link'),
              onTap: () {
                Navigator.pop(context);
                _performExport('link');
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _performExport(String format) {
    final message = switch (format) {
      'markdown' => 'Conversation exported as Markdown file',
      'json' => 'Conversation exported as JSON file',
      'txt' => 'Conversation exported as text file',
      'link' => 'Shareable link generated and copied to clipboard',
      _ => 'Conversation exported successfully',
    };
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }
}

class _ModelSelectionDialog extends ConsumerWidget {
  final String conversationId;
  final String? currentModelId;
  final void Function(String) onModelSelected;

  const _ModelSelectionDialog({
    required this.conversationId,
    required this.currentModelId,
    required this.onModelSelected,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final selectedModelsAsync = ref.watch(selectedModelsProvider);

    return AlertDialog(
      title: const Text('Switch Model'),
      content: SizedBox(
        width: double.maxFinite,
        child: selectedModelsAsync.when(
          data: (modelIds) {
            if (modelIds.isEmpty) {
              return const Text('No models selected. Please configure models in settings.');
            }
            
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: modelIds.map((modelId) {
                final isSelected = modelId == currentModelId;
                final parts = modelId.split('/');
                final provider = parts.isNotEmpty ? parts[0] : '';
                final modelName = parts.length > 1 ? parts[1] : modelId;
                
                return ListTile(
                  title: Text(
                    _formatModelName(modelName),
                    style: theme.textTheme.bodyLarge?.copyWith(
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                    ),
                  ),
                  subtitle: provider.isNotEmpty ? Text(provider.toLowerCase()) : null,
                  trailing: isSelected ? Icon(
                    Icons.check_circle,
                    color: theme.colorScheme.primary,
                  ) : null,
                  onTap: () {
                    Navigator.of(context).pop();
                    if (!isSelected) {
                      onModelSelected(modelId);
                    }
                  },
                );
              }).toList(),
            );
          },
          loading: () => const SizedBox(
            height: 100,
            child: Center(child: CircularProgressIndicator()),
          ),
          error: (error, _) => Text('Error loading models: $error'),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
            // Navigate to model settings
            context.push('/models');
          },
          child: const Text('Manage Models'),
        ),
      ],
    );
  }
  
  String _formatModelName(String modelName) {
    return modelName
        .replaceAll('-', ' ')
        .split(' ')
        .map((word) => word.isNotEmpty ? word[0].toUpperCase() + word.substring(1) : '')
        .join(' ');
  }
}

class _EditMessageDialog extends StatefulWidget {
  final String initialContent;
  final void Function(String) onSave;

  const _EditMessageDialog({
    required this.initialContent,
    required this.onSave,
  });

  @override
  State<_EditMessageDialog> createState() => _EditMessageDialogState();
}

class _EditMessageDialogState extends State<_EditMessageDialog> {
  late final TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialContent);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Edit Message'),
      content: TextField(
        controller: _controller,
        maxLines: 5,
        decoration: const InputDecoration(
          hintText: 'Enter your message...',
          border: OutlineInputBorder(),
        ),
        autofocus: true,
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () {
            widget.onSave(_controller.text);
            Navigator.of(context).pop();
          },
          child: const Text('Save'),
        ),
      ],
    );
  }
}