// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tool_registry_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

/// Global tool registry instance
/// This provides access to all MCP tools from connected servers
@ProviderFor(ToolRegistryNotifier)
const toolRegistryNotifierProvider = ToolRegistryNotifierProvider._();

/// Global tool registry instance
/// This provides access to all MCP tools from connected servers
final class ToolRegistryNotifierProvider
    extends $NotifierProvider<ToolRegistryNotifier, ToolRegistry> {
  /// Global tool registry instance
  /// This provides access to all MCP tools from connected servers
  const ToolRegistryNotifierProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'toolRegistryNotifierProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$toolRegistryNotifierHash();

  @$internal
  @override
  ToolRegistryNotifier create() => ToolRegistryNotifier();

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(ToolRegistry value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<ToolRegistry>(value),
    );
  }
}

String _$toolRegistryNotifierHash() =>
    r'2cbfd8500199fc58b32a90af1e35fa05bdb6fce3';

abstract class _$ToolRegistryNotifier extends $Notifier<ToolRegistry> {
  ToolRegistry build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<ToolRegistry, ToolRegistry>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<ToolRegistry, ToolRegistry>,
              ToolRegistry,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

/// Provider for accessing the tool registry
@ProviderFor(toolRegistry)
const toolRegistryProvider = ToolRegistryProvider._();

/// Provider for accessing the tool registry
final class ToolRegistryProvider
    extends $FunctionalProvider<ToolRegistry, ToolRegistry, ToolRegistry>
    with $Provider<ToolRegistry> {
  /// Provider for accessing the tool registry
  const ToolRegistryProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'toolRegistryProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$toolRegistryHash();

  @$internal
  @override
  $ProviderElement<ToolRegistry> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  ToolRegistry create(Ref ref) {
    return toolRegistry(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(ToolRegistry value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<ToolRegistry>(value),
    );
  }
}

String _$toolRegistryHash() => r'83dce266f9938a4853487a6ec719f9036378ae91';

/// Provider for getting all available tools
@ProviderFor(availableTools)
const availableToolsProvider = AvailableToolsProvider._();

/// Provider for getting all available tools
final class AvailableToolsProvider
    extends $FunctionalProvider<List<McpTool>, List<McpTool>, List<McpTool>>
    with $Provider<List<McpTool>> {
  /// Provider for getting all available tools
  const AvailableToolsProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'availableToolsProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$availableToolsHash();

  @$internal
  @override
  $ProviderElement<List<McpTool>> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  List<McpTool> create(Ref ref) {
    return availableTools(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(List<McpTool> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<List<McpTool>>(value),
    );
  }
}

String _$availableToolsHash() => r'30d94227f01a641817ed95c494762fef6461c3fc';

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
