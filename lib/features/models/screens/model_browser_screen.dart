import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/api/openrouter/models_api.dart';
import '../providers/models_provider.dart';

class ModelBrowserScreen extends ConsumerStatefulWidget {
  const ModelBrowserScreen({super.key});

  @override
  ConsumerState<ModelBrowserScreen> createState() => _ModelBrowserScreenState();
}

class _ModelBrowserScreenState extends ConsumerState<ModelBrowserScreen> {
  final _searchController = TextEditingController();
  String _searchQuery = '';
  final Set<String> _activeFilters = {};

  final List<FilterOption> _filterOptions = [
    FilterOption('vision', 'Vision', Icons.visibility),
    FilterOption('32k+', '32k+', Icons.memory),
    FilterOption('128k+', '128k+', Icons.storage),
    FilterOption('function_calling', 'Function Calling', Icons.functions),
    FilterOption('under_5', 'Under \$5/M', Icons.attach_money),
  ];

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final activeModelsAsync = ref.watch(activeModelsProvider);
    final searchResults = ref.watch(
      modelSearchProvider(_searchQuery, _activeFilters.toList()),
    );

    return Scaffold(
      appBar: AppBar(
        title: const Text('Model Browser'),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(120),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: TextField(
                  controller: _searchController,
                  decoration: const InputDecoration(
                    hintText: 'Search models...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
              ),
              SizedBox(
                height: 50,
                child: ListView(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  children: _filterOptions.map((filter) {
                    final isActive = _activeFilters.contains(filter.key);
                    return Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: FilterChip(
                        label: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(filter.icon, size: 16),
                            const SizedBox(width: 4),
                            Text(filter.label),
                          ],
                        ),
                        selected: isActive,
                        onSelected: (selected) {
                          setState(() {
                            if (selected) {
                              _activeFilters.add(filter.key);
                            } else {
                              _activeFilters.remove(filter.key);
                            }
                          });
                        },
                      ),
                    );
                  }).toList(),
                ),
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
      body: Column(
        children: [
          // Active Models Section
          activeModelsAsync.when(
            data: (activeModels) => activeModels.isNotEmpty
                ? _buildActiveModelsSection(activeModels)
                : const SizedBox(),
            loading: () => const SizedBox(),
            error: (_, __) => const SizedBox(),
          ),

          // Available Models
          Expanded(child: _buildModelsList(searchResults)),
        ],
      ),
    );
  }

  Widget _buildActiveModelsSection(List<ActiveModel> activeModels) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.star, color: theme.colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Active Models (${activeModels.length}/10)',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => _showActiveModelsManager(),
                  child: const Text('Manage'),
                ),
              ],
            ),
            const SizedBox(height: 12),
            SizedBox(
              height: 120,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: activeModels.length,
                itemBuilder: (context, index) {
                  final activeModel = activeModels[index];
                  return _buildActiveModelCard(activeModel, index == 0);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActiveModelCard(ActiveModel activeModel, bool isDefault) {
    final theme = Theme.of(context);
    final model = activeModel.model;

    return Container(
      width: 200,
      margin: const EdgeInsets.only(right: 12),
      child: Card(
        color: isDefault
            ? theme.colorScheme.primaryContainer
            : theme.colorScheme.surfaceVariant,
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  if (isDefault)
                    Icon(
                      Icons.star,
                      size: 16,
                      color: theme.colorScheme.onPrimaryContainer,
                    ),
                  if (isDefault) const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      activeModel.displayName,
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: isDefault
                            ? theme.colorScheme.onPrimaryContainer
                            : null,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  PopupMenuButton<String>(
                    iconSize: 16,
                    onSelected: (action) =>
                        _handleActiveModelAction(activeModel, action),
                    itemBuilder: (context) => [
                      if (!isDefault)
                        const PopupMenuItem(
                          value: 'make_default',
                          child: Row(
                            children: [
                              Icon(Icons.star, size: 16),
                              SizedBox(width: 8),
                              Text('Set as Default'),
                            ],
                          ),
                        ),
                      const PopupMenuItem(
                        value: 'configure',
                        child: Row(
                          children: [
                            Icon(Icons.tune, size: 16),
                            SizedBox(width: 8),
                            Text('Configure'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'remove',
                        child: Row(
                          children: [
                            Icon(Icons.remove_circle, size: 16),
                            SizedBox(width: 8),
                            Text('Remove'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                model.id,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: isDefault
                      ? theme.colorScheme.onPrimaryContainer.withValues(
                          alpha: 0.7,
                        )
                      : theme.colorScheme.onSurfaceVariant,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  _buildModelChip(
                    model.contextSizeLabel,
                    Icons.memory,
                    theme.colorScheme.secondary,
                  ),
                  const SizedBox(width: 4),
                  _buildModelChip(
                    model.priceRange,
                    Icons.attach_money,
                    theme.colorScheme.tertiary,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModelsList(List<OpenRouterModel> models) {
    if (models.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No models found',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            Text(
              'Try adjusting your search or filters',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: models.length,
      itemBuilder: (context, index) {
        final model = models[index];
        return _buildModelTile(model);
      },
    );
  }

  Widget _buildModelTile(OpenRouterModel model) {
    final theme = Theme.of(context);
    final activeModelsAsync = ref.watch(activeModelsProvider);

    final isActive =
        activeModelsAsync.whenOrNull(
          data: (activeModels) =>
              activeModels.any((am) => am.model.id == model.id),
        ) ??
        false;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        title: Text(
          model.name,
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              model.id,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            if (model.description.isNotEmpty) ...[
              const SizedBox(height: 4),
              Text(
                model.description,
                style: theme.textTheme.bodySmall,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
            const SizedBox(height: 8),
            Wrap(
              spacing: 4,
              runSpacing: 4,
              children: [
                _buildModelChip(
                  model.contextSizeLabel,
                  Icons.memory,
                  theme.colorScheme.secondary,
                ),
                _buildModelChip(
                  model.priceRange,
                  Icons.attach_money,
                  theme.colorScheme.tertiary,
                ),
                if (model.supportsVision)
                  _buildModelChip(
                    'Vision',
                    Icons.visibility,
                    theme.colorScheme.primary,
                  ),
                if (model.supportsFunctionCalling)
                  _buildModelChip(
                    'Functions',
                    Icons.functions,
                    theme.colorScheme.primary,
                  ),
              ],
            ),
          ],
        ),
        trailing: isActive
            ? Icon(Icons.check_circle, color: theme.colorScheme.primary)
            : IconButton(
                icon: const Icon(Icons.add_circle_outline),
                onPressed: () => _addToActiveModels(model),
              ),
        onTap: () => _showModelDetails(model),
      ),
    );
  }

  Widget _buildModelChip(String label, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 10, color: color),
          const SizedBox(width: 2),
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  void _addToActiveModels(OpenRouterModel model) async {
    final activeModelsNotifier = ref.read(activeModelsProvider.notifier);
    final currentActive = await ref.read(activeModelsProvider.future);

    if (currentActive.length >= 10) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Maximum 10 active models allowed')),
        );
      }
      return;
    }

    await activeModelsNotifier.addActiveModel(model);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Added ${model.name} to active models')),
      );
    }
  }

  void _handleActiveModelAction(ActiveModel activeModel, String action) {
    final notifier = ref.read(activeModelsProvider.notifier);

    switch (action) {
      case 'make_default':
        notifier.setDefaultModel(activeModel.model.id);
        break;
      case 'configure':
        _showModelConfiguration(activeModel);
        break;
      case 'remove':
        notifier.removeActiveModel(activeModel.model.id);
        break;
    }
  }

  void _showModelDetails(OpenRouterModel model) {
    showDialog(
      context: context,
      builder: (context) => _ModelDetailsDialog(model: model),
    );
  }

  void _showModelConfiguration(ActiveModel activeModel) {
    showDialog(
      context: context,
      builder: (context) => _ModelConfigurationDialog(activeModel: activeModel),
    );
  }

  void _showActiveModelsManager() {
    showDialog(
      context: context,
      builder: (context) => _ActiveModelsManagerDialog(),
    );
  }
}

class FilterOption {
  final String key;
  final String label;
  final IconData icon;

  FilterOption(this.key, this.label, this.icon);
}

class _ModelDetailsDialog extends StatelessWidget {
  final OpenRouterModel model;

  const _ModelDetailsDialog({required this.model});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AlertDialog(
      title: Text(model.name),
      content: SizedBox(
        width: 400,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('Model ID', model.id),
            _buildDetailRow('Context Length', '${model.contextLength} tokens'),
            _buildDetailRow(
              'Max Output',
              model.maxOutput?.toString() ?? 'Not specified',
            ),
            _buildDetailRow(
              'Input Price',
              '\$${model.inputPricePerMillion}/M tokens',
            ),
            _buildDetailRow(
              'Output Price',
              '\$${model.outputPricePerMillion}/M tokens',
            ),
            _buildDetailRow('Modalities', model.modalities.join(', ')),
            if (model.organization != null)
              _buildDetailRow('Organization', model.organization!),
            const SizedBox(height: 16),
            if (model.description.isNotEmpty) ...[
              Text(
                'Description',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Text(model.description, style: theme.textTheme.bodyMedium),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: SelectableText(value)),
        ],
      ),
    );
  }
}

class _ModelConfigurationDialog extends ConsumerStatefulWidget {
  final ActiveModel activeModel;

  const _ModelConfigurationDialog({required this.activeModel});

  @override
  ConsumerState<_ModelConfigurationDialog> createState() =>
      _ModelConfigurationDialogState();
}

class _ModelConfigurationDialogState
    extends ConsumerState<_ModelConfigurationDialog> {
  late final TextEditingController _nicknameController;
  late final TextEditingController _maxTokensController;
  late double _temperature;

  @override
  void initState() {
    super.initState();
    _nicknameController = TextEditingController(
      text: widget.activeModel.nickname ?? '',
    );
    _maxTokensController = TextEditingController(
      text: widget.activeModel.maxTokensOverride?.toString() ?? '',
    );
    _temperature = widget.activeModel.temperatureOverride ?? 0.7;
  }

  @override
  void dispose() {
    _nicknameController.dispose();
    _maxTokensController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Configure ${widget.activeModel.model.name}'),
      content: SizedBox(
        width: 400,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _nicknameController,
              decoration: const InputDecoration(
                labelText: 'Display Name (optional)',
                hintText: 'Custom name for this model',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _maxTokensController,
              decoration: const InputDecoration(
                labelText: 'Max Tokens Override (optional)',
                hintText: 'Custom max tokens limit',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Temperature Override',
                      style: TextStyle(fontWeight: FontWeight.w500),
                    ),
                    Text(_temperature.toStringAsFixed(2)),
                  ],
                ),
                Slider(
                  value: _temperature,
                  min: 0.0,
                  max: 2.0,
                  divisions: 20,
                  onChanged: (value) {
                    setState(() {
                      _temperature = value;
                    });
                  },
                ),
              ],
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _saveConfiguration,
          child: const Text('Save'),
        ),
      ],
    );
  }

  void _saveConfiguration() {
    final notifier = ref.read(activeModelsProvider.notifier);

    final updatedModel = widget.activeModel.copyWith(
      nickname: _nicknameController.text.trim().isEmpty
          ? null
          : _nicknameController.text.trim(),
      temperatureOverride: _temperature,
      maxTokensOverride: _maxTokensController.text.trim().isEmpty
          ? null
          : int.tryParse(_maxTokensController.text.trim()),
    );

    notifier.updateActiveModel(widget.activeModel.model.id, updatedModel);
    Navigator.of(context).pop();
  }
}

class _ActiveModelsManagerDialog extends ConsumerWidget {
  const _ActiveModelsManagerDialog();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final activeModelsAsync = ref.watch(activeModelsProvider);

    return Dialog(
      child: Container(
        width: 600,
        height: 500,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Text(
                  'Active Models Manager',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: activeModelsAsync.when(
                data: (models) => _buildModelsList(context, ref, models),
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, _) => Center(child: Text('Error: $error')),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                const Spacer(),
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Close'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModelsList(
    BuildContext context,
    WidgetRef ref,
    List<ActiveModel> models,
  ) {
    if (models.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.psychology, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No Active Models',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
            ),
            Text(
              'Add models from the browser below',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ReorderableListView.builder(
      itemCount: models.length,
      onReorder: (oldIndex, newIndex) {
        _reorderModels(ref, models, oldIndex, newIndex);
      },
      itemBuilder: (context, index) {
        final model = models[index];
        return _buildModelTile(
          context,
          ref,
          model,
          index,
          key: ValueKey(model.model.id),
        );
      },
    );
  }

  Widget _buildModelTile(
    BuildContext context,
    WidgetRef ref,
    ActiveModel activeModel,
    int index, {
    required Key key,
  }) {
    final theme = Theme.of(context);
    final isDefault = index == 0;

    return Card(
      key: key,
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.drag_handle),
            const SizedBox(width: 8),
            if (isDefault)
              Icon(Icons.star, color: theme.colorScheme.primary, size: 18)
            else
              const SizedBox(width: 18),
          ],
        ),
        title: Text(
          activeModel.displayName,
          style: TextStyle(
            fontWeight: isDefault ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(activeModel.model.id, style: theme.textTheme.bodySmall),
            Row(
              children: [
                Text(
                  activeModel.model.contextSizeLabel,
                  style: theme.textTheme.bodySmall,
                ),
                const SizedBox(width: 8),
                Text(
                  activeModel.model.priceRange,
                  style: theme.textTheme.bodySmall,
                ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (action) =>
              _handleModelAction(context, ref, activeModel, action, index),
          itemBuilder: (context) => [
            if (!isDefault)
              const PopupMenuItem(
                value: 'make_default',
                child: Row(
                  children: [
                    Icon(Icons.star, size: 16),
                    SizedBox(width: 8),
                    Text('Make Default'),
                  ],
                ),
              ),
            const PopupMenuItem(
              value: 'configure',
              child: Row(
                children: [
                  Icon(Icons.tune, size: 16),
                  SizedBox(width: 8),
                  Text('Configure'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'remove',
              child: Row(
                children: [
                  Icon(Icons.delete, size: 16, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Remove', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _reorderModels(
    WidgetRef ref,
    List<ActiveModel> models,
    int oldIndex,
    int newIndex,
  ) {
    if (newIndex > oldIndex) newIndex--;

    // For now, just show a message that reordering is not implemented
    ScaffoldMessenger.of(ref.context).showSnackBar(
      const SnackBar(
        content: Text('Model reordering functionality coming soon'),
      ),
    );
  }

  void _handleModelAction(
    BuildContext context,
    WidgetRef ref,
    ActiveModel activeModel,
    String action,
    int index,
  ) {
    final notifier = ref.read(activeModelsProvider.notifier);

    switch (action) {
      case 'make_default':
        notifier.setDefaultModel(activeModel.model.id);
        break;
      case 'configure':
        _showModelConfiguration(context, activeModel);
        break;
      case 'remove':
        _confirmRemoveModel(context, ref, activeModel);
        break;
    }
  }

  void _showModelConfiguration(BuildContext context, ActiveModel activeModel) {
    showDialog(
      context: context,
      builder: (context) => _ModelConfigurationDialog(activeModel: activeModel),
    );
  }

  void _confirmRemoveModel(
    BuildContext context,
    WidgetRef ref,
    ActiveModel activeModel,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Remove Model'),
        content: Text(
          'Remove "${activeModel.displayName}" from active models?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ref
                  .read(activeModelsProvider.notifier)
                  .removeActiveModel(activeModel.model.id);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Remove'),
          ),
        ],
      ),
    );
  }
}
