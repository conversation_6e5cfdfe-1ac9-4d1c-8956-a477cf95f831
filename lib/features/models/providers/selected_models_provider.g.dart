// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'selected_models_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

/// Manages the user's selected OpenRouter models for the model picker.
///
/// This provider handles persistence of selected model IDs in user settings
/// and provides methods to add/remove models from the selection.
/// Selected models appear in the chat interface model dropdown.
@ProviderFor(SelectedModels)
const selectedModelsProvider = SelectedModelsProvider._();

/// Manages the user's selected OpenRouter models for the model picker.
///
/// This provider handles persistence of selected model IDs in user settings
/// and provides methods to add/remove models from the selection.
/// Selected models appear in the chat interface model dropdown.
final class SelectedModelsProvider
    extends $AsyncNotifierProvider<SelectedModels, List<String>> {
  /// Manages the user's selected OpenRouter models for the model picker.
  ///
  /// This provider handles persistence of selected model IDs in user settings
  /// and provides methods to add/remove models from the selection.
  /// Selected models appear in the chat interface model dropdown.
  const SelectedModelsProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'selectedModelsProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$selectedModelsHash();

  @$internal
  @override
  SelectedModels create() => SelectedModels();
}

String _$selectedModelsHash() => r'4c8cf6f9ae00ed54c75d3aacd827a09350e1724c';

abstract class _$SelectedModels extends $AsyncNotifier<List<String>> {
  FutureOr<List<String>> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<AsyncValue<List<String>>, List<String>>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<List<String>>, List<String>>,
              AsyncValue<List<String>>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
