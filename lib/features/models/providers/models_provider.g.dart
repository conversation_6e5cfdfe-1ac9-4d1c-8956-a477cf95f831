// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'models_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

@ProviderFor(OpenRouterModels)
const openRouterModelsProvider = OpenRouterModelsProvider._();

final class OpenRouterModelsProvider
    extends $AsyncNotifierProvider<OpenRouterModels, List<OpenRouterModel>> {
  const OpenRouterModelsProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'openRouterModelsProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$openRouterModelsHash();

  @$internal
  @override
  OpenRouterModels create() => OpenRouterModels();
}

String _$openRouterModelsHash() => r'141d3e7d5a8c22b4c30a619ca197084c23160ca9';

abstract class _$OpenRouterModels
    extends $AsyncNotifier<List<OpenRouterModel>> {
  FutureOr<List<OpenRouterModel>> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref =
        this.ref
            as $Ref<AsyncValue<List<OpenRouterModel>>, List<OpenRouterModel>>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<
                AsyncValue<List<OpenRouterModel>>,
                List<OpenRouterModel>
              >,
              AsyncValue<List<OpenRouterModel>>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

@ProviderFor(ActiveModels)
const activeModelsProvider = ActiveModelsProvider._();

final class ActiveModelsProvider
    extends $AsyncNotifierProvider<ActiveModels, List<ActiveModel>> {
  const ActiveModelsProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'activeModelsProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$activeModelsHash();

  @$internal
  @override
  ActiveModels create() => ActiveModels();
}

String _$activeModelsHash() => r'a5842315ae66705bf735900e1f647545c00955da';

abstract class _$ActiveModels extends $AsyncNotifier<List<ActiveModel>> {
  FutureOr<List<ActiveModel>> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref =
        this.ref as $Ref<AsyncValue<List<ActiveModel>>, List<ActiveModel>>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<List<ActiveModel>>, List<ActiveModel>>,
              AsyncValue<List<ActiveModel>>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

@ProviderFor(ModelSearch)
const modelSearchProvider = ModelSearchFamily._();

final class ModelSearchProvider
    extends $NotifierProvider<ModelSearch, List<OpenRouterModel>> {
  const ModelSearchProvider._({
    required ModelSearchFamily super.from,
    required (String, List<String>) super.argument,
  }) : super(
         retry: null,
         name: r'modelSearchProvider',
         isAutoDispose: true,
         dependencies: null,
         $allTransitiveDependencies: null,
       );

  @override
  String debugGetCreateSourceHash() => _$modelSearchHash();

  @override
  String toString() {
    return r'modelSearchProvider'
        ''
        '$argument';
  }

  @$internal
  @override
  ModelSearch create() => ModelSearch();

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(List<OpenRouterModel> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<List<OpenRouterModel>>(value),
    );
  }

  @override
  bool operator ==(Object other) {
    return other is ModelSearchProvider && other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$modelSearchHash() => r'c8c665b55addb7ba0b3f5d1571509a7291901c7c';

final class ModelSearchFamily extends $Family
    with
        $ClassFamilyOverride<
          ModelSearch,
          List<OpenRouterModel>,
          List<OpenRouterModel>,
          List<OpenRouterModel>,
          (String, List<String>)
        > {
  const ModelSearchFamily._()
    : super(
        retry: null,
        name: r'modelSearchProvider',
        dependencies: null,
        $allTransitiveDependencies: null,
        isAutoDispose: true,
      );

  ModelSearchProvider call(String query, List<String> filters) =>
      ModelSearchProvider._(argument: (query, filters), from: this);

  @override
  String toString() => r'modelSearchProvider';
}

abstract class _$ModelSearch extends $Notifier<List<OpenRouterModel>> {
  late final _$args = ref.$arg as (String, List<String>);
  String get query => _$args.$1;
  List<String> get filters => _$args.$2;

  List<OpenRouterModel> build(String query, List<String> filters);
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build(_$args.$1, _$args.$2);
    final ref = this.ref as $Ref<List<OpenRouterModel>, List<OpenRouterModel>>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<List<OpenRouterModel>, List<OpenRouterModel>>,
              List<OpenRouterModel>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
