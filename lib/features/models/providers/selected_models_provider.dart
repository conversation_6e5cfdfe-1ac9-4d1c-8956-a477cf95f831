import 'dart:convert';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../settings/providers/settings_provider.dart';

part 'selected_models_provider.g.dart';

/// Manages the user's selected OpenRouter models for the model picker.
///
/// This provider handles persistence of selected model IDs in user settings
/// and provides methods to add/remove models from the selection.
/// Selected models appear in the chat interface model dropdown.
@riverpod
class SelectedModels extends _$SelectedModels {
  @override
  Future<List<String>> build() async {
    final settings = await ref.watch(settingsProvider.future);
    final selectedModelsJson = settings['selected_models'] as String?;
    
    if (selectedModelsJson == null) {
      // Return empty list if no models configured
      return [];
    }
    
    try {
      final List<dynamic> decoded = jsonDecode(selectedModelsJson);
      return decoded.cast<String>();
    } catch (e) {
      // Return empty list if JSON parsing fails
      return [];
    }
  }

  /// Toggles a model in the selection - adds if not present, removes if present.
  Future<void> toggleModel(String modelId) async {
    final current = await future;
    final updated = List<String>.from(current);
    
    if (updated.contains(modelId)) {
      updated.remove(modelId);
    } else {
      updated.add(modelId);
    }
    
    await _saveSelectedModels(updated);
  }

  /// Adds a model to the selection if not already present.
  Future<void> addModel(String modelId) async {
    final current = await future;
    if (!current.contains(modelId)) {
      final updated = [...current, modelId];
      await _saveSelectedModels(updated);
    }
  }

  /// Removes a model from the selection.
  Future<void> removeModel(String modelId) async {
    final current = await future;
    final updated = current.where((id) => id != modelId).toList();
    await _saveSelectedModels(updated);
  }

  /// Clears all selected models.
  Future<void> clearAll() async {
    await _saveSelectedModels([]);
  }

  Future<void> _saveSelectedModels(List<String> models) async {
    final settingsNotifier = ref.read(settingsProvider.notifier);
    final json = jsonEncode(models);
    await settingsNotifier.updateSetting('selected_models', json);
    ref.invalidateSelf();
  }

  /// Checks if a model is currently selected.
  bool isModelSelected(String modelId) {
    return state.whenOrNull(data: (models) => models.contains(modelId)) ?? false;
  }
}