import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../providers/selected_models_provider.dart';

/// Inline model management widget for API settings.
/// 
/// Displays currently selected models as editable text fields with remove buttons,
/// and provides an add button to create new model entries. Validates model IDs
/// to ensure they follow the provider/model-name format.
class ModelManagementWidget extends ConsumerStatefulWidget {
  const ModelManagementWidget({super.key});

  @override
  ConsumerState<ModelManagementWidget> createState() => _ModelManagementWidgetState();
}

class _ModelManagementWidgetState extends ConsumerState<ModelManagementWidget> {
  final Map<String, TextEditingController> _controllers = {};
  final Map<String, String> _editingModels = {}; // originalId -> currentValue
  final List<TextEditingController> _newModelControllers = [];

  @override
  void dispose() {
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    for (final controller in _newModelControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final selectedModelsAsync = ref.watch(selectedModelsProvider);

    return selectedModelsAsync.when(
      data: (selectedModels) => _buildModelManagement(selectedModels, theme),
      loading: () => const Center(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: CircularProgressIndicator(),
        ),
      ),
      error: (error, _) => Padding(
        padding: const EdgeInsets.all(16),
        child: Text(
          'Error loading models: $error',
          style: TextStyle(color: theme.colorScheme.error),
        ),
      ),
    );
  }

  Widget _buildModelManagement(List<String> selectedModels, ThemeData theme) {
    // Ensure controllers exist for all selected models
    _ensureControllers(selectedModels);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Currently selected models
        if (selectedModels.isNotEmpty) ...[
          ...selectedModels.map((modelId) => _buildModelTextField(
            modelId,
            _controllers[modelId]!,
            theme,
            isExisting: true,
          )),
          const SizedBox(height: 8),
        ],

        // New model input fields
        ..._newModelControllers.asMap().entries.map((entry) => 
          _buildModelTextField(
            '',
            entry.value,
            theme,
            isExisting: false,
            index: entry.key,
          ),
        ),

        // Add model button
        const SizedBox(height: 8),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _addNewModelField,
            icon: const Icon(Icons.add),
            label: const Text('Add Model'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.all(12),
              side: BorderSide(
                color: theme.colorScheme.outline.withOpacity(0.5),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildModelTextField(
    String originalModelId,
    TextEditingController controller,
    ThemeData theme, {
    required bool isExisting,
    int? index,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Expanded(
            child: TextFormField(
              controller: controller,
              decoration: InputDecoration(
                labelText: isExisting ? 'Selected Model' : 'New Model',
                hintText: 'provider/model-name',
                border: const OutlineInputBorder(),
                prefixIcon: const Icon(Icons.model_training),
                errorText: _getValidationError(controller.text),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              onChanged: (value) {
                setState(() {
                  if (isExisting) {
                    _editingModels[originalModelId] = value;
                  }
                });
              },
              onFieldSubmitted: (value) {
                if (isExisting) {
                  _saveModelEdit(originalModelId, value);
                } else {
                  _saveNewModel(index!, value);
                }
              },
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            onPressed: () {
              if (isExisting) {
                _removeModel(originalModelId);
              } else {
                _removeNewModelField(index!);
              }
            },
            icon: const Icon(Icons.close),
            color: theme.colorScheme.error,
            tooltip: 'Remove',
          ),
        ],
      ),
    );
  }

  void _ensureControllers(List<String> selectedModels) {
    // Remove controllers for models that no longer exist
    final currentKeys = _controllers.keys.toList();
    for (final key in currentKeys) {
      if (!selectedModels.contains(key)) {
        _controllers[key]?.dispose();
        _controllers.remove(key);
        _editingModels.remove(key);
      }
    }

    // Add controllers for new models
    for (final modelId in selectedModels) {
      if (!_controllers.containsKey(modelId)) {
        final controller = TextEditingController(text: modelId);
        _controllers[modelId] = controller;
        
        // Listen for changes to auto-save
        controller.addListener(() {
          final currentValue = controller.text;
          if (currentValue != modelId && _isValidModelId(currentValue)) {
            _saveModelEdit(modelId, currentValue);
          }
        });
      }
    }
  }

  void _addNewModelField() {
    setState(() {
      final controller = TextEditingController();
      _newModelControllers.add(controller);
      
      // Focus the new field
      WidgetsBinding.instance.addPostFrameCallback((_) {
        FocusScope.of(context).requestFocus(FocusNode());
      });
    });
  }

  void _removeNewModelField(int index) {
    setState(() {
      _newModelControllers[index].dispose();
      _newModelControllers.removeAt(index);
    });
  }

  void _removeModel(String modelId) {
    ref.read(selectedModelsProvider.notifier).removeModel(modelId);
    _controllers[modelId]?.dispose();
    _controllers.remove(modelId);
    _editingModels.remove(modelId);
  }

  void _saveModelEdit(String originalModelId, String newModelId) {
    if (_isValidModelId(newModelId) && newModelId != originalModelId) {
      final provider = ref.read(selectedModelsProvider.notifier);
      
      // Remove old model and add new one
      provider.removeModel(originalModelId);
      provider.addModel(newModelId);
      
      // Clear editing state
      _editingModels.remove(originalModelId);
    }
  }

  void _saveNewModel(int index, String modelId) {
    if (_isValidModelId(modelId)) {
      ref.read(selectedModelsProvider.notifier).addModel(modelId);
      
      // Remove the new model field
      setState(() {
        _newModelControllers[index].dispose();
        _newModelControllers.removeAt(index);
      });
    }
  }

  bool _isValidModelId(String modelId) {
    if (modelId.trim().isEmpty) return false;
    
    // Check for provider/model-name format
    final parts = modelId.trim().split('/');
    if (parts.length < 2) return false;
    
    // Ensure provider and model name are not empty
    final provider = parts[0].trim();
    final modelName = parts.sublist(1).join('/').trim();
    
    return provider.isNotEmpty && modelName.isNotEmpty;
  }

  String? _getValidationError(String value) {
    if (value.trim().isEmpty) return null;
    
    if (!_isValidModelId(value)) {
      return 'Format: provider/model-name';
    }
    
    return null;
  }
}