import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../providers/models_provider.dart';
import '../providers/selected_models_provider.dart';
import '../../../core/api/openrouter/models_api.dart';

/// A drawer widget for browsing and selecting OpenRouter models.
///
/// Features:
/// - Search functionality to filter models by name/ID/description
/// - Category filtering (All, Free Models, Premium, Vision, Code)
/// - Models grouped by provider (OpenAI, Anthropic, Google, etc.)
/// - Toggle selection with visual indicators
/// - Shows model metadata (context length, pricing)
/// - Persistent selection stored in user settings
class ModelSelectionDrawer extends ConsumerStatefulWidget {
  const ModelSelectionDrawer({super.key});

  @override
  ConsumerState<ModelSelectionDrawer> createState() =>
      _ModelSelectionDrawerState();
}

class _ModelSelectionDrawerState extends ConsumerState<ModelSelectionDrawer> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedCategory = 'ALL';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final allModelsAsync = ref.watch(openRouterModelsProvider);
    final selectedModelsAsync = ref.watch(selectedModelsProvider);

    return Drawer(
      width: MediaQuery.of(context).size.width * 0.85,
      child: Column(
        children: [
          // Header
          Container(
            height: 120,
            padding: const EdgeInsets.fromLTRB(16, 50, 16, 16),
            decoration: BoxDecoration(color: theme.colorScheme.primary),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'OpenRouter Models',
                    style: theme.textTheme.headlineSmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close, color: Colors.white),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
          ),

          // Search Bar
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                          });
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: theme.colorScheme.surfaceVariant.withValues(
                  alpha: 0.5,
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value.toLowerCase();
                });
              },
            ),
          ),

          // Category Filter
          _buildCategoryFilter(theme),

          // Model List
          Expanded(
            child: allModelsAsync.when(
              data: (allModels) => selectedModelsAsync.when(
                data: (selectedModels) =>
                    _buildModelList(allModels, selectedModels, theme),
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, _) => _buildErrorView(error),
              ),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, _) => _buildErrorView(error),
            ),
          ),

          // Footer with selected count
          selectedModelsAsync.when(
            data: (selectedModels) => Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceVariant,
                border: Border(
                  top: BorderSide(
                    color: theme.colorScheme.outline.withValues(alpha: 0.2),
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${selectedModels.length} models selected',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (selectedModels.isNotEmpty)
                    TextButton(
                      onPressed: () => _showClearAllDialog(context),
                      child: const Text('Clear All'),
                    ),
                ],
              ),
            ),
            loading: () => const SizedBox(height: 50),
            error: (_, __) => const SizedBox(height: 50),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryFilter(ThemeData theme) {
    final categories = ['ALL', 'FREE MODELS', 'PREMIUM', 'VISION', 'CODE'];

    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          final isSelected = _selectedCategory == category;

          return Padding(
            padding: const EdgeInsets.only(right: 8),
            child: FilterChip(
              label: Text(category),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedCategory = category;
                });
              },
              backgroundColor: theme.colorScheme.surface,
              selectedColor: theme.colorScheme.primaryContainer,
              labelStyle: TextStyle(
                color: isSelected
                    ? theme.colorScheme.onPrimaryContainer
                    : theme.colorScheme.onSurface,
                fontSize: 12,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildModelList(
    List<OpenRouterModel> allModels,
    List<String> selectedModels,
    ThemeData theme,
  ) {
    final filteredModels = _filterModels(allModels);

    if (filteredModels.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 48,
              color: theme.colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 16),
            Text(
              'No models found',
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      );
    }

    // Group models by category
    final groupedModels = _groupModelsByProvider(filteredModels);

    return ListView.builder(
      itemCount: groupedModels.length,
      itemBuilder: (context, groupIndex) {
        final group = groupedModels[groupIndex];
        return _buildModelGroup(group, selectedModels, theme);
      },
    );
  }

  Widget _buildModelGroup(
    ModelGroup group,
    List<String> selectedModels,
    ThemeData theme,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (group.title.isNotEmpty) ...[
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            child: Text(
              group.title,
              style: theme.textTheme.titleSmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
        ...group.models.map(
          (model) =>
              _buildModelTile(model, selectedModels.contains(model.id), theme),
        ),
      ],
    );
  }

  Widget _buildModelTile(
    OpenRouterModel model,
    bool isSelected,
    ThemeData theme,
  ) {
    final isFree =
        model.inputPricePerMillion == 0 && model.outputPricePerMillion == 0;

    return ListTile(
      title: Text(
        model.name,
        style: theme.textTheme.bodyMedium?.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            model.id,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
              fontFamily: 'monospace',
            ),
          ),
          const SizedBox(height: 2),
          Text(
            'Context: ${model.contextSizeLabel}',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (isFree)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: Colors.green, width: 1),
              ),
              child: Text(
                'FREE',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: Colors.green,
                  fontWeight: FontWeight.bold,
                  fontSize: 10,
                ),
              ),
            ),
          const SizedBox(width: 8),
          IconButton(
            icon: Icon(
              isSelected ? Icons.check_circle : Icons.add_circle_outline,
              color: isSelected
                  ? theme.colorScheme.primary
                  : theme.colorScheme.onSurfaceVariant,
            ),
            onPressed: () {
              ref.read(selectedModelsProvider.notifier).toggleModel(model.id);
            },
          ),
        ],
      ),
      onTap: () {
        ref.read(selectedModelsProvider.notifier).toggleModel(model.id);
      },
    );
  }

  Widget _buildErrorView(dynamic error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 48),
          const SizedBox(height: 16),
          Text('Error loading models: $error'),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => ref.refresh(openRouterModelsProvider),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  List<OpenRouterModel> _filterModels(List<OpenRouterModel> models) {
    var filtered = models.where((model) {
      // Text search
      if (_searchQuery.isNotEmpty) {
        if (!model.name.toLowerCase().contains(_searchQuery) &&
            !model.id.toLowerCase().contains(_searchQuery) &&
            !model.description.toLowerCase().contains(_searchQuery)) {
          return false;
        }
      }

      // Category filter
      switch (_selectedCategory) {
        case 'FREE MODELS':
          return model.inputPricePerMillion == 0 &&
              model.outputPricePerMillion == 0;
        case 'PREMIUM':
          return model.inputPricePerMillion > 0 ||
              model.outputPricePerMillion > 0;
        case 'VISION':
          return model.supportsVision;
        case 'CODE':
          return model.description.toLowerCase().contains('code') ||
              model.name.toLowerCase().contains('code') ||
              model.id.toLowerCase().contains('code');
        default:
          return true;
      }
    }).toList();

    // Sort by name
    filtered.sort((a, b) => a.name.compareTo(b.name));

    return filtered;
  }

  List<ModelGroup> _groupModelsByProvider(List<OpenRouterModel> models) {
    final Map<String, List<OpenRouterModel>> groupedMap = {};

    for (final model in models) {
      final provider = _getProviderFromId(model.id);
      if (!groupedMap.containsKey(provider)) {
        groupedMap[provider] = [];
      }
      groupedMap[provider]!.add(model);
    }

    final groups = <ModelGroup>[];
    final sortedKeys = groupedMap.keys.toList()..sort();

    for (final provider in sortedKeys) {
      groups.add(
        ModelGroup(_formatProviderTitle(provider), groupedMap[provider]!),
      );
    }

    return groups;
  }

  String _getProviderFromId(String modelId) {
    final parts = modelId.split('/');
    return parts.isNotEmpty ? parts.first : 'Unknown';
  }

  String _formatProviderTitle(String provider) {
    switch (provider.toLowerCase()) {
      case 'openai':
        return 'OpenAI';
      case 'anthropic':
        return 'Anthropic';
      case 'meta-llama':
        return 'Meta Llama';
      case 'google':
        return 'Google';
      case 'cohere':
        return 'Cohere';
      case 'mistralai':
        return 'Mistral AI';
      case 'huggingfaceh4':
        return 'Hugging Face';
      default:
        return provider.toUpperCase();
    }
  }

  void _showClearAllDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Models'),
        content: const Text(
          'Are you sure you want to remove all selected models?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(selectedModelsProvider.notifier).clearAll();
            },
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }
}

class ModelGroup {
  final String title;
  final List<OpenRouterModel> models;

  ModelGroup(this.title, this.models);
}
