import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../shared/providers/database_provider.dart';
import '../../../core/database/daos/settings_dao.dart';
import '../../../core/services/secure_storage.dart';

part 'settings_provider.g.dart';

@riverpod
class Settings extends _$Settings {
  late SettingsDao _settingsDao;
  late SecureStorageService _secureStorage;

  @override
  Future<Map<String, dynamic>> build() async {
    final database = ref.watch(databaseProvider);
    _settingsDao = SettingsDao(database);
    _secureStorage = SecureStorageService();

    return await _loadAllSettings();
  }

  Future<Map<String, dynamic>> _loadAllSettings() async {
    final settings = <String, dynamic>{};
    
    // Load from database
    final dbSettings = await _settingsDao.getAllSettings();
    for (final setting in dbSettings) {
      settings[setting.key] = _parseSettingValue(setting.value, setting.type);
    }

    // Load API keys from secure storage
    final openRouterKey = await _secureStorage.getOpenRouterApiKey();
    if (openRouterKey != null) {
      settings['openrouter_api_key'] = openRouterKey;
    }

    // Set defaults if not present
    _setDefaults(settings);

    return settings;
  }

  dynamic _parseSettingValue(String value, String type) {
    switch (type) {
      case 'int':
        return int.tryParse(value) ?? 0;
      case 'double':
        return double.tryParse(value) ?? 0.0;
      case 'bool':
        return value.toLowerCase() == 'true';
      case 'json':
        try {
          return Uri.decodeComponent(value);
        } catch (e) {
          return <String, dynamic>{};
        }
      default:
        return value;
    }
  }

  void _setDefaults(Map<String, dynamic> settings) {
    // Model settings
    settings.putIfAbsent('default_model', () => 'openai/gpt-4o-mini');
    settings.putIfAbsent('temperature', () => 0.7);
    settings.putIfAbsent('max_tokens', () => null);
    settings.putIfAbsent('top_p', () => 1.0);
    settings.putIfAbsent('top_k', () => null);
    settings.putIfAbsent('frequency_penalty', () => 0.0);
    settings.putIfAbsent('presence_penalty', () => 0.0);
    settings.putIfAbsent('repetition_penalty', () => null);

    // UI settings
    settings.putIfAbsent('theme_mode', () => 'system');
    settings.putIfAbsent('message_font_size', () => 14.0);
    settings.putIfAbsent('code_theme', () => 'github');
    settings.putIfAbsent('show_token_count', () => true);
    settings.putIfAbsent('auto_scroll', () => true);

    // Chat settings
    settings.putIfAbsent('auto_title', () => true);
    settings.putIfAbsent('send_on_enter', () => true);
    settings.putIfAbsent('stream_responses', () => true);

    // MCP settings
    settings.putIfAbsent('auto_execute_tools', () => false);
    settings.putIfAbsent('tool_timeout', () => 30);

    // Privacy settings
    settings.putIfAbsent('store_conversations', () => true);
    settings.putIfAbsent('analytics_enabled', () => false);
  }

  Future<void> updateSetting(String key, dynamic value) async {
    // Handle secure storage separately
    if (key == 'openrouter_api_key') {
      if (value != null) {
        await _secureStorage.storeOpenRouterApiKey(value.toString());
      } else {
        await _secureStorage.deleteOpenRouterApiKey();
      }
    } else {
      // Store in database based on type
      if (value is String) {
        await _settingsDao.setString(key, value);
      } else if (value is int) {
        await _settingsDao.setInt(key, value);
      } else if (value is double) {
        await _settingsDao.setDouble(key, value);
      } else if (value is bool) {
        await _settingsDao.setBool(key, value);
      } else if (value is Map<String, dynamic>) {
        await _settingsDao.setJson(key, value);
      } else if (value == null) {
        await _settingsDao.deleteSetting(key);
      }
    }

    // Refresh the provider
    ref.invalidateSelf();
  }

  Future<void> updateMultipleSettings(Map<String, dynamic> updates) async {
    for (final entry in updates.entries) {
      await updateSetting(entry.key, entry.value);
    }
    // Only invalidate once at the end
    ref.invalidateSelf();
  }

  Future<void> resetToDefaults() async {
    // Clear all settings
    await _settingsDao.clearAllSettings();
    await _secureStorage.clearAll();
    
    // Refresh to load defaults
    ref.invalidateSelf();
  }

  // Convenience getters
  String get defaultModel => state.value?['default_model'] ?? 'openai/gpt-4o-mini';
  double get temperature => state.value?['temperature'] ?? 0.7;
  int? get maxTokens => state.value?['max_tokens'];
  double get topP => state.value?['top_p'] ?? 1.0;
  double get frequencyPenalty => state.value?['frequency_penalty'] ?? 0.0;
  double get presencePenalty => state.value?['presence_penalty'] ?? 0.0;
  
  String get themeMode => state.value?['theme_mode'] ?? 'system';
  bool get showTokenCount => state.value?['show_token_count'] ?? true;
  bool get autoScroll => state.value?['auto_scroll'] ?? true;
  bool get sendOnEnter => state.value?['send_on_enter'] ?? true;
  bool get streamResponses => state.value?['stream_responses'] ?? true;
}

@riverpod
class ModelConfig extends _$ModelConfig {
  @override
  Future<Map<String, dynamic>> build() async {
    final settings = await ref.watch(settingsProvider.future);
    
    return {
      'model': settings['default_model'],
      'temperature': settings['temperature'],
      'max_tokens': settings['max_tokens'],
      'top_p': settings['top_p'],
      'top_k': settings['top_k'],
      'frequency_penalty': settings['frequency_penalty'],
      'presence_penalty': settings['presence_penalty'],
      'repetition_penalty': settings['repetition_penalty'],
    };
  }

  Future<void> updateModelConfig(Map<String, dynamic> config) async {
    final settingsNotifier = ref.read(settingsProvider.notifier);
    await settingsNotifier.updateMultipleSettings(config);
    ref.invalidateSelf();
  }

  Future<void> savePreset(String name, Map<String, dynamic> config) async {
    final settingsNotifier = ref.read(settingsProvider.notifier);
    await settingsNotifier.updateSetting('preset_$name', config);
  }

  Future<Map<String, dynamic>?> loadPreset(String name) async {
    final settings = await ref.read(settingsProvider.future);
    return settings['preset_$name'] as Map<String, dynamic>?;
  }

  Future<List<String>> getPresetNames() async {
    final settings = await ref.read(settingsProvider.future);
    return settings.keys
        .where((key) => key.startsWith('preset_'))
        .map((key) => key.substring(7))
        .toList();
  }
}