import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildSection(
            context,
            title: 'API Configuration',
            children: [
              ListTile(
                leading: const Icon(Icons.api),
                title: const Text('API Settings'),
                subtitle: const Text('Configure OpenRouter API key and settings'),
                trailing: const Icon(Icons.chevron_right),
                onTap: () => context.push('/settings/api'),
              ),
              ListTile(
                leading: const Icon(Icons.tune),
                title: const Text('Model Parameters'),
                subtitle: const Text('Temperature, tokens, and other model settings'),
                trailing: const Icon(Icons.chevron_right),
                onTap: () => context.push('/settings/model-parameters'),
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildSection(
            context,
            title: 'Appearance',
            children: [
              ListTile(
                leading: const Icon(Icons.palette),
                title: const Text('Appearance Settings'),
                subtitle: const Text('Theme, colors, and UI preferences'),
                trailing: const Icon(Icons.chevron_right),
                onTap: () => context.push('/settings/appearance'),
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildSection(
            context,
            title: 'MCP Integration',
            children: [
              ListTile(
                leading: const Icon(Icons.extension),
                title: const Text('MCP Servers'),
                subtitle: const Text('Manage Model Context Protocol servers'),
                trailing: const Icon(Icons.chevron_right),
                onTap: () => context.push('/mcp/servers'),
              ),
              ListTile(
                leading: const Icon(Icons.build),
                title: const Text('Tool Browser'),
                subtitle: const Text('Browse available MCP tools'),
                trailing: const Icon(Icons.chevron_right),
                onTap: () => context.push('/mcp/tools'),
              ),
              ListTile(
                leading: const Icon(Icons.history),
                title: const Text('Execution History'),
                subtitle: const Text('View tool execution history'),
                trailing: const Icon(Icons.chevron_right),
                onTap: () => context.push('/mcp/history'),
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildSection(
            context,
            title: 'Data Management',
            children: [
              ListTile(
                leading: const Icon(Icons.download),
                title: const Text('Export Data'),
                subtitle: const Text('Export conversations and settings'),
                trailing: const Icon(Icons.chevron_right),
                onTap: () => _showExportDialog(context),
              ),
              ListTile(
                leading: const Icon(Icons.upload),
                title: const Text('Import Data'),
                subtitle: const Text('Import conversations and settings'),
                trailing: const Icon(Icons.chevron_right),
                onTap: () => _showImportDialog(context),
              ),
              ListTile(
                leading: const Icon(Icons.delete_forever),
                title: const Text('Clear All Data'),
                subtitle: const Text('Delete all conversations and settings'),
                trailing: const Icon(Icons.chevron_right),
                onTap: () => _showClearDataDialog(context),
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildSection(
            context,
            title: 'About',
            children: [
              const ListTile(
                leading: Icon(Icons.info),
                title: Text('Version'),
                subtitle: Text('Otlo Chat v1.0.0'),
              ),
              ListTile(
                leading: const Icon(Icons.code),
                title: const Text('Open Source'),
                subtitle: const Text('Built with Flutter and Riverpod'),
                trailing: const Icon(Icons.open_in_new),
                onTap: () => _showAboutDialog(context),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSection(
    BuildContext context, {
    required String title,
    required List<Widget> children,
  }) {
    final theme = Theme.of(context);
    
    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              title,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          ...children,
          const SizedBox(height: 8),
        ],
      ),
    );
  }

  void _showExportDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Data'),
        content: const Text('Export all your conversations and settings to a file.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement export functionality
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Export functionality will be implemented')),
              );
            },
            child: const Text('Export'),
          ),
        ],
      ),
    );
  }

  void _showImportDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Import Data'),
        content: const Text('Import conversations and settings from a file.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement import functionality
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Import functionality will be implemented')),
              );
            },
            child: const Text('Select File'),
          ),
        ],
      ),
    );
  }

  void _showClearDataDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Data'),
        content: const Text(
          'This will permanently delete all your conversations, settings, and data. This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement clear data functionality
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Clear data functionality will be implemented')),
              );
            },
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Clear All Data'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: 'Otlo Chat',
      applicationVersion: '1.0.0',
      applicationLegalese: '© 2024 Otlo Chat. Built with Flutter.',
      children: [
        const SizedBox(height: 16),
        const Text(
          'A powerful AI chat application with comprehensive MCP (Model Context Protocol) support and OpenRouter integration.',
        ),
      ],
    );
  }
}