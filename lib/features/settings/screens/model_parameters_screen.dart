import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../providers/settings_provider.dart';

class ModelParametersScreen extends ConsumerStatefulWidget {
  const ModelParametersScreen({super.key});

  @override
  ConsumerState<ModelParametersScreen> createState() => _ModelParametersScreenState();
}

class _ModelParametersScreenState extends ConsumerState<ModelParametersScreen> {
  final _defaultModelController = TextEditingController();
  final _maxTokensController = TextEditingController();
  
  double _temperature = 0.7;
  double _topP = 1.0;
  double _frequencyPenalty = 0.0;
  double _presencePenalty = 0.0;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  @override
  void dispose() {
    _defaultModelController.dispose();
    _maxTokensController.dispose();
    super.dispose();
  }

  void _loadSettings() async {
    final settings = await ref.read(settingsProvider.future);
    setState(() {
      _defaultModelController.text = settings['default_model'] ?? 'openai/gpt-4o-mini';
      _maxTokensController.text = (settings['max_tokens'] ?? '').toString();
      _temperature = (settings['temperature'] ?? 0.7).toDouble();
      _topP = (settings['top_p'] ?? 1.0).toDouble();
      _frequencyPenalty = (settings['frequency_penalty'] ?? 0.0).toDouble();
      _presencePenalty = (settings['presence_penalty'] ?? 0.0).toDouble();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Model Parameters'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveSettings,
            child: _isLoading 
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Save'),
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.tune,
                        color: theme.colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Model Selection',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: _defaultModelController,
                    decoration: const InputDecoration(
                      labelText: 'Default Model',
                      hintText: 'e.g., openai/gpt-4o-mini',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.psychology),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Popular models: openai/gpt-4o, openai/gpt-4o-mini, anthropic/claude-3-haiku, meta-llama/llama-3-70b-instruct',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.tune,
                        color: theme.colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Generation Parameters',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: _maxTokensController,
                    decoration: const InputDecoration(
                      labelText: 'Max Tokens',
                      hintText: 'Leave empty for model default',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.numbers),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                  const SizedBox(height: 16),
                  _buildSliderParameter(
                    'Temperature',
                    _temperature,
                    0.0,
                    2.0,
                    'Controls randomness. Lower = more focused, Higher = more creative',
                    (value) => setState(() => _temperature = value),
                  ),
                  const SizedBox(height: 16),
                  _buildSliderParameter(
                    'Top P',
                    _topP,
                    0.0,
                    1.0,
                    'Nucleus sampling. Controls diversity of responses',
                    (value) => setState(() => _topP = value),
                  ),
                  const SizedBox(height: 16),
                  _buildSliderParameter(
                    'Frequency Penalty',
                    _frequencyPenalty,
                    0.0,
                    2.0,
                    'Reduces repetition based on frequency',
                    (value) => setState(() => _frequencyPenalty = value),
                  ),
                  const SizedBox(height: 16),
                  _buildSliderParameter(
                    'Presence Penalty',
                    _presencePenalty,
                    0.0,
                    2.0,
                    'Reduces repetition based on presence',
                    (value) => setState(() => _presencePenalty = value),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.restore,
                        color: theme.colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Quick Actions',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: _resetToDefaults,
                          child: const Text('Reset to Defaults'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: OutlinedButton(
                          onPressed: _setCreativePreset,
                          child: const Text('Creative Preset'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: OutlinedButton(
                          onPressed: _setPrecisePreset,
                          child: const Text('Precise Preset'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSliderParameter(
    String label,
    double value,
    double min,
    double max,
    String description,
    void Function(double) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              value.toStringAsFixed(2),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ],
        ),
        Slider(
          value: value,
          min: min,
          max: max,
          divisions: 100,
          onChanged: onChanged,
        ),
        Text(
          description,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  void _resetToDefaults() {
    setState(() {
      _temperature = 0.7;
      _topP = 1.0;
      _frequencyPenalty = 0.0;
      _presencePenalty = 0.0;
      _maxTokensController.text = '';
    });
  }

  void _setCreativePreset() {
    setState(() {
      _temperature = 1.0;
      _topP = 0.9;
      _frequencyPenalty = 0.5;
      _presencePenalty = 0.3;
    });
  }

  void _setPrecisePreset() {
    setState(() {
      _temperature = 0.2;
      _topP = 0.5;
      _frequencyPenalty = 0.0;
      _presencePenalty = 0.0;
    });
  }

  void _saveSettings() async {
    setState(() => _isLoading = true);

    try {
      final settingsNotifier = ref.read(settingsProvider.notifier);
      
      await settingsNotifier.updateSetting('default_model', _defaultModelController.text.trim());
      await settingsNotifier.updateSetting('temperature', _temperature);
      await settingsNotifier.updateSetting('top_p', _topP);
      await settingsNotifier.updateSetting('frequency_penalty', _frequencyPenalty);
      await settingsNotifier.updateSetting('presence_penalty', _presencePenalty);
      
      if (_maxTokensController.text.trim().isNotEmpty) {
        final maxTokens = int.tryParse(_maxTokensController.text.trim());
        if (maxTokens != null) {
          await settingsNotifier.updateSetting('max_tokens', maxTokens);
        }
      } else {
        await settingsNotifier.updateSetting('max_tokens', null);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Model parameters saved successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving parameters: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }
}