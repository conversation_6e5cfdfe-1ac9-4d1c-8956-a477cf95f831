name: otlo
description: "AI Chat Application with MCP Support"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter

  # Core
  flutter_riverpod: ^3.0.0-dev.17
  riverpod_annotation: ^3.0.0-dev.17
  go_router: ^16.1.0
  
  # Database
  drift: ^2.18.0
  sqlite3_flutter_libs: ^0.5.0
  
  # API & Networking  
  dio: ^5.4.0
  http: ^1.2.0
  web_socket_channel: ^3.0.0
  stream_transform: ^2.1.0
  
  # UI
  gpt_markdown: ^1.1.2
  flutter_highlight: ^0.7.0
  
  # Storage
  flutter_secure_storage: ^9.2.0
  shared_preferences: ^2.2.0
  
  # Models
  freezed_annotation: ^3.1.0
  json_annotation: ^4.9.0
  
  # Utilities
  uuid: ^4.4.0
  intl: ^0.20.2
  collection: ^1.18.0
  path_provider: ^2.1.0
  path: ^1.9.0
  
  cupertino_icons: ^1.0.8

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^6.0.0
  
  build_runner: ^2.4.0
  riverpod_generator: ^3.0.0-dev.17
  freezed: ^3.2.0
  json_serializable: ^6.8.0
  drift_dev: ^2.18.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

flutter:
  uses-material-design: true
  
  assets:
    - assets/config/
    - assets/icons/
